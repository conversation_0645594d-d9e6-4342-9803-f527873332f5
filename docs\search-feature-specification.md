# Search Feature Specification

This document provides a comprehensive specification for the search functionality in the ADGitOps UI application. It serves as a reference for both frontend implementation and backend adaptation.

## Table of Contents

- [Overview](#overview)
- [Search Syntax](#search-syntax)
- [Query Operators](#query-operators)
- [Searchable Fields](#searchable-fields)
- [Search Patterns](#search-patterns)
- [Backend Implementation Requirements](#backend-implementation-requirements)
- [Examples](#examples)
- [UI Components](#ui-components)

## Overview

The search feature allows users to find groups and users using a flexible query syntax. It supports exact matching, substring matching, prefix matching, and logical operators to create complex queries. The search is designed to be intuitive while providing powerful filtering capabilities.

## Search Syntax

The search syntax follows these patterns:

| Syntax | Description | Example |
|--------|-------------|---------|
| `column:value` | Exact match within a specific column | `groupname:admins` |
| `column:~value` | Contains match (substring) within a column | `groupname:~admin` |
| `column:value*` | Prefix match (starts with) within a column | `name:j*` |
| `"exact word"` | Match an exact word or phrase in any field | `"security"` |
| Plain text | Simple text search across all fields | `admin` |

### Important Notes:

- All searches are case-insensitive
- By default, column-specific searches (`column:value`) use exact matching
- Use `column:~value` for substring matching
- Use `column:value*` for prefix matching
- Spaces in search values should be enclosed in quotes: `description:"access control"`

## Query Operators

The search supports logical operators to create complex queries:

| Operator | Description | Example |
|----------|-------------|---------|
| `AND` | Both terms must match (default operator) | `type:security AND lob:fm` |
| `OR` | Either term can match | `type:security OR type:admin` |
| `NOT` | Exclude matches with this term | `groupname:~admin NOT lob:fm` |
| `( )` | Grouping expressions | `(type:security OR type:admin) AND lob:fm` |

### Important Notes:

- `AND` is the default operator when no operator is specified between terms
- Operators are case-insensitive: `AND`/`and`, `OR`/`or`, `NOT`/`not` are all valid
- Parentheses can be used to group expressions and control precedence

## Searchable Fields

### Groups

| Field | Description | Example |
|-------|-------------|---------|
| `groupname:` | Group name | `groupname:org` |
| `type:` | Group type | `type:security` |
| `members:` | Group members | `members:john` |
| `lob:` | Line of business | `lob:fm` |
| `description:` | Group description | `description:~access` |

### Users

| Field | Description | Example |
|-------|-------------|---------|
| `name:` | Username | `name:john` |
| `groups:` | Group memberships | `groups:admins` |
| `lob:` | Line of business | `lob:fm` |

## Search Patterns

### Basic Patterns

1. **Exact Match**: Find items where a field exactly matches a value
   ```
   groupname:org
   name:john
   ```

2. **Contains Match**: Find items where a field contains a substring
   ```
   groupname:~admin
   name:~john
   ```

3. **Prefix Match**: Find items where a field starts with a value
   ```
   groupname:adm*
   name:j*
   ```

4. **Exact Word/Phrase**: Find items containing an exact word or phrase
   ```
   "security"
   "access control"
   ```

### Advanced Patterns

1. **AND Queries**: Find items matching multiple criteria
   ```
   type:security AND lob:fm
   name:~john AND groups:~admin
   ```

2. **OR Queries**: Find items matching any of multiple criteria
   ```
   type:security OR type:admin
   name:~john OR groups:~admin
   ```

3. **NOT Queries**: Exclude items matching certain criteria
   ```
   groupname:~admin NOT lob:fm
   groups:~admin NOT lob:fm
   ```

4. **Combined Queries**: Combine multiple operators
   ```
   type:security AND (lob:fm OR lob:it)
   name:~john AND (groups:~admin OR groups:~finance) NOT lob:fm
   ```

## Backend Implementation Requirements

The backend should implement the following functionality to support the search feature:

### Query Parsing

1. Parse the search query to identify:
   - Field-specific searches (`column:value`)
   - Substring searches (`column:~value`)
   - Prefix searches (`column:value*`)
   - Exact word/phrase searches (`"exact phrase"`)
   - Logical operators (`AND`, `OR`, `NOT`)

2. Handle special cases:
   - Empty searches (return all results)
   - Malformed queries (provide appropriate error handling)
   - Escaping special characters

### Query Execution

1. Convert parsed queries to database queries or filtering logic
2. Support case-insensitive matching for all search types
3. Implement exact, substring, and prefix matching for each searchable field
4. Support logical operators for combining search conditions
5. Optimize query execution for performance

### Response Handling

1. Return paginated results
2. Include total count of matching items
3. Provide appropriate error messages for invalid queries
4. Ensure consistent response format for both Groups and Users searches

## Examples

### Group Search Examples

| Query | Description |
|-------|-------------|
| `groupname:org` | Find groups with name exactly "org" |
| `groupname:~org` | Find groups with "org" in their name |
| `type:security` | Find security groups |
| `members:john` | Find groups with member "john" |
| `lob:fm` | Find groups in FM line of business |
| `type:security AND lob:fm` | Find FM security groups |
| `type:security OR type:admin` | Find security or admin groups |
| `groupname:~admin NOT lob:fm` | Find admin groups not in FM |

### User Search Examples

| Query | Description |
|-------|-------------|
| `name:john` | Find users with name exactly "john" |
| `name:~john` | Find users with "john" in their name |
| `groups:admins` | Find users in "admins" group |
| `groups:~finance` | Find users in finance groups |
| `lob:fm` | Find users in FM line of business |
| `lob:fm AND groups:~admin` | Find FM users in admin groups |
| `name:~john OR groups:~admin` | Find users named john or in admin groups |
| `groups:~admin NOT lob:fm` | Find admin users not in FM |

## UI Components

The search feature includes the following UI components:

1. **Search Input**: Text input field for entering search queries
2. **Search Help**: Popup with syntax guide and examples
3. **Search Suggestions**: Dropdown with autocomplete suggestions
4. **Results Display**: Table showing search results
5. **Pagination**: Controls for navigating through results

### Search Input Behavior

- The search input should maintain focus throughout the search process
- Queries should not be automatically executed while typing
- Pressing Enter or clicking the search button triggers the search
- The search query should be preserved in the URL for bookmarking and sharing

### Search Help

The search help popup provides:
- Basic search syntax guide
- Query operators explanation
- List of searchable fields
- Examples of common search patterns

### Search Suggestions

The search suggestions dropdown should:
- Suggest field names (e.g., `groupname:`, `type:`, `lob:`)
- Suggest logical operators (`AND`, `OR`, `NOT`)
- Appear when typing and navigate with arrow keys
- Not trigger searches automatically when selecting a suggestion

### Results Display

The results display should:
- Show matching items in a table format
- Highlight the matching parts of the search results
- Provide clear indication when no results are found
- Display appropriate loading states during search

### Pagination

The pagination controls should:
- Allow navigation between pages of results
- Display the current page and total number of pages
- Allow changing the number of results per page
- Preserve the search query when changing pages
