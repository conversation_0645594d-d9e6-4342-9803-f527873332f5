import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Settings,
  Clock,
  Zap,
  AlertTriangle,
  CheckCircle,
  Plus,
  Database,
  Calendar,
  Activity
} from 'lucide-react';
import { apiClient as api } from '@/api/client';
import { RepositoryAutoSchedulerConfig } from './RepositoryAutoSchedulerConfig';
import { ContinuousSchedulingConfig } from './ContinuousSchedulingConfig';
import { RepositorySelector } from '@/components/repository/RepositorySelector';
import { useRepository } from '@/context/RepositoryContext';

interface AutoScanConfig {
  id: string;
  repoId: string;
  repositoryName: string;
  enabled: boolean;
  timeWindow: {
    startHour: number;
    endHour: number;
    timezone: string;
  };
  minimumIntervalMinutes?: number;
  priorityConfig?: {
    enabled: boolean;
    defaultWeight: number;
    rules: any[];
  };
  createdAt: string;
  updatedAt: string;
}

interface Repository {
  id: string;
  name: string;
  isActive: boolean;
  type: string;
}

export const AutoScanManagement: React.FC = () => {
  const { repositories } = useRepository();
  const [configs, setConfigs] = useState<Record<string, AutoScanConfig>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedRepo, setSelectedRepo] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [showRepositorySelector, setShowRepositorySelector] = useState(false);

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.scheduler.getAutoScanConfigs();
      setConfigs(response.configs || {});
    } catch (err: any) {
      console.error('Error loading auto-scan configs:', err);
      if (err.message?.includes('404') || err.status === 404) {
        setError('Auto-scan configuration endpoints not available. Please ensure the backend is running with the latest version.');
      } else {
        setError(`Failed to load auto-scan configurations: ${err.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleQuickToggle = async (repoId: string, enabled: boolean) => {
    setError(null);
    setSuccess(null);

    try {
      if (enabled) {
        await api.scheduler.enableAutoScan(repoId);
        setSuccess(`Auto-scan enabled for ${getRepositoryName(repoId)}`);
      } else {
        await api.scheduler.disableAutoScan(repoId);
        setSuccess(`Auto-scan disabled for ${getRepositoryName(repoId)}`);
      }
      await loadConfigs();
    } catch (err: any) {
      console.error(`Error ${enabled ? 'enabling' : 'disabling'} auto-scan:`, err);
      if (err.message?.includes('404') || err.status === 404) {
        setError('Auto-scan endpoints not available. Please ensure the backend is running with the latest version.');
      } else {
        setError(err.message || `Failed to ${enabled ? 'enable' : 'disable'} auto-scan`);
      }
    }
  };

  const getRepositoryName = (repoId: string): string => {
    const repo = repositories.find(r => r.id === repoId);
    return repo?.name || repoId;
  };

  const formatTime = (hour: number) => {
    return `${hour.toString().padStart(2, '0')}:00`;
  };

  const formatMinimumInterval = (minutes?: number) => {
    if (!minutes) return '60 minutes';
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${remainingMinutes}m`;
  };

  const openConfigModal = (repoId: string) => {
    setSelectedRepo(repoId);
    setModalOpen(true);
  };

  const closeConfigModal = () => {
    setModalOpen(false);
    setSelectedRepo(null);
  };

  const handleConfigSaved = () => {
    loadConfigs();
    // Clear success/error messages after a delay
    setTimeout(() => {
      setSuccess(null);
      setError(null);
    }, 3000);
  };

  const { selectedRepoId, onRepositoryChange } = useRepository();

  // Handle repository selection changes
  useEffect(() => {
    const unsubscribe = onRepositoryChange((repoId) => {
      if (showRepositorySelector && repoId) {
        setSelectedRepo(repoId);
        setShowRepositorySelector(false);
        setModalOpen(true);
      }
    });

    return unsubscribe;
  }, [showRepositorySelector, onRepositoryChange]);

  const handleAddConfiguration = () => {
    setShowRepositorySelector(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <span>Scan Auto-Scheduler Configuration</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Configure repository-specific settings for the continuous auto-scheduler. Set up time windows, minimum intervals, and priority rules.
              </p>
            </div>
            <Button
              onClick={handleAddConfiguration}
              size="sm"
              className="shrink-0"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Configuration
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Continuous Scheduling Configuration */}
      <ContinuousSchedulingConfig />

      {/* Alerts */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Repository Configuration Cards */}
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
        {repositories.map((repo) => {
          const config = configs[repo.id];
          const hasConfig = !!config;

          return (
            <Card key={repo.id} className={`relative transition-all duration-200 hover:shadow-md ${hasConfig && config.enabled ? 'border-green-200 bg-green-50/30' : ''}`}>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${hasConfig && config.enabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-500'}`}>
                      <Database className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-base font-semibold">
                        {repo.name}
                      </CardTitle>
                      <p className="text-xs text-muted-foreground">
                        Repository ID: {repo.id}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {hasConfig && (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-muted-foreground">Auto-scan</span>
                        <Switch
                          checked={config.enabled}
                          onCheckedChange={(checked) => handleQuickToggle(repo.id, checked)}
                        />
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openConfigModal(repo.id)}
                      className="shrink-0"
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {hasConfig ? (
                  <div className="space-y-4">
                    {/* Configuration Overview */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Status</span>
                          <Badge
                            variant={config.enabled ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {config.enabled ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Time Window</span>
                          <div className="flex items-center space-x-1 text-sm">
                            <Clock className="h-3 w-3" />
                            <span>
                              {formatTime(config.timeWindow.startHour)}-{formatTime(config.timeWindow.endHour)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Min Interval</span>
                          <div className="flex items-center space-x-1 text-sm">
                            <Activity className="h-3 w-3" />
                            <span>{formatMinimumInterval(config.minimumIntervalMinutes)}</span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Priority Rules</span>
                          <Badge
                            variant={config.priorityConfig?.enabled ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {config.priorityConfig?.enabled ? 'Enabled' : 'Disabled'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Configuration Details */}
                    <div className="pt-2 border-t">
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Last updated: {new Date(config.updatedAt).toLocaleDateString()}</span>
                        <span>Managed by auto-scheduler</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                      <Zap className="h-6 w-6 text-gray-400" />
                    </div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      Auto-scan not configured
                    </h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Configure repository settings for the continuous auto-scheduler
                    </p>
                    <Button
                      onClick={() => handleQuickToggle(repo.id, true)}
                      className="w-full"
                      size="sm"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Enable Auto-Scan
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Repository Selection Dialog */}
      {showRepositorySelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full m-4 p-6">
            <h3 className="text-lg font-semibold mb-4">Select Repository</h3>
            <p className="text-sm text-gray-600 mb-4">
              Choose a repository to configure auto-scheduling settings for:
            </p>
            <div className="space-y-4">
              <RepositorySelector />
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowRepositorySelector(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Configuration Modal */}
      <RepositoryAutoSchedulerConfig
        isOpen={modalOpen}
        onClose={closeConfigModal}
        repoId={selectedRepo || undefined}
        repositoryName={selectedRepo ? getRepositoryName(selectedRepo) : undefined}
        onConfigSaved={handleConfigSaved}
      />
    </div>
  );
};
