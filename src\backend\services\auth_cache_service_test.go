package services

import (
	"testing"
	"time"

	"adgitops-ui/src/backend/models"
)

func TestNewAuthCacheService(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	if service == nil {
		t.Fatal("Expected service to be created, got nil")
	}

	if !service.config.Enabled {
		t.<PERSON>rror("Expected service to be enabled")
	}

	if service.config.TTL != 5*time.Minute {
		t.Errorf("Expected TTL to be 5 minutes, got %v", service.config.TTL)
	}

	if service.config.MaxEntries != 100 {
		t.Errorf("Expected MaxEntries to be 100, got %d", service.config.MaxEntries)
	}
}

func TestAuthCacheService_CacheGitAuth(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Test caching successful authentication
	metadata := map[string]interface{}{
		"repoURL": "https://github.com/test/repo.git",
		"branch":  "main",
	}

	service.CacheGitAuth("source1", "https://github.com/test/repo.git", "token", "bearer", true, "", metadata)

	// Test retrieving cached authentication
	entry, found := service.GetGitAuth("source1", "https://github.com/test/repo.git", "token", "bearer")

	if !found {
		t.Fatal("Expected to find cached auth entry")
	}

	if entry == nil {
		t.Fatal("Expected entry to be non-nil")
	}

	if !entry.Success {
		t.Error("Expected cached entry to indicate success")
	}

	if entry.AuthType != "git" {
		t.Errorf("Expected AuthType to be 'git', got %s", entry.AuthType)
	}

	if entry.AuthMethod != "bearer" {
		t.Errorf("Expected AuthMethod to be 'bearer', got %s", entry.AuthMethod)
	}

	if entry.SourceID != "source1" {
		t.Errorf("Expected SourceID to be 'source1', got %s", entry.SourceID)
	}
}

func TestAuthCacheService_CacheAPIAuth(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Test caching API authentication
	metadata := map[string]interface{}{
		"endpoint": "https://api.example.com/v1",
		"method":   "GET",
	}

	service.CacheAPIAuth("api-source1", "https://api.example.com/v1", "bearer", true, "", metadata)

	// Test retrieving cached API authentication
	entry, found := service.GetAPIAuth("api-source1", "https://api.example.com/v1", "bearer")

	if !found {
		t.Fatal("Expected to find cached API auth entry")
	}

	if entry == nil {
		t.Fatal("Expected entry to be non-nil")
	}

	if !entry.Success {
		t.Error("Expected cached entry to indicate success")
	}

	if entry.AuthType != "api" {
		t.Errorf("Expected AuthType to be 'api', got %s", entry.AuthType)
	}

	if entry.AuthMethod != "bearer" {
		t.Errorf("Expected AuthMethod to be 'bearer', got %s", entry.AuthMethod)
	}

	if entry.SourceID != "api-source1" {
		t.Errorf("Expected SourceID to be 'api-source1', got %s", entry.SourceID)
	}
}

func TestAuthCacheService_CacheFailure(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Test caching failed authentication
	errorMsg := "Authentication failed: invalid token"
	service.CacheGitAuth("source1", "https://github.com/test/repo.git", "token", "bearer", false, errorMsg, nil)

	// Test retrieving cached failed authentication
	entry, found := service.GetGitAuth("source1", "https://github.com/test/repo.git", "token", "bearer")

	if !found {
		t.Fatal("Expected to find cached auth entry")
	}

	if entry.Success {
		t.Error("Expected cached entry to indicate failure")
	}

	if entry.ErrorMessage != errorMsg {
		t.Errorf("Expected ErrorMessage to be '%s', got '%s'", errorMsg, entry.ErrorMessage)
	}
}

func TestAuthCacheService_InvalidateAuth(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Cache multiple entries for the same source
	service.CacheGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer", true, "", nil)
	service.CacheGitAuth("source1", "https://github.com/test/repo2.git", "token", "bearer", true, "", nil)
	service.CacheAPIAuth("source1", "https://api.example.com/v1", "bearer", true, "", nil)

	// Verify entries exist
	_, found1 := service.GetGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer")
	_, found2 := service.GetGitAuth("source1", "https://github.com/test/repo2.git", "token", "bearer")
	_, found3 := service.GetAPIAuth("source1", "https://api.example.com/v1", "bearer")

	if !found1 || !found2 || !found3 {
		t.Fatal("Expected all entries to be cached")
	}

	// Invalidate all entries for source1
	service.InvalidateAuth("source1")

	// Verify entries are removed
	_, found1After := service.GetGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer")
	_, found2After := service.GetGitAuth("source1", "https://github.com/test/repo2.git", "token", "bearer")
	_, found3After := service.GetAPIAuth("source1", "https://api.example.com/v1", "bearer")

	if found1After || found2After || found3After {
		t.Error("Expected all entries for source1 to be invalidated")
	}
}

func TestAuthCacheService_GetStats(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Cache some entries
	service.CacheGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer", true, "", nil)
	service.CacheGitAuth("source2", "https://github.com/test/repo2.git", "token", "bearer", true, "", nil)
	service.CacheAPIAuth("source3", "https://api.example.com/v1", "bearer", true, "", nil)

	// Generate some hits and misses
	service.GetGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer") // hit
	service.GetGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer") // hit
	service.GetGitAuth("nonexistent", "https://github.com/test/repo.git", "token", "bearer") // miss

	stats := service.GetStats()

	if stats.TotalEntries != 3 {
		t.Errorf("Expected TotalEntries to be 3, got %d", stats.TotalEntries)
	}

	if stats.HitCount != 2 {
		t.Errorf("Expected HitCount to be 2, got %d", stats.HitCount)
	}

	if stats.MissCount != 1 {
		t.Errorf("Expected MissCount to be 1, got %d", stats.MissCount)
	}

	expectedHitRate := float64(2) / float64(3) * 100
	if stats.HitRate != expectedHitRate {
		t.Errorf("Expected HitRate to be %.2f, got %.2f", expectedHitRate, stats.HitRate)
	}

	if len(stats.TopSources) == 0 {
		t.Error("Expected TopSources to contain entries")
	}
}

func TestAuthCacheService_DisabledCache(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         false,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Try to cache an entry
	service.CacheGitAuth("source1", "https://github.com/test/repo.git", "token", "bearer", true, "", nil)

	// Try to retrieve the entry
	_, found := service.GetGitAuth("source1", "https://github.com/test/repo.git", "token", "bearer")

	if found {
		t.Error("Expected cache to be disabled and entry not found")
	}

	stats := service.GetStats()
	if stats.TotalEntries != 0 {
		t.Errorf("Expected TotalEntries to be 0 for disabled cache, got %d", stats.TotalEntries)
	}
}

func TestAuthCacheService_ClearCache(t *testing.T) {
	config := models.AuthCacheConfig{
		Enabled:         true,
		TTL:             5 * time.Minute,
		MaxEntries:      100,
		CleanupInterval: 1 * time.Minute,
	}

	service := NewAuthCacheService(config)

	// Cache some entries
	service.CacheGitAuth("source1", "https://github.com/test/repo1.git", "token", "bearer", true, "", nil)
	service.CacheAPIAuth("source2", "https://api.example.com/v1", "bearer", true, "", nil)

	// Verify entries exist
	stats := service.GetStats()
	if stats.TotalEntries != 2 {
		t.Errorf("Expected 2 entries before clear, got %d", stats.TotalEntries)
	}

	// Clear cache
	service.ClearCache()

	// Verify cache is empty
	statsAfter := service.GetStats()
	if statsAfter.TotalEntries != 0 {
		t.Errorf("Expected 0 entries after clear, got %d", statsAfter.TotalEntries)
	}

	if statsAfter.HitCount != 0 {
		t.Errorf("Expected HitCount to be 0 after clear, got %d", statsAfter.HitCount)
	}

	if statsAfter.MissCount != 0 {
		t.Errorf("Expected MissCount to be 0 after clear, got %d", statsAfter.MissCount)
	}
}
