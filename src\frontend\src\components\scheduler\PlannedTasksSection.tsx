import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  Calendar,
  Clock,
  Database,
  User,
  ChevronDown,
  ChevronRight,
  RotateCcw,
  Play,
  Pause,
  X,
  Edit,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Timer,
  Settings,
  Zap,
  GripVertical,
  RefreshCw
} from 'lucide-react';

import type { PlannedTask } from '@/types/scheduler';
import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName
} from '@/types/scheduler';


interface PlannedTasksSectionProps {
  tasks: PlannedTask[];
  loading?: boolean;
  onRefresh?: () => void;
  onTriggerTask?: (taskId: string) => void;
  onDelayTask?: (taskId: string, delayMinutes: number) => void;
  onPauseTask?: (taskId: string) => void;
  onCancelTask?: (taskId: string, taskType: string) => void;
  onEditTask?: (taskId: string, updates: Partial<PlannedTask>) => void;
  onReorderTasks?: (taskIds: string[]) => void;
}



const PlannedTasksSection: React.FC<PlannedTasksSectionProps> = ({
  tasks,
  loading = false,
  onRefresh,
  onTriggerTask,
  onDelayTask,
  onPauseTask,
  onCancelTask,
  onEditTask,
  onReorderTasks
}) => {
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [actioningTasks, setActioningTasks] = useState<Set<string>>(new Set());
  const [showDelayDialog, setShowDelayDialog] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [delayMinutes, setDelayMinutes] = useState<number>(30);
  const [draggedTask, setDraggedTask] = useState<string | null>(null);
  const [dragOverTask, setDragOverTask] = useState<string | null>(null);

  const { toast } = useToast();

  const toggleExpanded = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  const handleAction = async (taskId: string, action: () => void | Promise<void>) => {
    setActioningTasks(prev => new Set(prev).add(taskId));
    try {
      await Promise.resolve(action());
      // Don't show generic toast here - specific actions will handle their own toasts
      // via WebSocket events or specific handlers
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to perform task action',
        variant: 'destructive',
      });
    } finally {
      setActioningTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  };

  const handleTrigger = (taskId: string) => {
    if (!onTriggerTask) return;
    handleAction(taskId, () => onTriggerTask(taskId));
  };

  const handleDelay = async () => {
    if (!onDelayTask || !selectedTaskId) return;

    try {
      await onDelayTask(selectedTaskId, delayMinutes);
      setShowDelayDialog(false);
      setSelectedTaskId(null);
      toast({
        title: 'Success',
        description: `Task delayed by ${delayMinutes} minutes`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delay task',
        variant: 'destructive',
      });
    }
  };

  const handlePause = (taskId: string) => {
    if (!onPauseTask) return;
    handleAction(taskId, () => onPauseTask(taskId));
  };

  const handleCancel = (taskId: string, taskType: string) => {
    if (!onCancelTask) return;
    handleAction(taskId, () => onCancelTask(taskId, taskType));
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, taskId: string) => {
    setDraggedTask(taskId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, taskId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverTask(taskId);
  };

  const handleDragLeave = () => {
    setDragOverTask(null);
  };

  const handleDrop = (e: React.DragEvent, targetTaskId: string) => {
    e.preventDefault();

    if (!draggedTask || draggedTask === targetTaskId || !onReorderTasks) {
      setDraggedTask(null);
      setDragOverTask(null);
      return;
    }

    // Find the indices of the dragged and target tasks
    const draggedIndex = tasks.findIndex(task => task.id === draggedTask);
    const targetIndex = tasks.findIndex(task => task.id === targetTaskId);

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedTask(null);
      setDragOverTask(null);
      return;
    }

    // Create new order
    const newTasks = [...tasks];
    const [draggedTaskObj] = newTasks.splice(draggedIndex, 1);
    newTasks.splice(targetIndex, 0, draggedTaskObj);

    // Extract the new order of task IDs
    const newOrder = newTasks.map(task => task.id);
    onReorderTasks(newOrder);

    setDraggedTask(null);
    setDragOverTask(null);

    toast({
      title: 'Success',
      description: 'Task order updated successfully',
    });
  };

  const openDelayDialog = (taskId: string) => {
    setSelectedTaskId(taskId);
    setShowDelayDialog(true);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'paused': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'delayed': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Planned Tasks</CardTitle>
          <CardDescription>
            Scheduled tasks with planning and management capabilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <RotateCcw className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                <span>Planned Group Scans</span>
                <Badge variant="secondary">{tasks?.length || 0} scheduled</Badge>
              </CardTitle>
              <CardDescription>
                Automatically scheduled group scans with drag-and-drop reordering and management controls
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  {loading ? 'Refreshing...' : 'Refresh'}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
              {(tasks?.length || 0) === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Scheduled Group Scans</h3>
              <p className="text-gray-500 mb-4">
                No group scans are currently scheduled. The scheduler will automatically create tasks based on your configuration.
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Settings className="h-4 w-4" />
                <span>Configure auto-scan settings to schedule group scans</span>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {(tasks || []).map((task) => {
                const isExpanded = expandedTasks.has(task.id);
                const isActioning = actioningTasks.has(task.id);
                const scheduledTime = new Date(task.scheduledFor);
                const isOverdue = scheduledTime < new Date();

                return (
                  <div
                    key={task.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, task.id)}
                    onDragOver={(e) => handleDragOver(e, task.id)}
                    onDragLeave={handleDragLeave}
                    onDrop={(e) => handleDrop(e, task.id)}
                    className={`border rounded-lg p-4 transition-all duration-200 cursor-move ${
                      isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                    } ${
                      draggedTask === task.id ? 'opacity-50 scale-95' : ''
                    } ${
                      dragOverTask === task.id ? 'border-blue-400 bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab active:cursor-grabbing" />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(task.id)}
                          className="p-1 h-auto"
                        >
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>

                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium">{task.name}</h4>
                            <Badge className={`${TaskTypeColors[task.type]} border-0`}>
                              {getTaskTypeDisplayName(task.type)}
                            </Badge>
                            <Badge className={getPriorityColor(task.priority || 'medium')}>
                              {(task.priority || 'medium').toUpperCase()}
                            </Badge>
                            <Badge className={getStatusColor('scheduled')}>
                              SCHEDULED
                            </Badge>
                            {isOverdue && (
                              <Badge variant="destructive">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                OVERDUE
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-1">
                              <Database className="h-3 w-3" />
                              <span>{task.repository}</span>
                            </div>
                            {task.groupName && (
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                            )}
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>
                                {isOverdue ? 'Overdue by ' : 'Scheduled '}
                                {formatRelativeTime(task.scheduledFor)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {onTriggerTask && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTrigger(task.id)}
                            disabled={isActioning}
                            className="text-green-600 border-green-200 hover:bg-green-50"
                          >
                            <Play className="h-4 w-4 mr-1" />
                            Trigger Now
                          </Button>
                        )}

                        {onDelayTask && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDelayDialog(task.id)}
                            disabled={isActioning}
                            className="text-purple-600 border-purple-200 hover:bg-purple-50"
                          >
                            <Timer className="h-4 w-4 mr-1" />
                            Delay
                          </Button>
                        )}

                        {onPauseTask && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePause(task.id)}
                            disabled={isActioning}
                            className="text-orange-600 border-orange-200 hover:bg-orange-50"
                          >
                            <Pause className="h-4 w-4 mr-1" />
                            Pause
                          </Button>
                        )}

                        {onCancelTask && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCancel(task.id, task.type)}
                            disabled={isActioning}
                            className="text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <X className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                        )}
                      </div>
                    </div>

                    {isExpanded && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-medium mb-2">Task Details</h5>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="font-medium">ID:</span> {task.id}
                              </div>
                              <div>
                                <span className="font-medium">Scheduled:</span> {formatRelativeTime(task.scheduledFor)}
                              </div>
                              <div>
                                <span className="font-medium">Scheduled:</span> {scheduledTime.toLocaleString()}
                              </div>
                              {task.description && (
                                <div>
                                  <span className="font-medium">Description:</span>
                                  <p className="mt-1 text-gray-600">{task.description}</p>
                                </div>
                              )}
                            </div>
                          </div>

                          <div>
                            <h5 className="font-medium mb-2">Configuration</h5>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="font-medium">Type:</span> {getTaskTypeDisplayName(task.type)}
                              </div>
                              <div>
                                <span className="font-medium">Repository:</span> {task.repository}
                              </div>
                              {task.groupName && (
                                <div>
                                  <span className="font-medium">Group:</span> {task.groupName}
                                </div>
                              )}
                              <div>
                                <span className="font-medium">Priority:</span> {(task.priority || 'medium').toUpperCase()}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Error information would be shown here if available */}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delay Task Dialog */}
      <Dialog open={showDelayDialog} onOpenChange={setShowDelayDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Delay Task</DialogTitle>
            <DialogDescription>
              Specify how many minutes to delay the selected task.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="delayMinutes" className="text-right">Minutes</Label>
              <Input
                id="delayMinutes"
                type="number"
                value={delayMinutes}
                onChange={(e) => setDelayMinutes(parseInt(e.target.value) || 0)}
                className="col-span-3"
                min="1"
                max="1440"
                placeholder="30"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDelayDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleDelay} disabled={delayMinutes <= 0}>
              Delay Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PlannedTasksSection;
