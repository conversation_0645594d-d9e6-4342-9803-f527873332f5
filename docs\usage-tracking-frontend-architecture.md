# Group Usage Tracking - Frontend Architecture

## Overview

This document outlines the frontend architecture for the Group Usage Tracking feature, including integration with existing Settings page and Group detail tabs.

## Existing Frontend Patterns

### Component Structure

The application follows these patterns:

1. **Page Components**: Main page components in `src/frontend/src/pages/`
2. **Reusable Components**: Shared components in `src/frontend/src/components/ui/`
3. **API Client**: Centralized API client in `src/frontend/src/api/client.ts`
4. **TypeScript Interfaces**: Type definitions for data models
5. **shadcn/ui Components**: Consistent UI component library

### Settings Page Structure

Current settings page (`src/frontend/src/pages/Settings.tsx`):

```tsx
<Tabs defaultValue="general">
  <TabsList>
    <TabsTrigger value="general">General</TabsTrigger>
    <TabsTrigger value="repository">Repository</TabsTrigger>
  </TabsList>
  
  <TabsContent value="general">
    <GeneralSettings />
  </TabsContent>
  
  <TabsContent value="repository">
    <RepositorySettings />
  </TabsContent>
</Tabs>
```

### Group Detail Page Structure

Current group detail page has tabs for:
- Direct Members
- Resolved Members  
- Group Tree

## New Frontend Components

### Settings Page Integration

#### 1. UsageSourcesTab

```tsx
// src/frontend/src/pages/Settings/UsageSourcesTab.tsx
interface UsageSourcesTabProps {
  // No props needed - self-contained
}

const UsageSourcesTab: React.FC<UsageSourcesTabProps> = () => {
  return (
    <div className="space-y-6">
      <UsageSourcesHeader />
      <UsageSourcesList />
      <UsageSourcesStats />
    </div>
  );
};
```

#### 2. UsageSourcesHeader

```tsx
// src/frontend/src/pages/Settings/UsageSourcesHeader.tsx
const UsageSourcesHeader: React.FC = () => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <h3 className="text-lg font-medium">Usage Sources</h3>
        <p className="text-sm text-muted-foreground">
          Configure external sources to scan for group usage
        </p>
      </div>
      <Button onClick={handleAddSource}>
        <Plus className="h-4 w-4 mr-2" />
        Add Source
      </Button>
    </div>
  );
};
```

#### 3. UsageSourcesList

```tsx
// src/frontend/src/pages/Settings/UsageSourcesList.tsx
interface UsageSourcesListProps {
  sources: UsageSource[];
  loading: boolean;
  onEdit: (source: UsageSource) => void;
  onDelete: (sourceId: string) => void;
  onTest: (sourceId: string) => void;
  onToggleActive: (sourceId: string, active: boolean) => void;
}

const UsageSourcesList: React.FC<UsageSourcesListProps> = ({
  sources,
  loading,
  onEdit,
  onDelete,
  onTest,
  onToggleActive
}) => {
  return (
    <div className="space-y-4">
      {sources.map(source => (
        <UsageSourceCard
          key={source.id}
          source={source}
          onEdit={onEdit}
          onDelete={onDelete}
          onTest={onTest}
          onToggleActive={onToggleActive}
        />
      ))}
    </div>
  );
};
```

#### 4. UsageSourceCard

```tsx
// src/frontend/src/pages/Settings/UsageSourceCard.tsx
interface UsageSourceCardProps {
  source: UsageSource;
  onEdit: (source: UsageSource) => void;
  onDelete: (sourceId: string) => void;
  onTest: (sourceId: string) => void;
  onToggleActive: (sourceId: string, active: boolean) => void;
}

const UsageSourceCard: React.FC<UsageSourceCardProps> = ({
  source,
  onEdit,
  onDelete,
  onTest,
  onToggleActive
}) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          <SourceTypeIcon type={source.type} />
          <div>
            <CardTitle className="text-base">{source.name}</CardTitle>
            <CardDescription>{source.type} source</CardDescription>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            checked={source.isActive}
            onCheckedChange={(checked) => onToggleActive(source.id, checked)}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onTest(source.id)}>
                Test Connection
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit(source)}>
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(source.id)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <UsageSourceDetails source={source} />
      </CardContent>
    </Card>
  );
};
```

#### 5. UsageSourceForm

```tsx
// src/frontend/src/pages/Settings/UsageSourceForm.tsx
interface UsageSourceFormProps {
  source?: UsageSource; // undefined for new source
  onSave: (source: UsageSource) => void;
  onCancel: () => void;
}

const UsageSourceForm: React.FC<UsageSourceFormProps> = ({
  source,
  onSave,
  onCancel
}) => {
  const [sourceType, setSourceType] = useState<SourceType>(source?.type || 'git');
  
  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {source ? 'Edit Usage Source' : 'Add Usage Source'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <SourceTypeSelector
              value={sourceType}
              onChange={setSourceType}
            />
            
            <DynamicSourceConfigForm
              sourceType={sourceType}
              initialConfig={source}
              onChange={handleConfigChange}
            />
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">
              {source ? 'Update' : 'Create'} Source
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
```

### Group Detail Page Integration

#### 1. GroupUsageTab

```tsx
// src/frontend/src/pages/Groups/GroupUsageTab.tsx
interface GroupUsageTabProps {
  group: Group;
  repoId: string;
}

const GroupUsageTab: React.FC<GroupUsageTabProps> = ({ group, repoId }) => {
  return (
    <div className="space-y-6">
      <GroupUsageHeader group={group} repoId={repoId} />
      <GroupUsageContent group={group} repoId={repoId} />
    </div>
  );
};
```

#### 2. GroupUsageHeader

```tsx
// src/frontend/src/pages/Groups/GroupUsageHeader.tsx
interface GroupUsageHeaderProps {
  group: Group;
  repoId: string;
}

const GroupUsageHeader: React.FC<GroupUsageHeaderProps> = ({ group, repoId }) => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <h3 className="text-lg font-medium">Usage Tracking</h3>
        <p className="text-sm text-muted-foreground">
          External references to {group.Groupname}
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <UsageScanStatus groupName={group.Groupname} repoId={repoId} />
        <Button onClick={handleManualScan} disabled={isScanning}>
          {isScanning ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Search className="h-4 w-4 mr-2" />
          )}
          Scan Now
        </Button>
      </div>
    </div>
  );
};
```

#### 3. GroupUsageContent

```tsx
// src/frontend/src/pages/Groups/GroupUsageContent.tsx
interface GroupUsageContentProps {
  group: Group;
  repoId: string;
}

const GroupUsageContent: React.FC<GroupUsageContentProps> = ({ group, repoId }) => {
  const [usageResults, setUsageResults] = useState<UsageResultList | null>(null);
  const [loading, setLoading] = useState(true);
  
  return (
    <div className="space-y-4">
      {loading ? (
        <UsageResultsSkeleton />
      ) : usageResults?.results.length ? (
        <UsageResultsList results={usageResults} />
      ) : (
        <UsageResultsEmpty onScan={handleManualScan} />
      )}
    </div>
  );
};
```

#### 4. UsageResultsList

```tsx
// src/frontend/src/pages/Groups/UsageResultsList.tsx
interface UsageResultsListProps {
  results: UsageResultList;
}

const UsageResultsList: React.FC<UsageResultsListProps> = ({ results }) => {
  const groupedResults = groupResultsBySource(results.results);
  
  return (
    <div className="space-y-4">
      {Object.entries(groupedResults).map(([sourceId, sourceResults]) => (
        <UsageSourceSection
          key={sourceId}
          sourceId={sourceId}
          results={sourceResults}
        />
      ))}
    </div>
  );
};
```

#### 5. UsageSourceSection

```tsx
// src/frontend/src/pages/Groups/UsageSourceSection.tsx
interface UsageSourceSectionProps {
  sourceId: string;
  results: UsageResult[];
}

const UsageSourceSection: React.FC<UsageSourceSectionProps> = ({
  sourceId,
  results
}) => {
  const [expanded, setExpanded] = useState(false);
  
  return (
    <Card>
      <CardHeader
        className="cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <SourceTypeIcon type={results[0].sourceType} />
            <div>
              <CardTitle className="text-base">{results[0].sourceName}</CardTitle>
              <CardDescription>
                {results.length} usage{results.length !== 1 ? 's' : ''} found
              </CardDescription>
            </div>
          </div>
          <ChevronDown
            className={`h-4 w-4 transition-transform ${
              expanded ? 'transform rotate-180' : ''
            }`}
          />
        </div>
      </CardHeader>
      
      {expanded && (
        <CardContent>
          <div className="space-y-2">
            {results.map(result => (
              <UsageResultItem key={result.id} result={result} />
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};
```

## TypeScript Interfaces

```typescript
// src/frontend/src/types/usage.ts
export interface UsageSource {
  id: string;
  name: string;
  type: SourceType;
  isActive: boolean;
  scanFrequency: number;
  createdAt: string;
  updatedAt: string;
  includePatterns?: string[];
  excludePatterns?: string[];
  gitConfig?: GitSourceConfig;
  apiConfig?: ApiSourceConfig;
  fileConfig?: FileSourceConfig;
}

export type SourceType = 'git' | 'api' | 'file';

export interface UsageResult {
  id: string;
  groupName: string;
  sourceId: string;
  sourceName: string;
  sourceType: SourceType;
  filePath: string;
  lineNumber: number;
  context: string;
  matchType: string;
  detectedAt: string;
  repoId: string;
  fileSize?: number;
  fileType?: string;
  commitHash?: string;
  branch?: string;
  apiResponse?: string;
}

export interface UsageScanStatus {
  groupName: string;
  sourcesTotal: number;
  sourcesScanned: number;
  inProgress: boolean;
  lastScanTime: string;
  completedSources: string[];
  pendingSources: string[];
  failedSources: SourceScanFailure[];
  repoId: string;
  totalUsages: number;
  scanDuration: number;
}
```

## API Client Extensions

```typescript
// src/frontend/src/api/client.ts - Usage Sources
export const usageSources = {
  getAll: (params?: { type?: SourceType; active?: boolean; page?: number; pageSize?: number }) =>
    apiRequest<{ sources: UsageSource[]; total: number; page: number; pageSize: number }>('/usage-sources', { params }),
  
  getById: (id: string) =>
    apiRequest<UsageSource>(`/usage-sources/${id}`),
  
  create: (source: Omit<UsageSource, 'id' | 'createdAt' | 'updatedAt'>) =>
    apiRequest<UsageSource>('/usage-sources', { method: 'POST', data: source }),
  
  update: (id: string, source: UsageSource) =>
    apiRequest<UsageSource>(`/usage-sources/${id}`, { method: 'PUT', data: source }),
  
  delete: (id: string) =>
    apiRequest<{ success: boolean; message: string }>(`/usage-sources/${id}`, { method: 'DELETE' }),
  
  test: (id: string) =>
    apiRequest<{ success: boolean; status: SourceStatus }>(`/usage-sources/${id}/test`, { method: 'POST' }),
};

// Usage Results
export const usageResults = {
  getForGroup: (repoId: string, groupName: string, params?: { page?: number; pageSize?: number; sourceType?: SourceType; sourceId?: string }) =>
    apiRequest<UsageResultList>(`/repo/${repoId}/groups/${groupName}/usage`, { params }),
  
  scanGroup: (repoId: string, groupName: string, request: UsageScanRequest) =>
    apiRequest<{ success: boolean; message: string; scanId: string }>(`/repo/${repoId}/groups/${groupName}/scan`, { method: 'POST', data: request }),
  
  getScanStatus: (repoId: string, groupName: string) =>
    apiRequest<UsageScanStatus>(`/repo/${repoId}/groups/${groupName}/scan-status`),
  
  cancelScan: (repoId: string, groupName: string) =>
    apiRequest<{ success: boolean; message: string }>(`/repo/${repoId}/groups/${groupName}/scan`, { method: 'DELETE' }),
  
  clearResults: (repoId: string, groupName: string) =>
    apiRequest<{ success: boolean; message: string }>(`/repo/${repoId}/groups/${groupName}/usage`, { method: 'DELETE' }),
};
```

## Integration Points

### Settings Page

Add new tab to existing Settings component:

```tsx
// src/frontend/src/pages/Settings.tsx
<TabsList>
  <TabsTrigger value="general">General</TabsTrigger>
  <TabsTrigger value="repository">Repository</TabsTrigger>
  <TabsTrigger value="usage-sources">Usage Sources</TabsTrigger>
</TabsList>

<TabsContent value="usage-sources">
  <UsageSourcesTab />
</TabsContent>
```

### Group Detail Page

Add new tab to existing GroupMembershipInfo component:

```tsx
// src/frontend/src/pages/Groups/GroupMembershipInfo.tsx
const [activeTab, setActiveTab] = useState<'direct' | 'resolved' | 'tree' | 'usage'>('direct');

<button
  className={`px-4 py-2 text-sm font-medium ${
    activeTab === 'usage'
      ? 'border-b-2 border-blue-500 text-blue-600'
      : 'text-gray-500 hover:text-gray-700'
  }`}
  onClick={() => setActiveTab('usage')}
>
  Usage
</button>

{activeTab === 'usage' && <GroupUsageTab group={group} repoId={repoId} />}
```
