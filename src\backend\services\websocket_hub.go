package services

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/gorilla/websocket"
)

// WebSocketHub manages WebSocket connections and broadcasts progress updates
type WebSocketHub struct {
	// Registered clients
	clients map[*WebSocketClient]bool

	// Inbound messages from clients
	broadcast chan []byte

	// Register requests from clients
	register chan *WebSocketClient

	// Unregister requests from clients
	unregister chan *WebSocketClient

	// Mutex for thread-safe operations
	mutex sync.RWMutex

	// Upgrader for HTTP to WebSocket connections
	upgrader websocket.Upgrader
}

// WebSocketClient represents a WebSocket client connection
type WebSocketClient struct {
	// The WebSocket connection
	conn *websocket.Conn

	// Buffered channel of outbound messages
	send chan []byte

	// Client identifier (could be user ID, session ID, etc.)
	clientID string

	// Subscribed scan IDs for filtering updates
	subscribedScans map[string]bool

	// Admin-level subscriptions
	isAdminSubscriber   bool            // Whether client is subscribed to all scans
	subscribedRepos     map[string]bool // Repository-level subscriptions
	subscribedGroups    map[string]bool // Group-level subscriptions
	subscribedScanTypes map[string]bool // Scan type subscriptions (manual, auto, etc.)

	// Mutex for thread-safe operations
	mutex sync.RWMutex

	// Hub reference
	hub *WebSocketHub
}

// NewWebSocketHub creates a new WebSocket hub
func NewWebSocketHub() *WebSocketHub {
	return &WebSocketHub{
		clients:    make(map[*WebSocketClient]bool),
		broadcast:  make(chan []byte),
		register:   make(chan *WebSocketClient),
		unregister: make(chan *WebSocketClient),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin in development
				// In production, implement proper origin checking
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}
}

// Run starts the WebSocket hub
func (h *WebSocketHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
			}
			h.mutex.Unlock()

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					delete(h.clients, client)
					close(client.send)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// HandleWebSocket handles WebSocket connection requests
func (h *WebSocketHub) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	clientID := r.URL.Query().Get("clientId")
	if clientID == "" {
		clientID = generateClientID()
	}

	client := &WebSocketClient{
		conn:                conn,
		send:                make(chan []byte, 256),
		clientID:            clientID,
		subscribedScans:     make(map[string]bool),
		isAdminSubscriber:   false,
		subscribedRepos:     make(map[string]bool),
		subscribedGroups:    make(map[string]bool),
		subscribedScanTypes: make(map[string]bool),
		hub:                 h,
	}

	log.Printf("Registering WebSocket client: %s", clientID)
	h.register <- client

	// Start goroutines for reading and writing
	go client.writePump()
	go client.readPump()
}

// BroadcastProgressUpdate broadcasts a progress update to all connected clients
func (h *WebSocketHub) BroadcastProgressUpdate(update models.RealTimeProgressUpdate) {
	data, err := json.Marshal(update)
	if err != nil {
		log.Printf("Error marshaling progress update: %v", err)
		return
	}

	h.broadcast <- data
}

// BroadcastToScanSubscribers broadcasts a message to clients subscribed to a specific scan
func (h *WebSocketHub) BroadcastToScanSubscribers(scanID string, update models.RealTimeProgressUpdate) {
	data, err := json.Marshal(update)
	if err != nil {
		log.Printf("Error marshaling progress update: %v", err)
		return
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	subscriberCount := 0
	for client := range h.clients {
		client.mutex.RLock()
		subscribed := client.subscribedScans[scanID]
		client.mutex.RUnlock()

		if subscribed {
			subscriberCount++
			select {
			case client.send <- data:
				// Successfully sent
			default:
				log.Printf("Warning: Failed to send to client %s, removing", client.clientID)
				delete(h.clients, client)
				close(client.send)
			}
		}
	}

}

// BroadcastToAdminSubscribers broadcasts a message to all admin subscribers
func (h *WebSocketHub) BroadcastToAdminSubscribers(update models.RealTimeProgressUpdate) {
	data, err := json.Marshal(update)
	if err != nil {
		log.Printf("Error marshaling admin progress update: %v", err)
		return
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	adminCount := 0
	for client := range h.clients {
		client.mutex.RLock()
		isAdmin := client.isAdminSubscriber
		client.mutex.RUnlock()

		if isAdmin {
			adminCount++
			select {
			case client.send <- data:
				// Successfully sent
			default:
				log.Printf("Warning: Failed to send to admin client %s, removing", client.clientID)
				delete(h.clients, client)
				close(client.send)
			}
		}
	}

}

// BroadcastToRepoSubscribers broadcasts a message to clients subscribed to a specific repository
func (h *WebSocketHub) BroadcastToRepoSubscribers(repoID string, update models.RealTimeProgressUpdate) {
	data, err := json.Marshal(update)
	if err != nil {
		log.Printf("Error marshaling repo progress update: %v", err)
		return
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		client.mutex.RLock()
		subscribed := client.subscribedRepos[repoID] || client.isAdminSubscriber
		client.mutex.RUnlock()

		if subscribed {
			select {
			case client.send <- data:
			default:
				delete(h.clients, client)
				close(client.send)
			}
		}
	}
}

// BroadcastToGroupSubscribers broadcasts a message to clients subscribed to a specific group
func (h *WebSocketHub) BroadcastToGroupSubscribers(groupName string, update models.RealTimeProgressUpdate) {
	data, err := json.Marshal(update)
	if err != nil {
		log.Printf("Error marshaling group progress update: %v", err)
		return
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		client.mutex.RLock()
		subscribed := client.subscribedGroups[groupName] || client.isAdminSubscriber
		client.mutex.RUnlock()

		if subscribed {
			select {
			case client.send <- data:
			default:
				delete(h.clients, client)
				close(client.send)
			}
		}
	}
}

// GetConnectedClientsCount returns the number of connected clients
func (h *WebSocketHub) GetConnectedClientsCount() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.clients)
}

// GetAdminSubscribersCount returns the number of admin subscribers
func (h *WebSocketHub) GetAdminSubscribersCount() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	count := 0
	for client := range h.clients {
		client.mutex.RLock()
		if client.isAdminSubscriber {
			count++
		}
		client.mutex.RUnlock()
	}
	return count
}

// readPump pumps messages from the WebSocket connection to the hub
func (c *WebSocketClient) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(512)
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// Handle client messages (e.g., subscription requests)
		c.handleMessage(message)
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// Send the current message as a separate JSON message
			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				return
			}

			// Send any additional queued messages as separate JSON messages
			// This ensures each message is valid JSON instead of concatenating them
			n := len(c.send)
			for i := 0; i < n; i++ {
				queuedMessage := <-c.send
				c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
				if err := c.conn.WriteMessage(websocket.TextMessage, queuedMessage); err != nil {
					return
				}
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage handles incoming messages from the client
func (c *WebSocketClient) handleMessage(message []byte) {
	var msg map[string]interface{}
	if err := json.Unmarshal(message, &msg); err != nil {
		log.Printf("Error parsing client message: %v", err)
		return
	}

	msgType, ok := msg["type"].(string)
	if !ok {
		return
	}

	switch msgType {
	case "subscribe_scan":
		if scanID, ok := msg["scanId"].(string); ok {
			c.mutex.Lock()
			c.subscribedScans[scanID] = true
			c.mutex.Unlock()
		}

	case "unsubscribe_scan":
		if scanID, ok := msg["scanId"].(string); ok {
			c.mutex.Lock()
			delete(c.subscribedScans, scanID)
			c.mutex.Unlock()
		}

	case "subscribe_admin":
		c.mutex.Lock()
		c.isAdminSubscriber = true
		c.mutex.Unlock()

	case "unsubscribe_admin":
		c.mutex.Lock()
		c.isAdminSubscriber = false
		c.mutex.Unlock()

	case "subscribe_repo":
		if repoID, ok := msg["repoId"].(string); ok {
			c.mutex.Lock()
			c.subscribedRepos[repoID] = true
			c.mutex.Unlock()
		}

	case "unsubscribe_repo":
		if repoID, ok := msg["repoId"].(string); ok {
			c.mutex.Lock()
			delete(c.subscribedRepos, repoID)
			c.mutex.Unlock()
		}

	case "subscribe_group":
		if groupName, ok := msg["groupName"].(string); ok {
			c.mutex.Lock()
			c.subscribedGroups[groupName] = true
			c.mutex.Unlock()
			log.Printf("Client %s subscribed to group %s", c.clientID, groupName)
		}

	case "unsubscribe_group":
		if groupName, ok := msg["groupName"].(string); ok {
			c.mutex.Lock()
			delete(c.subscribedGroups, groupName)
			c.mutex.Unlock()
			log.Printf("Client %s unsubscribed from group %s", c.clientID, groupName)
		}

	case "subscribe_scan_type":
		if scanType, ok := msg["scanType"].(string); ok {
			c.mutex.Lock()
			c.subscribedScanTypes[scanType] = true
			c.mutex.Unlock()
			log.Printf("Client %s subscribed to scan type %s", c.clientID, scanType)
		}

	case "unsubscribe_scan_type":
		if scanType, ok := msg["scanType"].(string); ok {
			c.mutex.Lock()
			delete(c.subscribedScanTypes, scanType)
			c.mutex.Unlock()
			log.Printf("Client %s unsubscribed from scan type %s", c.clientID, scanType)
		}
	}
}

// generateClientID generates a unique client ID
func generateClientID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString generates a random string of specified length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
