import { AlertCircle, RefreshCw } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface ErrorStateProps {
  title?: string
  error: string
  onRetry: () => void
  loading?: boolean
  refreshing?: boolean
}

export const ErrorState = ({ 
  title = "Scheduler Dashboard", 
  error, 
  onRetry, 
  loading = false, 
  refreshing = false 
}: ErrorStateProps) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{title}</h1>
      </div>
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span>{error}</span>
          </div>
          <Button
            onClick={onRetry}
            className="mt-4"
            disabled={loading || refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
            {(loading || refreshing) ? 'Retrying...' : 'Retry'}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
