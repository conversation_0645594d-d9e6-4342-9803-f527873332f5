import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  CheckCircle,
  Clock,
  FileText,
  Search,
  RefreshCw,
  Calendar,
  Timer,
  Database,
  ExternalLink
} from 'lucide-react';
import api from '@/api/client';
import type { CompletedTask } from '@/types/scheduler';

interface CompletedTasksSectionProps {
  loading?: boolean;
  onRefresh?: () => void;
}

const CompletedTasksSection: React.FC<CompletedTasksSectionProps> = ({
  loading = false,
  onRefresh
}) => {
  const [tasks, setTasks] = useState<CompletedTask[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');

  const loadCompletedTasks = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await api.scheduler.getCompletedTasks();
      setTasks(response.tasks || []);
    } catch (err: any) {
      console.error('Failed to load completed tasks:', err);
      setError(err.message || 'Failed to load completed tasks');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadCompletedTasks();
  }, []);

  const handleRefresh = () => {
    loadCompletedTasks();
    onRefresh?.();
  };

  const formatDuration = (durationMs: number) => {
    if (durationMs < 1000) {
      return `${durationMs}ms`;
    }
    const seconds = Math.floor(durationMs / 1000);
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch {
      return dateString;
    }
  };

  const getTaskTypeIcon = (type: string) => {
    switch (type) {
      case 'scan':
        return <Search className="w-4 h-4" />;
      case 'report':
        return <FileText className="w-4 h-4" />;
      case 'auto-scan':
        return <RefreshCw className="w-4 h-4" />;
      default:
        return <CheckCircle className="w-4 h-4" />;
    }
  };

  const getTaskTypeColor = (type: string) => {
    switch (type) {
      case 'scan':
        return 'text-blue-600 bg-blue-50';
      case 'report':
        return 'text-green-600 bg-green-50';
      case 'auto-scan':
        return 'text-purple-600 bg-purple-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Filter tasks based on search term and type
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = searchTerm === '' ||
      task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.groupName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.repository?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = selectedType === 'all' || task.type === selectedType;

    return matchesSearch && matchesType;
  });

  const taskTypes = ['all', ...Array.from(new Set(tasks.map(task => task.type)))];

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 text-red-700">
          <div className="text-sm font-medium">Error loading completed tasks</div>
        </div>
        <div className="text-red-600 text-sm mt-1">{error}</div>
        <button
          onClick={handleRefresh}
          className="mt-2 text-red-700 hover:text-red-800 text-sm underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Completed Tasks ({filteredTasks.length})
          </h3>
        </div>

        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Type filter */}
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {taskTypes.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>

          {/* Refresh button */}
          <button
            onClick={handleRefresh}
            disabled={isLoading || loading}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            <RefreshCw className={`w-4 h-4 ${(isLoading || loading) ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Tasks list */}
      {isLoading || loading ? (
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading completed tasks...</span>
        </div>
      ) : filteredTasks.length === 0 ? (
        <div className="text-center py-8">
          <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            {tasks.length === 0 ? 'No completed tasks' : 'No tasks match your filters'}
          </h4>
          <p className="text-gray-600">
            {tasks.length === 0
              ? 'Completed tasks will appear here once they finish successfully.'
              : 'Try adjusting your search or filter criteria.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredTasks.map((task) => (
            <div
              key={task.id}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getTaskTypeColor(task.type)}`}>
                      {getTaskTypeIcon(task.type)}
                      <span>{task.type}</span>
                    </div>
                    <h4 className="font-medium text-gray-900">{task.name}</h4>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Completed: {formatDateTime(task.completedAt)}</span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Timer className="w-4 h-4" />
                      <span>Duration: {formatDuration(task.duration)}</span>
                    </div>

                    {task.groupName && (
                      <div className="flex items-center space-x-2">
                        <Database className="w-4 h-4" />
                        <span>Group: </span>
                        <Link
                          to={`/groups?search=${encodeURIComponent(`groupname:"${task.groupName}"`)}&page=1`}
                          className="text-blue-600 hover:text-blue-800 hover:underline font-medium inline-flex items-center space-x-1"
                        >
                          <span>{task.groupName}</span>
                          <ExternalLink className="w-3 h-3" />
                        </Link>
                      </div>
                    )}

                    {task.repository && (
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4" />
                        <span>Repo: {task.repository}</span>
                      </div>
                    )}
                  </div>

                  {/* Scan Results Summary */}
                  {(task.resultsCount !== undefined || task.filesScanned !== undefined ||
                    task.sourcesScanned !== undefined || task.errorCount !== undefined) && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                        {task.resultsCount !== undefined && (
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-gray-600">Results:</span>
                            <span className="font-medium text-green-700">{task.resultsCount}</span>
                          </div>
                        )}

                        {task.filesScanned !== undefined && (
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span className="text-gray-600">Files:</span>
                            <span className="font-medium text-blue-700">{task.filesScanned}</span>
                          </div>
                        )}

                        {(task.sourcesScanned !== undefined && task.totalSources !== undefined) && (
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                            <span className="text-gray-600">Sources:</span>
                            <span className="font-medium text-purple-700">
                              {task.sourcesScanned}/{task.totalSources}
                            </span>
                          </div>
                        )}

                        {(task.errorCount !== undefined || task.warningCount !== undefined) && (
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              (task.errorCount || 0) > 0 ? 'bg-red-500' : 'bg-yellow-500'
                            }`}></div>
                            <span className="text-gray-600">Issues:</span>
                            <span className={`font-medium ${
                              (task.errorCount || 0) > 0 ? 'text-red-700' : 'text-yellow-700'
                            }`}>
                              {(task.errorCount || 0) > 0 && `${task.errorCount} errors`}
                              {(task.errorCount || 0) > 0 && (task.warningCount || 0) > 0 && ', '}
                              {(task.warningCount || 0) > 0 && `${task.warningCount} warnings`}
                              {(task.errorCount || 0) === 0 && (task.warningCount || 0) === 0 && 'None'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  <span className="text-sm font-medium">Completed</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CompletedTasksSection;
