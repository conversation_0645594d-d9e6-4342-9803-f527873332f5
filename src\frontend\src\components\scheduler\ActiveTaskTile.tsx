import React, { useMemo } from 'react'
import { ActiveTask, TaskTypeColors } from '@/types/scheduler'
import { Progress as ProgressType } from '@/types/scanLogs'
import { useTaskSpecificWebSocket } from '@/hooks/useTaskSpecificWebSocket'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Pause,
  Play,
  RotateCcw,
  X,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react'

interface ActiveTaskTileProps {
  task: ActiveTask
  onCancel?: (taskId: string) => void
  onPause?: (taskId: string) => void
  onResume?: (taskId: string) => void
  onRetry?: (taskId: string) => void
  onTaskCompleted?: (taskId: string) => void
  onTaskFailed?: (taskId: string, error: string) => void
  onTaskCancelled?: (taskId: string) => void
  isActioning?: boolean
}

export const ActiveTaskTile: React.FC<ActiveTaskTileProps> = ({
  task,
  onCancel,
  onPause,
  onResume,
  onRetry,
  onTaskCompleted,
  onTaskFailed,
  onTaskCancelled,
  isActioning = false
}) => {

  // Generate scan ID from task ID for WebSocket subscription
  const scanId = useMemo(() => {
    // If task ID has timestamp format (GroupName:RepoID:timestamp), create scan ID
    const parts = task.id.split(':')
    if (parts.length >= 3) {
      return `${parts[0]}:${parts[1]}:${parts[2]}:active`
    }
    // Fallback for legacy format
    return `${task.groupName}:${task.repository}:${Date.now()}:active`
  }, [task.id, task.groupName, task.repository])

  // Task-specific WebSocket connection
  const {
    isConnected: wsConnected,
    isConnecting: wsConnecting,
    progress: wsProgress,
    error: wsError
  } = useTaskSpecificWebSocket({
    taskId: task.id,
    scanId: scanId,
    enabled: true,
    onTaskCompleted,
    onTaskFailed,
    onTaskCancelled
  })

  // Use WebSocket progress if available, otherwise fall back to task progress
  const currentProgress: ProgressType | undefined = wsProgress || task.progress

  const colors = TaskTypeColors[task.type] || 'text-gray-600 bg-gray-50'

  const formatDuration = (startedAt: string): string => {
    try {
      const start = new Date(startedAt)
      const now = new Date()
      const diffMs = now.getTime() - start.getTime()
      const diffMinutes = Math.floor(diffMs / 60000)
      const diffSeconds = Math.floor((diffMs % 60000) / 1000)

      if (diffMinutes > 0) {
        return `${diffMinutes}m ${diffSeconds}s`
      }
      return `${diffSeconds}s`
    } catch {
      return 'Unknown'
    }
  }

  const getProgressPercentage = (): number => {
    if (!currentProgress) return 0
    return currentProgress.percentage || 0
  }

  const getProgressText = (): string => {
    if (!currentProgress) return 'Starting...'

    const percentage = currentProgress.percentage || 0
    const current = currentProgress.current || 0
    const total = currentProgress.total || 0

    if (total > 0) {
      return `${current}/${total} files (${percentage.toFixed(1)}%)`
    }

    return `${percentage.toFixed(1)}%`
  }

  const getConnectionStatus = () => {
    if (wsConnecting) return { icon: Clock, color: 'text-yellow-500', text: 'Connecting...' }
    if (wsConnected) return { icon: Wifi, color: 'text-green-500', text: 'Connected' }
    if (wsError) return { icon: WifiOff, color: 'text-red-500', text: 'Connection Error' }
    return { icon: WifiOff, color: 'text-gray-500', text: 'Disconnected' }
  }

  const connectionStatus = getConnectionStatus()
  const ConnectionIcon = connectionStatus.icon

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 min-h-[200px] flex flex-col">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Badge
              variant="secondary"
              className={colors}
            >
              {task.type.replace('_', ' ').toUpperCase()}
            </Badge>
            <div className="flex items-center gap-1">
              <ConnectionIcon className={`h-3 w-3 ${connectionStatus.color}`} />
              <span className={`text-xs ${connectionStatus.color}`}>
                {connectionStatus.text}
              </span>
            </div>
          </div>
          <h3 className="font-medium text-gray-900 truncate" title={task.name}>
            {task.name}
          </h3>
          <p className="text-sm text-gray-500 truncate">
            {task.repository} • {task.groupName}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-1 ml-2">
          <Button
            size="sm"
            variant="outline"
            onClick={(e) => {
              e.stopPropagation()
              onPause?.(task.id)
            }}
            disabled={isActioning}
            className="h-8 w-8 p-0"
            title="Pause Task"
          >
            <Pause className="h-3 w-3" />
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={(e) => {
              e.stopPropagation()
              onRetry?.(task.id)
            }}
            disabled={isActioning}
            className="h-8 w-8 p-0"
            title="Retry Task"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={(e) => {
              e.stopPropagation()
              onCancel?.(task.id)
            }}
            disabled={isActioning}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
            title="Cancel Task"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Progress Section */}
      <div className="flex-1 flex flex-col justify-center">
        <div className="mb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">{getProgressText()}</span>
          </div>
          <Progress
            value={getProgressPercentage()}
            className="h-2"
          />
        </div>

        {/* Current File */}
        {currentProgress?.currentFile && (
          <div className="mb-2">
            <p className="text-xs text-gray-500 mb-1">Current File:</p>
            <p className="text-xs text-gray-700 truncate" title={currentProgress.currentFile}>
              {currentProgress.currentFile}
            </p>
          </div>
        )}

        {/* Performance Metrics */}
        {currentProgress && (
          <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
            {currentProgress.filesPerSecond && (
              <div>
                <span className="font-medium">Speed:</span> {currentProgress.filesPerSecond.toFixed(1)} files/s
              </div>
            )}
            {currentProgress.estimatedTimeRemaining && (
              <div>
                <span className="font-medium">ETA:</span> {Math.round(currentProgress.estimatedTimeRemaining / 1000)}s
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex justify-between items-center text-xs text-gray-500 mt-3 pt-3 border-t border-gray-100">
        <span>Started: {formatDuration(task.startedAt)} ago</span>
        <span className="truncate ml-2" title={task.id}>
          ID: {task.id.length > 20 ? `${task.id.substring(0, 20)}...` : task.id}
        </span>
      </div>
    </div>
  )
}
