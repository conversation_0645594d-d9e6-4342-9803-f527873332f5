import { useState, useEffect, useRef, useCallback } from 'react'
import { API_CONFIG } from '@/configs'
import { Progress } from '@/types/scanLogs'

interface TaskWebSocketState {
  isConnected: boolean
  isConnecting: boolean
  error: string | null
  progress: Progress | null
  lastUpdate: any
}

interface UseTaskSpecificWebSocketProps {
  taskId: string
  scanId?: string
  enabled: boolean
  onTaskCompleted?: (taskId: string) => void
  onTaskFailed?: (taskId: string, error: string) => void
  onTaskCancelled?: (taskId: string) => void
}

interface UseTaskSpecificWebSocketReturn {
  isConnected: boolean
  isConnecting: boolean
  error: string | null
  progress: Progress | null
  lastUpdate: any
  connect: () => void
  disconnect: () => void
}

export const useTaskSpecificWebSocket = ({
  taskId,
  scanId,
  enabled,
  onTaskCompleted,
  onTaskFailed,
  onTaskCancelled
}: UseTaskSpecificWebSocketProps): UseTaskSpecificWebSocketReturn => {

  const [state, setState] = useState<TaskWebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    progress: null,
    lastUpdate: null
  })

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectCountRef = useRef(0)
  const onTaskCompletedRef = useRef(onTaskCompleted)
  const onTaskFailedRef = useRef(onTaskFailed)
  const onTaskCancelledRef = useRef(onTaskCancelled)
  const scanIdRef = useRef(scanId)
  const maxReconnectAttempts = 5
  const reconnectInterval = 3000

  // Update refs when values change
  onTaskCompletedRef.current = onTaskCompleted
  onTaskFailedRef.current = onTaskFailed
  onTaskCancelledRef.current = onTaskCancelled
  scanIdRef.current = scanId

  // Determine WebSocket protocol and URL
  const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const wsUrl = `${wsProtocol}//${window.location.host}${API_CONFIG.WS_BASE_URL}/progress`

  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
  }, [])

  const connect = useCallback(() => {
    if (!enabled || !taskId) {
      return
    }

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return // Already connected
    }

    cleanup()

    setState(prev => ({ ...prev, isConnecting: true, error: null }))

    try {
      const clientId = `task-${taskId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
      const fullWsUrl = `${wsUrl}?clientId=${clientId}`

      wsRef.current = new WebSocket(fullWsUrl)

      wsRef.current.onopen = () => {
        console.log(`Task WebSocket connected for task: ${taskId}`)
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null
        }))
        reconnectCountRef.current = 0

        // Subscribe to this specific task's scan ID
        if (scanIdRef.current) {
          const subscribeMessage = {
            type: 'subscribe_scan',
            scanId: scanIdRef.current
          }
          wsRef.current?.send(JSON.stringify(subscribeMessage))
          console.log(`Subscribed to scan updates for task ${taskId}, scan ${scanIdRef.current}`)
        }
      }

      wsRef.current.onmessage = (event) => {
        try {
          const update = JSON.parse(event.data)

          setState(prev => ({
            ...prev,
            lastUpdate: update
          }))

          // Handle progress updates
          if (update.progress) {
            setState(prev => ({
              ...prev,
              progress: update.progress
            }))
          }

          // Handle task lifecycle events
          switch (update.eventType) {
            case 'task_completed':
            case 'scan_complete':
              console.log(`Received ${update.eventType} event for task ${taskId}:`, {
                updateTaskId: update.taskId,
                updateScanId: update.scanId,
                expectedTaskId: taskId,
                expectedScanId: scanIdRef.current,
                matches: update.taskId === taskId || update.scanId === scanIdRef.current
              })
              if (update.taskId === taskId || update.scanId === scanIdRef.current) {
                console.log(`Task ${taskId} completed`)
                onTaskCompletedRef.current?.(taskId)
              }
              break

            case 'task_failed':
            case 'scan_error':
              if (update.taskId === taskId || update.scanId === scanIdRef.current) {
                console.log(`Task ${taskId} failed:`, update.error)
                onTaskFailedRef.current?.(taskId, update.error || 'Task failed')
              }
              break

            case 'task_cancelled':
              if (update.taskId === taskId || update.scanId === scanIdRef.current) {
                console.log(`Task ${taskId} cancelled`)
                onTaskCancelledRef.current?.(taskId)
              }
              break
          }

        } catch (error) {
          console.error(`Failed to parse WebSocket message for task ${taskId}:`, error)
        }
      }

      wsRef.current.onclose = (event) => {
        console.log(`Task WebSocket disconnected for task ${taskId}:`, event.code, event.reason)
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }))

        // Attempt reconnection if enabled and not at max attempts
        if (enabled && reconnectCountRef.current < maxReconnectAttempts) {
          reconnectCountRef.current++
          console.log(`Attempting to reconnect task WebSocket for ${taskId} (attempt ${reconnectCountRef.current}/${maxReconnectAttempts})`)

          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval * reconnectCountRef.current) // Exponential backoff
        }
      }

      wsRef.current.onerror = (error) => {
        console.error(`Task WebSocket error for task ${taskId}:`, error)
        setState(prev => ({
          ...prev,
          error: `WebSocket connection failed for task ${taskId}`,
          isConnecting: false
        }))
      }

    } catch (error) {
      console.error(`Failed to create WebSocket connection for task ${taskId}:`, error)
      setState(prev => ({
        ...prev,
        error: `Failed to connect to task ${taskId}`,
        isConnecting: false
      }))
    }
  }, [enabled, taskId, wsUrl, cleanup])

  const disconnect = useCallback(() => {
    cleanup()
    setState({
      isConnected: false,
      isConnecting: false,
      error: null,
      progress: null,
      lastUpdate: null
    })
  }, [cleanup])

  // Auto-connect when enabled
  useEffect(() => {
    if (enabled && taskId) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      cleanup()
    }
  }, [enabled, taskId])

  return {
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,
    progress: state.progress,
    lastUpdate: state.lastUpdate,
    connect,
    disconnect
  }
}
