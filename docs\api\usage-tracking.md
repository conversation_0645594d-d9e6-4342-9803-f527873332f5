# Usage Tracking API Documentation

This document describes the REST API endpoints for the Group Usage Tracking feature.

## Base URL

All API endpoints are prefixed with `/api`.

## Authentication

Currently, no authentication is required for these endpoints.

## Usage Sources Management

### Get All Usage Sources

**GET** `/usage-sources`

Retrieves all configured usage sources with optional filtering and pagination.

#### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `type` | string | Filter by source type (`git`, `api`, `file`) | - |
| `active` | boolean | Filter by active status | - |
| `page` | integer | Page number (1-based) | 1 |
| `pageSize` | integer | Number of items per page (1-100) | 50 |

#### Response

```json
{
  "sources": [
    {
      "id": "uuid",
      "name": "My Git Repository",
      "type": "git",
      "isActive": true,
      "scanFrequency": 3600,
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "includePatterns": ["*.yaml", "*.json"],
      "excludePatterns": ["*.log"],
      "gitConfig": {
        "repoURL": "https://github.com/user/repo.git",
        "branch": "main",
        "authType": "token",
        "token": "***"
      }
    }
  ],
  "total": 1,
  "page": 1,
  "pageSize": 50
}
```

### Get Usage Source by ID

**GET** `/usage-sources/{id}`

Retrieves a specific usage source by its ID.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Usage source ID |

#### Response

```json
{
  "id": "uuid",
  "name": "My Git Repository",
  "type": "git",
  "isActive": true,
  "scanFrequency": 3600,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "gitConfig": {
    "repoURL": "https://github.com/user/repo.git",
    "branch": "main",
    "authType": "token"
  }
}
```

### Create Usage Source

**POST** `/usage-sources`

Creates a new usage source.

#### Request Body

```json
{
  "name": "My Git Repository",
  "type": "git",
  "isActive": true,
  "scanFrequency": 3600,
  "includePatterns": ["*.yaml", "*.json"],
  "excludePatterns": ["*.log"],
  "gitConfig": {
    "repoURL": "https://github.com/user/repo.git",
    "branch": "main",
    "authType": "token",
    "token": "github_pat_xxx"
  }
}
```

#### Response

Returns the created usage source with generated ID and timestamps.

**Status Code:** `201 Created`

### Update Usage Source

**PUT** `/usage-sources/{id}`

Updates an existing usage source.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Usage source ID |

#### Request Body

Same as create request, but with the ID field included.

#### Response

Returns the updated usage source.

**Status Code:** `200 OK`

### Delete Usage Source

**DELETE** `/usage-sources/{id}`

Deletes a usage source and all associated scan results.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Usage source ID |

#### Response

```json
{
  "success": true,
  "message": "Usage source deleted successfully"
}
```

**Status Code:** `200 OK`

### Test Usage Source Connection

**POST** `/usage-sources/{id}/test`

Tests the connection to a usage source.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Usage source ID |

#### Response

```json
{
  "success": true,
  "status": {
    "available": true,
    "lastChecked": "2023-01-01T00:00:00Z",
    "error": "",
    "metadata": {}
  }
}
```

### Get Usage Source Statistics

**GET** `/usage-sources/statistics`

Retrieves overall statistics about configured usage sources.

#### Response

```json
{
  "totalSources": 5,
  "activeSources": 3,
  "sourcesByType": {
    "git": 3,
    "api": 1,
    "file": 1
  }
}
```

## Group Usage Tracking

### Get Group Usage Results

**GET** `/repo/{repoId}/groups/{groupName}/usage`

Retrieves usage results for a specific group.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repoId` | string | Repository ID |
| `groupName` | string | Group name |

#### Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `page` | integer | Page number (1-based) | 1 |
| `pageSize` | integer | Number of items per page (1-100) | 50 |
| `sourceType` | string | Filter by source type | - |
| `sourceId` | string | Filter by specific source | - |

#### Response

```json
{
  "results": [
    {
      "id": "uuid",
      "groupName": "my-group",
      "sourceId": "source-uuid",
      "sourceName": "My Git Repository",
      "sourceType": "git",
      "filePath": "config/groups.yaml",
      "lineNumber": 15,
      "context": "groups:\n  - my-group\n  - other-group",
      "matchType": "exact",
      "detectedAt": "2023-01-01T00:00:00Z",
      "repoId": "repo-1",
      "fileSize": 1024,
      "fileType": "yaml",
      "commitHash": "abc123",
      "branch": "main"
    }
  ],
  "total": 1,
  "page": 1,
  "pageSize": 50
}
```

### Trigger Group Usage Scan

**POST** `/repo/{repoId}/groups/{groupName}/scan`

Initiates a usage scan for a specific group.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repoId` | string | Repository ID |
| `groupName` | string | Group name |

#### Request Body

```json
{
  "sourceIds": ["source-uuid-1", "source-uuid-2"],
  "force": true
}
```

#### Response

```json
{
  "success": true,
  "message": "Scan started successfully",
  "scanId": "my-group:repo-1"
}
```

**Status Code:** `200 OK`

### Get Group Scan Status

**GET** `/repo/{repoId}/groups/{groupName}/scan-status`

Retrieves the current scan status for a group.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repoId` | string | Repository ID |
| `groupName` | string | Group name |

#### Response

```json
{
  "groupName": "my-group",
  "sourcesTotal": 3,
  "sourcesScanned": 2,
  "inProgress": true,
  "lastScanTime": "2023-01-01T00:00:00Z",
  "completedSources": ["source-1", "source-2"],
  "pendingSources": ["source-3"],
  "failedSources": [
    {
      "sourceId": "source-4",
      "sourceName": "Failed Source",
      "error": "Connection timeout",
      "failedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "repoId": "repo-1",
  "totalUsages": 15,
  "scanDuration": 120
}
```

### Cancel Group Scan

**DELETE** `/repo/{repoId}/groups/{groupName}/scan`

Cancels an ongoing scan for a group.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repoId` | string | Repository ID |
| `groupName` | string | Group name |

#### Response

```json
{
  "success": true,
  "message": "Scan cancelled successfully"
}
```

### Clear Group Usage Results

**DELETE** `/repo/{repoId}/groups/{groupName}/usage`

Clears all usage results for a group.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repoId` | string | Repository ID |
| `groupName` | string | Group name |

#### Response

```json
{
  "success": true,
  "message": "Usage results cleared successfully"
}
```

### Get Repository Usage Statistics

**GET** `/repo/{repoId}/usage-statistics`

Retrieves usage statistics for a repository.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repoId` | string | Repository ID |

#### Response

```json
{
  "totalGroups": 50,
  "groupsWithUsage": 25,
  "totalUsages": 150,
  "sourcesConfigured": 5,
  "sourcesActive": 3,
  "lastScanTime": "2023-01-01T00:00:00Z"
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request

```json
{
  "error": "Invalid request body",
  "field": "name"
}
```

### 404 Not Found

```json
{
  "error": "Usage source not found"
}
```

### 500 Internal Server Error

```json
{
  "error": "Failed to process request"
}
```

## Data Models

### Usage Source Types

- **git**: Git repository source
- **api**: REST API source  
- **file**: File system source

### Authentication Types

#### Git Sources
- `none`: No authentication
- `token`: Personal access token
- `basic`: Username/password

#### API Sources
- `none`: No authentication
- `basic`: Basic authentication
- `bearer`: Bearer token
- `api-key`: API key in header

### Scan Frequencies

Common scan frequency values (in seconds):
- `300`: 5 minutes
- `900`: 15 minutes
- `1800`: 30 minutes
- `3600`: 1 hour
- `21600`: 6 hours
- `86400`: 24 hours
