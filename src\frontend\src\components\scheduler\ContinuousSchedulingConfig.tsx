import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Settings, Clock, Target, Save, RefreshCw } from 'lucide-react';
import { apiClient } from '@/api/client';
import type { ContinuousSchedulingConfig } from '@/types/autoScan';

export function ContinuousSchedulingConfig() {
  const { toast } = useToast();
  const [config, setConfig] = useState<ContinuousSchedulingConfig>({
    enabled: true,
    targetQueueSize: 15,
    checkIntervalMinutes: 2
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Load current configuration
  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await apiClient.scheduler.getContinuousConfig();
      setConfig(response.config);
    } catch (error) {
      console.error('Failed to load continuous config:', error);
      toast({
        title: 'Error',
        description: 'Failed to load continuous scheduling configuration',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Save configuration
  const saveConfig = async () => {
    try {
      setSaving(true);
      const response = await apiClient.scheduler.updateContinuousConfig(config);
      toast({
        title: 'Success',
        description: response.message || 'Configuration updated successfully',
      });
    } catch (error) {
      console.error('Failed to save continuous config:', error);
      toast({
        title: 'Error',
        description: 'Failed to save continuous scheduling configuration',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const updateConfig = (updates: Partial<ContinuousSchedulingConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading configuration...
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-purple-600" />
              <span>Continuous Scheduling Configuration</span>
              <Badge variant={config.enabled ? "default" : "secondary"}>
                {config.enabled ? "Active" : "Inactive"}
              </Badge>
            </CardTitle>
            <CardDescription>
              Configure the continuous auto-scheduling system that maintains an optimal queue of pending scan tasks.
              The scheduler runs at regular intervals until the target queue size is reached, respecting repository-specific rules.
            </CardDescription>
          </div>
          <Button onClick={saveConfig} disabled={saving} size="sm">
            <Save className="h-4 w-4 mr-1" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="continuous-enabled" className="text-base">Enable Continuous Scheduling</Label>
            <p className="text-sm text-muted-foreground">
              Automatically maintain a queue of pending scan tasks based on priority rules
            </p>
          </div>
          <Switch
            id="continuous-enabled"
            checked={config.enabled}
            onCheckedChange={(enabled) => updateConfig({ enabled })}
          />
        </div>

        {config.enabled && (
          <>
            <Separator />

            {/* Queue Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="target-queue-size" className="flex items-center space-x-2">
                  <Target className="h-4 w-4" />
                  <span>Target Queue Size</span>
                </Label>
                <Input
                  id="target-queue-size"
                  type="number"
                  min="1"
                  max="100"
                  value={config.targetQueueSize}
                  onChange={(e) => updateConfig({ targetQueueSize: parseInt(e.target.value) || 15 })}
                />
                <p className="text-xs text-muted-foreground">
                  Number of pending tasks to maintain in the queue (1-100)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="check-interval" className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>Check Interval (minutes)</span>
                </Label>
                <Input
                  id="check-interval"
                  type="number"
                  min="1"
                  max="60"
                  value={config.checkIntervalMinutes}
                  onChange={(e) => updateConfig({ checkIntervalMinutes: parseInt(e.target.value) || 2 })}
                />
                <p className="text-xs text-muted-foreground">
                  How often to check and maintain the queue (1-60 minutes)
                </p>
              </div>
            </div>



            {/* Status Information */}
            <div className="bg-muted/50 rounded-lg p-4">
              <h4 className="font-medium mb-2">Current Configuration Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Queue Target:</span>
                  <div className="font-medium">{config.targetQueueSize} tasks</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Check Frequency:</span>
                  <div className="font-medium">Every {config.checkIntervalMinutes}m</div>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
