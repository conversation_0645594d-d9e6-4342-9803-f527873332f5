import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Settings,
  Clock,
  Users,
  Search,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Activity,
  Database,
  Globe,
  FileText,
  Zap,
  Trash2
} from 'lucide-react';

import UsageSourcesSettings from '@/components/settings/UsageSourcesSettings';
import { useToast } from '@/components/ui/use-toast';
import api from '@/api/client';
import type { InterruptedScansStats, InterruptedScansResponse } from '@/types/scheduler';

interface UsageTrackingSectionProps {
  className?: string;
}

const UsageTrackingSection: React.FC<UsageTrackingSectionProps> = ({ className }) => {
  const { toast } = useToast();

  // Simplified state for now
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Interrupted scans state
  const [interruptedScansStats, setInterruptedScansStats] = useState<InterruptedScansStats>({
    interruptedCount: 0,
    failedCount: 0,
    pendingRestart: 0,
  });
  const [interruptedScansData, setInterruptedScansData] = useState<InterruptedScansResponse | null>(null);
  const [interruptedScansLoading, setInterruptedScansLoading] = useState(false);

  // Load interrupted scans data
  const loadInterruptedScansData = async () => {
    try {
      setInterruptedScansLoading(true);
      setError(null);

      // Fetch both stats and detailed data
      const [statsResponse, dataResponse] = await Promise.all([
        api.scheduler.getInterruptedScansStats(),
        api.scheduler.getInterruptedScans(),
      ]);

      setInterruptedScansStats(statsResponse);
      setInterruptedScansData(dataResponse);
    } catch (err) {
      console.error('Error loading interrupted scans data:', err);
      setError('Failed to load interrupted scans data');
      toast({
        title: 'Error',
        description: 'Failed to load interrupted scans data',
        variant: 'destructive',
      });
    } finally {
      setInterruptedScansLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadInterruptedScansData();
  }, []);

  // Action handlers
  const handleRefreshStatus = async () => {
    await loadInterruptedScansData();
    toast({
      title: 'Status Refreshed',
      description: 'Interrupted scans data has been refreshed',
    });
  };

  const handleRestartAllInterrupted = async () => {
    try {
      setLoading(true);
      const response = await api.scheduler.restartInterruptedScans();

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'All interrupted scans have been restarted',
        });
        // Refresh data after restart
        await loadInterruptedScansData();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to restart interrupted scans',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error restarting interrupted scans:', err);
      toast({
        title: 'Error',
        description: 'Failed to restart interrupted scans',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClearFailedScans = async () => {
    try {
      setLoading(true);
      const response = await api.scheduler.clearFailedScans();

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Failed scans have been cleared',
        });
        // Refresh data after clearing
        await loadInterruptedScansData();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to clear failed scans',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error clearing failed scans:', err);
      toast({
        title: 'Error',
        description: 'Failed to clear failed scans',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRestartSpecificScan = async (scanId: string) => {
    try {
      setLoading(true);
      const response = await api.scheduler.restartSpecificScan({
        action: 'retry',
        service: 'usage',
        taskId: scanId,
        taskType: 'scan',
      });

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || `Scan ${scanId} has been restarted`,
        });
        // Refresh data after restart
        await loadInterruptedScansData();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to restart scan',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error restarting specific scan:', err);
      toast({
        title: 'Error',
        description: 'Failed to restart scan',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveSpecificScan = async (scanId: string) => {
    try {
      setLoading(true);
      const response = await api.scheduler.removeSpecificScan({
        action: 'retry', // Using retry as the action type
        service: 'usage',
        taskId: scanId,
        taskType: 'scan',
      });

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || `Scan ${scanId} has been removed`,
        });
        // Refresh data after removal
        await loadInterruptedScansData();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to remove scan',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error removing specific scan:', err);
      toast({
        title: 'Error',
        description: 'Failed to remove scan',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>


      {/* Interrupted Scans Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-orange-600" />
            <span>Interrupted Scans Management</span>
          </CardTitle>
          <CardDescription>
            View and restart scans that were interrupted or failed to complete
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-orange-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {interruptedScansLoading ? '...' : interruptedScansStats.interruptedCount}
                </div>
                <div className="text-sm text-gray-600">Interrupted Scans</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {interruptedScansLoading ? '...' : interruptedScansStats.failedCount}
                </div>
                <div className="text-sm text-gray-600">Failed Scans</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {interruptedScansLoading ? '...' : interruptedScansStats.pendingRestart}
                </div>
                <div className="text-sm text-gray-600">Pending Restart</div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshStatus}
                disabled={interruptedScansLoading || loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${interruptedScansLoading ? 'animate-spin' : ''}`} />
                Refresh Status
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleRestartAllInterrupted}
                disabled={interruptedScansLoading || loading || interruptedScansStats.interruptedCount === 0}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Restart All Interrupted
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleClearFailedScans}
                disabled={interruptedScansLoading || loading || interruptedScansStats.failedCount === 0}
              >
                <AlertCircle className="h-4 w-4 mr-2" />
                Clear Failed Scans
              </Button>
            </div>

            {/* Interrupted Scans List */}
            <div className="border rounded-lg">
              <div className="p-4 border-b bg-gray-50">
                <h3 className="font-medium">Recent Interrupted Scans</h3>
              </div>
              <div className="divide-y">
                {interruptedScansLoading ? (
                  <div className="p-4 text-center text-gray-500">
                    <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                    Loading interrupted scans...
                  </div>
                ) : interruptedScansData && interruptedScansData.scans && interruptedScansData.scans.length > 0 ? (
                  interruptedScansData.scans.map((scan) => (
                    <div key={scan.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Group: {scan.groupName}</div>
                          <div className="text-sm text-gray-500">Repository: {scan.repository}</div>
                          <div className="text-sm text-gray-500">
                            Started: {new Date(scan.startedAt).toLocaleString()} • Progress: {scan.progress}%
                          </div>
                          {scan.errorMessage && (
                            <div className="text-sm text-red-500 mt-1">
                              Error: {scan.errorMessage}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={scan.status === 'failed' ? 'destructive' : 'secondary'}
                            className={scan.status === 'interrupted' ? 'bg-orange-100 text-orange-800' : ''}
                          >
                            {scan.status === 'interrupted' ? 'Interrupted' : 'Failed'}
                          </Badge>
                          {scan.canRestart && (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleRestartSpecificScan(scan.id)}
                                disabled={loading}
                              >
                                <RefreshCw className="h-3 w-3 mr-1" />
                                {scan.status === 'failed' ? 'Retry' : 'Restart'}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleRemoveSpecificScan(scan.id)}
                                disabled={loading}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-3 w-3 mr-1" />
                                Remove
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No interrupted or failed scans found
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Automated Scanning Configuration */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-blue-600" />
                <span>Automated Scanning Configuration</span>
                <Badge variant="default" className="bg-blue-100 text-blue-800">
                  Active
                </Badge>
              </CardTitle>
              <CardDescription>
                Configure automatic scanning schedules and performance settings
              </CardDescription>
            </div>
            <Switch
              checked={true}
              disabled={loading}
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Configuration Tabs */}
            <Tabs defaultValue="schedule" className="space-y-4">
              <TabsList>
                <TabsTrigger value="schedule">Schedule</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="notifications">Notifications</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>

              <TabsContent value="schedule" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="interval">Scan Interval (minutes)</Label>
                    <Input
                      id="interval"
                      type="number"
                      min="5"
                      max="1440"
                      defaultValue="60"
                      disabled={loading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timeWindow">Active Time Window</Label>
                    <Select defaultValue="business-hours" disabled={loading}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="always">Always Active</SelectItem>
                        <SelectItem value="business-hours">Business Hours (9-17)</SelectItem>
                        <SelectItem value="off-hours">Off Hours (17-9)</SelectItem>
                        <SelectItem value="weekends">Weekends Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Scan Priority Groups</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">FINANCE_*</Badge>
                    <Badge variant="outline">IT_*</Badge>
                    <Badge variant="outline">SECURITY_*</Badge>
                    <Button variant="ghost" size="sm" className="h-6 px-2">
                      + Add Pattern
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="performance" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxConcurrent">Max Concurrent Scans</Label>
                    <Input
                      id="maxConcurrent"
                      type="number"
                      min="1"
                      max="10"
                      defaultValue="3"
                      disabled={loading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timeout">Scan Timeout (seconds)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      min="30"
                      max="3600"
                      defaultValue="300"
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="retryAttempts">Retry Attempts</Label>
                  <Input
                    id="retryAttempts"
                    type="number"
                    min="0"
                    max="5"
                    defaultValue="2"
                    disabled={loading}
                  />
                </div>
              </TabsContent>

              <TabsContent value="notifications" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch checked={true} disabled={loading} />
                    <Label>Notify on scan completion</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch checked={true} disabled={loading} />
                    <Label>Notify on scan failure</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch checked={false} disabled={loading} />
                    <Label>Send periodic summaries</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notificationEmail">Notification Email</Label>
                    <Input
                      id="notificationEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      disabled={loading}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch checked={false} disabled={loading} />
                    <Label>Enable deep scanning (slower but more thorough)</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch checked={true} disabled={loading} />
                    <Label>Cache scan results for faster subsequent scans</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch checked={true} disabled={loading} />
                    <Label>Auto-cleanup old scan results (&gt;30 days)</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="logLevel">Log Level</Label>
                    <Select defaultValue="info" disabled={loading}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="debug">Debug</SelectItem>
                        <SelectItem value="info">Info</SelectItem>
                        <SelectItem value="warn">Warning</SelectItem>
                        <SelectItem value="error">Error</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Save Button */}
            <div className="flex justify-end">
              <Button disabled={loading}>
                <Settings className="h-4 w-4 mr-2" />
                Save Configuration
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>



    </div>
  );
};

export default UsageTrackingSection;
