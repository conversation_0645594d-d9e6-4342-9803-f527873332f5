# Group Usage Tracking - Background Job Architecture

## Overview

This document outlines the design for the background job architecture that will support the Group Usage Tracking feature. The system will extend the existing scheduler service to handle asynchronous scanning of usage sources for group references.

## Existing Scheduler Service

The application currently has a scheduler service (`SchedulerService`) that:

1. Runs on a ticker (every minute)
2. Checks for scheduled reports
3. Executes report generation in the background
4. Tracks execution status

```go
// Existing scheduler service pattern
type SchedulerService struct {
    dataProcessor DataProcessorInterface
    repoManager   interface {
        GetRepoInstances() ([]RepoInstance, error)
    }
    isRunning      bool
    ticker         *time.Ticker
    executionMutex sync.Mutex
    executionsDir  string
}
```

## Extension for Usage Tracking

### New Components

1. **UsageScannerService**: Main service for managing usage scans
2. **SourceHandlerRegistry**: Registry for different source type handlers
3. **ScanWorkerPool**: Pool of workers for concurrent scanning
4. **ScanStatusManager**: Tracks scan status and results

### Architecture Diagram

```
┌─────────────────┐     ┌───────────────────┐     ┌─────────────────┐
│ SchedulerService│────▶│ UsageScannerService│────▶│ ScanWorkerPool  │
└─────────────────┘     └───────────────────┘     └─────────────────┘
                                │                          │
                                ▼                          ▼
                        ┌───────────────┐         ┌─────────────────┐
                        │SourceHandler  │◀────────│ Worker Goroutine│
                        │Registry       │         └─────────────────┘
                        └───────────────┘                 │
                                │                         │
                                ▼                         ▼
                        ┌───────────────┐         ┌─────────────────┐
                        │SourceHandler  │         │ScanStatusManager│
                        │Implementations│         └─────────────────┘
                        └───────────────┘                 │
                                                          ▼
                                                  ┌─────────────────┐
                                                  │ Usage Results   │
                                                  │ Storage         │
                                                  └─────────────────┘
```

## UsageScannerService

```go
// UsageScannerService manages usage scanning
type UsageScannerService struct {
    sourceManager      UsageSourceManager
    handlerRegistry    SourceHandlerRegistry
    statusManager      ScanStatusManager
    workerPool         ScanWorkerPool
    scheduledScans     map[string]time.Time // Map of groupName:repoID to next scan time
    scheduleMutex      sync.RWMutex
    isRunning          bool
    ticker             *time.Ticker
    scanInterval       time.Duration // Default scan interval
    maxConcurrentScans int           // Maximum concurrent scans
}
```

### Key Methods

```go
// Start begins the scanner service
func (s *UsageScannerService) Start() error

// Stop stops the scanner service
func (s *UsageScannerService) Stop()

// ScanGroupUsage initiates a scan for a group
func (s *UsageScannerService) ScanGroupUsage(ctx context.Context, request UsageScanRequest) error

// GetScanStatus gets the status of a scan
func (s *UsageScannerService) GetScanStatus(groupName, repoID string) (UsageScanStatus, error)

// CancelScan cancels an ongoing scan
func (s *UsageScannerService) CancelScan(groupName, repoID string) error

// checkSchedules checks for scheduled scans
func (s *UsageScannerService) checkSchedules()

// processScan processes a scan request
func (s *UsageScannerService) processScan(ctx context.Context, request UsageScanRequest)
```

## SourceHandlerRegistry

```go
// SourceHandlerRegistry manages source handlers
type SourceHandlerRegistry struct {
    handlers map[SourceType]SourceHandlerFactory
    mutex    sync.RWMutex
}

// SourceHandlerFactory creates a new source handler
type SourceHandlerFactory func() SourceHandler
```

### Key Methods

```go
// RegisterHandler registers a source handler factory
func (r *SourceHandlerRegistry) RegisterHandler(sourceType SourceType, factory SourceHandlerFactory)

// GetHandler gets a source handler for a source type
func (r *SourceHandlerRegistry) GetHandler(sourceType SourceType) (SourceHandler, error)

// GetSupportedTypes gets all supported source types
func (r *SourceHandlerRegistry) GetSupportedTypes() []SourceType
```

## ScanWorkerPool

```go
// ScanWorkerPool manages a pool of worker goroutines
type ScanWorkerPool struct {
    workers        int
    jobQueue       chan ScanJob
    resultsChannel chan ScanResult
    wg             sync.WaitGroup
    isRunning      bool
    mutex          sync.Mutex
}

// ScanJob represents a scan job
type ScanJob struct {
    GroupName string
    RepoID    string
    Source    UsageSource
    Handler   SourceHandler
    Context   context.Context
}

// ScanResult represents a scan result
type ScanResult struct {
    GroupName   string
    RepoID      string
    SourceID    string
    Results     []UsageResult
    Error       error
    CompletedAt time.Time
}
```

### Key Methods

```go
// Start starts the worker pool
func (p *ScanWorkerPool) Start()

// Stop stops the worker pool
func (p *ScanWorkerPool) Stop()

// SubmitJob submits a job to the pool
func (p *ScanWorkerPool) SubmitJob(job ScanJob) error

// worker is the worker goroutine
func (p *ScanWorkerPool) worker()

// processResults processes scan results
func (p *ScanWorkerPool) processResults()
```

## ScanStatusManager

```go
// ScanStatusManager manages scan status
type ScanStatusManager struct {
    statusMap     map[string]UsageScanStatus // Map of groupName:repoID to status
    resultsMap    map[string][]UsageResult   // Map of groupName:repoID to results
    statusMutex   sync.RWMutex
    resultsMutex  sync.RWMutex
    statusDir     string // Directory for status files
    resultsDir    string // Directory for results files
}
```

### Key Methods

```go
// GetStatus gets the status of a scan
func (m *ScanStatusManager) GetStatus(groupName, repoID string) (UsageScanStatus, error)

// UpdateStatus updates the status of a scan
func (m *ScanStatusManager) UpdateStatus(status UsageScanStatus) error

// AddResults adds results to a scan
func (m *ScanStatusManager) AddResults(groupName, repoID, sourceID string, results []UsageResult) error

// GetResults gets the results of a scan
func (m *ScanStatusManager) GetResults(groupName, repoID string, page, pageSize int) (UsageResultList, error)

// ClearResults clears the results of a scan
func (m *ScanStatusManager) ClearResults(groupName, repoID string) error

// SaveStatus saves the status to disk
func (m *ScanStatusManager) SaveStatus(status UsageScanStatus) error

// LoadStatus loads the status from disk
func (m *ScanStatusManager) LoadStatus(groupName, repoID string) (UsageScanStatus, error)
```

## Integration with Existing Scheduler

The `UsageScannerService` will be integrated with the existing scheduler service:

```go
// In main.go
schedulerService := services.NewSchedulerService(dataProcessor, nil, *dataDir)
usageScannerService := services.NewUsageScannerService(usageSourceManager, *dataDir)

// Register with scheduler
schedulerService.RegisterExternalService(usageScannerService)
```

## Scan Workflow

1. **Scheduled Scan**:
   - Scheduler triggers scan based on source scan frequency
   - UsageScannerService creates scan jobs for each active source
   - Jobs are submitted to worker pool

2. **Manual Scan**:
   - User triggers scan via API
   - UsageScannerService creates scan jobs for requested sources
   - Jobs are submitted to worker pool with higher priority

3. **Scan Execution**:
   - Worker pool processes jobs concurrently
   - Each worker initializes the appropriate source handler
   - Handler scans the source for group references
   - Results are collected and stored

4. **Status Tracking**:
   - ScanStatusManager tracks progress of each scan
   - Status is updated as sources are completed
   - Results are stored and indexed for retrieval

## Error Handling and Recovery

1. **Source Failures**:
   - Individual source failures are recorded but don't fail the entire scan
   - Failed sources are marked in the scan status
   - Retry mechanism for transient failures

2. **Worker Crashes**:
   - Worker pool monitors for crashed workers
   - Crashed jobs are resubmitted with backoff
   - Maximum retry count to prevent infinite loops

3. **Service Restart**:
   - Status is persisted to disk
   - Incomplete scans can be resumed after restart
   - In-progress flag is cleared on startup

## Performance Considerations

1. **Concurrency Control**:
   - Configurable number of worker goroutines
   - Rate limiting for external API calls
   - Backpressure mechanism when queue is full

2. **Resource Management**:
   - Timeout for long-running scans
   - Memory limits for large result sets
   - Disk space monitoring for result storage

3. **Caching**:
   - Cache source data to reduce external calls
   - Cache scan results for frequently accessed groups
   - Invalidate cache on source changes
