# Search Quick Reference Guide

This guide provides a quick reference for using the search functionality in the ADGitOps UI application.

## Basic Search Syntax

| Syntax | Description | Example |
|--------|-------------|---------|
| `column:value` | Exact match | `groupname:admins` |
| `column:~value` | Contains match | `groupname:~admin` |
| `column:value*` | Prefix match | `name:j*` |
| `"exact phrase"` | Match exact phrase | `"access control"` |
| Plain text | Search all fields | `admin` |

## Query Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `AND` | Both terms must match | `type:security AND lob:fm` |
| `OR` | Either term can match | `type:security OR type:admin` |
| `NOT` | Exclude matches | `groupname:~admin NOT lob:fm` |
| `( )` | Grouping expressions | `(type:security OR type:admin) AND lob:fm` |

## Searchable Fields

### Groups

- `groupname:` - Group name
- `type:` - Group type
- `members:` - Group members
- `lob:` - Line of business
- `description:` - Group description

### Users

- `name:` - Username
- `groups:` - Group memberships
- `lob:` - Line of business

## Common Search Examples

### Finding Groups

- Find security groups: `type:security`
- Find groups with "admin" in the name: `groupname:~admin`
- Find groups in FM line of business: `lob:fm`
- Find groups with member "john": `members:john`
- Find security groups in FM: `type:security AND lob:fm`
- Find security or admin groups: `type:security OR type:admin`
- Find admin groups not in FM: `groupname:~admin NOT lob:fm`

### Finding Users

- Find users named "john": `name:john`
- Find users with "john" in their name: `name:~john`
- Find users in admin groups: `groups:~admin`
- Find users in FM line of business: `lob:fm`
- Find FM users in admin groups: `lob:fm AND groups:~admin`
- Find users named john or in admin groups: `name:~john OR groups:~admin`

## Tips

- All searches are case-insensitive
- Use quotes for phrases with spaces: `description:"access control"`
- The default operator between terms is AND
- Use parentheses for complex queries: `(type:security OR type:admin) AND lob:fm`
- Click the help icon (?) next to the search box for more examples
