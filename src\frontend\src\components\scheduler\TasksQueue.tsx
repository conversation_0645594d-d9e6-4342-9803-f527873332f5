import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/components/ui/use-toast';
import { ActiveTaskTile } from './ActiveTaskTile';
import {
  ChevronRight,
  Calendar,
  XCircle,
  CheckCircle,
  RefreshCw,
  Activity,
  Clock,
  AlertTriangle,
  Timer,
  Database,
  User,
  ExternalLink,
  GripVertical,
  RotateCcw,
  Trash2,
  Edit,
  FastForward
} from 'lucide-react';

import type {
  PlannedTask,
  ActiveTask,
  FailedTask,
  CompletedTask
} from '@/types/scheduler';
import type { RealTimeProgressUpdate } from '@/types/scanLogs';
import type { Progress as ProgressType } from '@/types/scanLogs';

import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName
} from '@/types/scheduler';



interface TasksQueueProps {
  // Task data
  plannedTasks: PlannedTask[];
  activeTasks: ActiveTask[];
  failedTasks: FailedTask[];
  completedTasks: CompletedTask[];

  // Loading states
  loading?: boolean;

  // WebSocket data for real-time progress
  isConnected?: boolean;
  isConnecting?: boolean;
  wsError?: string | null;
  lastUpdate?: RealTimeProgressUpdate | null;
  isAdminSubscribed?: boolean;
  realTimeProgress?: Map<string, ProgressType>;
  globalScans?: Map<string, any>;

  // Event handlers
  onRefresh?: () => void;
  onTriggerTask?: (taskId: string) => void;
  onDelayTask?: (taskId: string, delayMinutes: number) => void;
  onPauseTask?: (taskId: string, taskType?: string) => void;
  onResumeTask?: (taskId: string, taskType?: string) => void;
  onCancelTask?: (taskId: string, taskType?: string) => void;
  onRetryTask?: (taskId: string) => void;
  onDeleteTask?: (taskId: string) => void;
  onEditTask?: (taskId: string, updates: Partial<PlannedTask>) => void;
  onReorderTasks?: (taskIds: string[]) => void;
  onDeleteAllFailedTasks?: () => void;
  onDeleteAllScheduledTasks?: () => void;

  // Real-time monitoring controls
  enableRealTimeMonitoring?: boolean;
  onToggleRealTimeMonitoring?: () => void;
  onConnect?: () => void;

  // Retry fix - tracking recently retried tasks
  recentlyRetriedTasks?: Set<string>;
  onTaskRestarted?: (taskId: string, taskType: string) => void;
}

// Default open states for swimlanes
const DEFAULT_SWIMLANE_STATES = {
  active: true,      // Active tasks expanded by default
  scheduled: true,   // Scheduled tasks expanded by default
  failed: true,      // Failed tasks expanded by default
  completed: false   // Completed tasks collapsed by default
};

const TasksQueue: React.FC<TasksQueueProps> = ({
  plannedTasks = [],
  activeTasks = [],
  failedTasks = [],
  completedTasks = [],
  loading = false,
  isConnected = false,
  isConnecting = false,
  wsError = null,
  lastUpdate = null,
  isAdminSubscribed = false,
  realTimeProgress = new Map(),
  globalScans = new Map(),
  onRefresh,
  onTriggerTask,
  onDelayTask,
  onPauseTask,
  onResumeTask,
  onCancelTask,
  onRetryTask,
  onDeleteTask,
  onEditTask,
  onReorderTasks,
  onDeleteAllFailedTasks,
  onDeleteAllScheduledTasks,
  enableRealTimeMonitoring = true,
  onToggleRealTimeMonitoring,
  onConnect,
  recentlyRetriedTasks = new Set(),
  onTaskRestarted
}) => {
  const { toast } = useToast();

  // Drag and drop state for scheduled tasks
  const [draggedTask, setDraggedTask] = useState<string | null>(null);
  const [dragOverTask, setDragOverTask] = useState<string | null>(null);

  // Action states
  const [actioningTasks, setActioningTasks] = useState<Set<string>>(new Set());
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  // Track last refresh time for display purposes
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Update last refresh when manual refresh is triggered
  useEffect(() => {
    setLastRefresh(new Date());
  }, [plannedTasks, activeTasks, failedTasks, completedTasks]);

  // Helper function to detect scheduler events
  const isSchedulerEvent = (update: any): boolean => {
    const schedulerEventTypes = [
      'task_cancelled',
      'task_restarted',
      'task_removed',
      'task_paused',
      'task_resumed',
      'scheduler_refresh',
      // Auto-scan specific events
      'task_started',
      'task_completed',
      'task_failed',
      // Progress events for real-time updates
      'scan_init',
      'scan_start',
      'progress',
      'file_progress',
      'chunk_start',
      'chunk_complete',
      'file_complete',
      'scan_complete',
      'scan_error',
      'error'
    ];

    // Check if it has a known eventType
    if (update?.eventType && schedulerEventTypes.includes(update.eventType)) {
      return true;
    }

    // Also consider events with progress data but no eventType as scheduler events
    // These should be handled by useSchedulerWebSocket hook
    if (!update?.eventType && update?.progress && update?.scanId && update?.repoId && update?.groupName) {
      return true;
    }

    return false;
  };

  // Handle WebSocket events for task state changes
  useEffect(() => {

    // Only process task management events, let progress events pass through to useSchedulerWebSocket
    if (lastUpdate && lastUpdate.eventType) {
      // Handle task management events that require UI actions
      switch (lastUpdate.eventType) {
        case 'task_restarted':
          if (lastUpdate.success && lastUpdate.taskId && onTaskRestarted) {
            onTaskRestarted(lastUpdate.taskId, lastUpdate.taskType || 'usage_scan');
          }
          break;

        case 'scheduler_refresh':
        case 'task_started':
        case 'task_completed':
        case 'task_failed':
        case 'task_cancelled':
          // These events indicate task state changes that require UI refresh
          if (onRefresh) {
            // Use debounced refresh to prevent multiple rapid calls
            onRefresh();
          }
          break;

        case 'progress':
        case 'file_progress':
        case 'scan_start':
        case 'scan_complete':
        case 'scan_error':
        case 'scan_init':
        case 'chunk_start':
        case 'chunk_complete':
        case 'file_complete':
        case 'error':
          // Progress events - let them pass through to useSchedulerWebSocket hook
          break;

        default:
          // Unhandled event type - silently ignore
          break;
      }
    }
  }, [lastUpdate, onTaskRestarted, onRefresh]);

  // Drag and drop handlers for scheduled tasks
  const handleDragStart = useCallback((e: React.DragEvent, taskId: string) => {
    setDraggedTask(taskId);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, taskId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverTask(taskId);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverTask(null);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, targetTaskId: string) => {
    e.preventDefault();

    if (!draggedTask || draggedTask === targetTaskId) {
      setDraggedTask(null);
      setDragOverTask(null);
      return;
    }

    // Find the tasks and reorder them
    const draggedIndex = plannedTasks.findIndex(task => task.id === draggedTask);
    const targetIndex = plannedTasks.findIndex(task => task.id === targetTaskId);

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedTask(null);
      setDragOverTask(null);
      return;
    }

    // Create new order
    const newOrder = [...plannedTasks];
    const [movedTask] = newOrder.splice(draggedIndex, 1);
    newOrder.splice(targetIndex, 0, movedTask);

    // Extract task IDs in new order
    const newTaskIds = newOrder.map(task => task.id);

    // Call the reorder handler
    if (onReorderTasks) {
      onReorderTasks(newTaskIds);
    }

    setDraggedTask(null);
    setDragOverTask(null);
  }, [draggedTask, plannedTasks, onReorderTasks]);

  // Task action handlers with immediate refresh
  const handleTaskAction = useCallback(async (taskId: string, action: string, taskType?: string) => {
    setActioningTasks(prev => new Set(prev).add(taskId));

    try {
      let success = false;
      let message = '';

      switch (action) {
        case 'trigger':
          if (onTriggerTask) {
            await onTriggerTask(taskId);
            success = true;
            message = 'Task triggered successfully';
          }
          break;
        case 'pause':
          if (onPauseTask) {
            await onPauseTask(taskId, taskType);
            success = true;
            message = 'Task paused successfully';
          }
          break;
        case 'resume':
          if (onResumeTask) {
            await onResumeTask(taskId, taskType);
            success = true;
            message = 'Task resumed successfully';
          }
          break;
        case 'cancel':
          if (onCancelTask) {
            await onCancelTask(taskId, taskType);
            success = true;
            message = 'Task cancelled successfully';
          }
          break;
        case 'retry':
          if (onRetryTask) {
            await onRetryTask(taskId);
            success = true;
            message = 'Task retry initiated successfully, updating UI';
          }
          break;
        case 'delete':
          if (onDeleteTask) {
            await onDeleteTask(taskId);
            success = true;
            message = 'Task deleted successfully';
          }
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (success) {
        // Show success toast
        toast({
          title: 'Success',
          description: message,
        });

        // Immediate refresh to update UI state using debounced refresh
        if (onRefresh) {
          onRefresh(); // This is now the debounced refresh function
        }
      }
    } catch (error) {
      console.error(`Failed to ${action} task:`, error);

      // Provide more specific error messages based on the action and error type
      let errorTitle = 'Operation Failed';
      let errorDescription = `Failed to ${action} task`;

      if (action === 'retry') {
        errorTitle = 'Retry Failed';
        errorDescription = 'Failed to retry the task. Please check the task status and try again.';
      } else if (action === 'cancel') {
        errorTitle = 'Cancellation Failed';
        errorDescription = 'Failed to cancel the task. It may have already completed or been cancelled.';
      } else if (action === 'delete') {
        errorTitle = 'Deletion Failed';
        errorDescription = 'Failed to delete the task. Please refresh and try again.';
      }

      // Check if it's a network error
      if (error instanceof TypeError && error.message.includes('fetch')) {
        errorDescription += ' Please check your network connection.';
      }

      toast({
        title: errorTitle,
        description: errorDescription,
        variant: 'destructive',
      });
    } finally {
      setActioningTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  }, [onTriggerTask, onPauseTask, onResumeTask, onCancelTask, onRetryTask, onDeleteTask, onRefresh, toast]);

  // Toggle task details expansion
  const toggleTaskExpansion = useCallback((taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  }, []);

  // Manual refresh handler
  const handleManualRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
      setLastRefresh(new Date());
    }
  }, [onRefresh]);






  // Note: Removed activeWebSocketScans since we now use task-specific WebSocket connections
  // in ActiveTaskTile components for consistent styling and real-time progress

  const totalActiveItems = (activeTasks?.length || 0);
  const totalPendingTasks = (plannedTasks?.length || 0) + totalActiveItems + (failedTasks?.length || 0);

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch {
      return dateString;
    }
  };

  const formatDuration = (durationMs: number) => {
    try {
      if (!durationMs || durationMs < 1000) {
        return `${durationMs || 0}ms`;
      }
      const seconds = Math.floor(durationMs / 1000);
      if (seconds < 60) {
        return `${seconds}s`;
      }
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } catch (error) {
      console.error('Error formatting duration:', error);
      return '0s';
    }
  };

  // Safe date formatting helper
  const safeFormatRelativeTime = (dateInput: string | Date | undefined | null) => {
    try {
      if (!dateInput) return 'Unknown time';

      let dateString: string;
      if (typeof dateInput === 'string') {
        dateString = dateInput;
      } else if (dateInput instanceof Date) {
        dateString = dateInput.toISOString();
      } else {
        return 'Unknown time';
      }

      return formatRelativeTime(dateString);
    } catch (error) {
      console.error('Error formatting relative time:', error, dateInput);
      return 'Unknown time';
    }
  };

  // Error boundary wrapper
  try {
    return (
      <Card>
        <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Timer className="h-5 w-5 text-blue-600" />
              <span>Tasks Queue</span>
              <Badge variant="secondary">
                {totalPendingTasks} pending
              </Badge>
            </CardTitle>
            <CardDescription>
              Unified view of all scheduled, active, failed, and completed tasks with real-time progress monitoring
              <br />
              <span className="text-xs text-gray-500">
                Last updated: {lastRefresh.toLocaleTimeString()} • Auto-refresh every 20s
              </span>
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {/* Real-time monitoring toggle */}
            {onToggleRealTimeMonitoring && (
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleRealTimeMonitoring}
                className={enableRealTimeMonitoring ? 'bg-green-50 border-green-200 text-green-700' : ''}
              >
                <Activity className={`h-4 w-4 mr-2 ${enableRealTimeMonitoring ? 'text-green-600' : ''}`} />
                {enableRealTimeMonitoring ? 'Real-time On' : 'Real-time Off'}
              </Button>
            )}



            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleManualRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Active Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.active}>
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
              <div className="flex items-center space-x-3">
                <Activity className="h-5 w-5 text-green-600" />
                <span className="font-semibold">Active Tasks</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {totalActiveItems} running
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                {totalActiveItems > 0 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async (e) => {
                        e.stopPropagation(); // Prevent collapsible trigger
                        // Cancel all active tasks
                        for (const task of activeTasks || []) {
                          if (task.status === 'running') {
                            await handleTaskAction(task.id, 'cancel', task.type);
                          }
                        }
                      }}
                      disabled={actioningTasks.size > 0}
                      className="text-red-600 hover:text-red-700"
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      Cancel All
                    </Button>
                  </div>
                )}
                <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2 max-h-96 overflow-y-auto">
            {totalActiveItems === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p>No active tasks running</p>
              </div>
            ) : (
              <div className="space-y-3">
                {/* Active Tasks from Scheduler */}
                {(activeTasks || []).map((task) => {
                  // Use task ID as key since it's now unique with timestamp
                  const isActioning = actioningTasks.has(task.id);

                  return (
                    <ActiveTaskTile
                      key={task.id}
                      task={task}
                      onCancel={(taskId) => handleTaskAction(taskId, 'cancel', task.type)}
                      onPause={(taskId) => handleTaskAction(taskId, 'pause', task.type)}
                      onResume={(taskId) => handleTaskAction(taskId, 'resume', task.type)}
                      onRetry={(taskId) => handleTaskAction(taskId, 'retry', task.type)}
                      onTaskCompleted={() => {
                        // Refresh data to update task lists
                        if (onRefresh) {
                          onRefresh();
                        }
                      }}
                      onTaskFailed={() => {
                        // Refresh data to update task lists
                        if (onRefresh) {
                          onRefresh();
                        }
                      }}
                      onTaskCancelled={() => {
                        // Refresh data to update task lists
                        if (onRefresh) {
                          onRefresh();
                        }
                      }}
                      isActioning={isActioning}
                    />
                  );
                })}


              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Scheduled Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.scheduled}>
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-blue-600" />
                <span className="font-semibold">Scheduled Tasks</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {plannedTasks?.length || 0} scheduled
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                {(plannedTasks?.length || 0) > 0 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async (e) => {
                        e.stopPropagation(); // Prevent collapsible trigger
                        if (onDeleteAllScheduledTasks) {
                          await onDeleteAllScheduledTasks();
                        }
                      }}
                      className="text-red-600 hover:text-red-700"
                      title="Remove All Scheduled Tasks"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Remove All
                    </Button>
                  </div>
                )}
                <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2 max-h-96 overflow-y-auto">
            {(plannedTasks?.length || 0) === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p>No scheduled tasks</p>
              </div>
            ) : (
              <div className="space-y-3">
                {(plannedTasks || []).map((task) => {
                  const typeColor = TaskTypeColors[task.type] || 'bg-gray-100 text-gray-800 border-gray-300';
                  const isOverdue = new Date(task.scheduledFor) < new Date();
                  const isActioning = actioningTasks.has(task.id);
                  const isDragging = draggedTask === task.id;
                  const isDragOver = dragOverTask === task.id;

                  return (
                    <div
                      key={task.id}
                      className={`border rounded-lg p-4 bg-white transition-all duration-200 ${
                        isDragging ? 'opacity-50 scale-95' : ''
                      } ${
                        isDragOver ? 'border-blue-400 bg-blue-50' : ''
                      }`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task.id)}
                      onDragOver={(e) => handleDragOver(e, task.id)}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, task.id)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
                          <Badge className={typeColor}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          {isOverdue && (
                            <Badge variant="destructive" className="text-xs">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Overdue
                            </Badge>
                          )}
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Timer className="h-3 w-3" />
                                <span className={isOverdue ? 'text-red-600 font-medium' : ''}>
                                  {safeFormatRelativeTime(task.scheduledFor)}
                                </span>
                              </div>

                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTaskAction(task.id, 'trigger')}
                            disabled={isActioning}
                            title="Trigger Now"
                          >
                            {isActioning ? (
                              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                            ) : (
                              <FastForward className="h-3 w-3 mr-1" />
                            )}
                            {isActioning ? 'Triggering...' : 'Trigger Now'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTaskAction(task.id, 'cancel', task.type)}
                            disabled={isActioning}
                            className="text-red-600 hover:text-red-700"
                            title="Cancel Task"
                          >
                            <XCircle className="h-3 w-3" />
                          </Button>
                          {onEditTask && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onEditTask(task.id, {})}
                              disabled={isActioning}
                              title="Edit Task"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          )}
                          {onDelayTask && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onDelayTask(task.id, 30)}
                              disabled={isActioning}
                              title="Delay 30min"
                            >
                              <Clock className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>



                      {task.description && (
                        <p className="text-sm text-gray-600 mt-2">{task.description}</p>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Failed Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.failed}>
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
              <div className="flex items-center space-x-3">
                <XCircle className="h-5 w-5 text-red-600" />
                <span className="font-semibold">Failed Tasks</span>
                <Badge variant="destructive">
                  {failedTasks?.length || 0} failed
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                {(failedTasks?.length || 0) > 0 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async (e) => {
                        e.stopPropagation(); // Prevent collapsible trigger
                        if (onDeleteAllFailedTasks) {
                          await onDeleteAllFailedTasks();
                        }
                      }}
                      className="text-red-600 hover:text-red-700"
                      title="Remove All Failed Tasks"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Remove All
                    </Button>
                  </div>
                )}
                <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2 max-h-96 overflow-y-auto">
            {(failedTasks?.length || 0) === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p>No failed tasks - all systems running smoothly!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {(failedTasks || [])
                  .filter(task => !recentlyRetriedTasks.has(task.id)) // Filter out recently retried tasks
                  .map((task) => {
                  const typeColor = TaskTypeColors[task.type] || 'text-gray-600 bg-gray-50';
                  const isActioning = actioningTasks.has(task.id);

                  return (
                    <div key={task.id} className="border rounded-lg p-4 bg-red-50/50 border-red-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Badge className={typeColor}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>Failed {safeFormatRelativeTime(task.failedAt)}</span>
                              </div>
                              {task.retryCount > 0 && (
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                    Retries: {task.retryCount}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.canRetry && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTaskAction(task.id, 'retry')}
                              disabled={isActioning}
                              className="text-green-600 hover:text-green-700"
                              title="Retry Task"
                            >
                              <RotateCcw className="h-3 w-3 mr-1" />
                              Retry
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTaskAction(task.id, 'delete')}
                            disabled={isActioning}
                            className="text-red-600 hover:text-red-700"
                            title="Delete Task"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Last attempt info */}
                      {task.lastAttempt && (
                        <div className="mt-2 text-xs text-gray-500">
                          Last attempt: {safeFormatRelativeTime(task.lastAttempt)}
                        </div>
                      )}

                      {task.errorMessage && (
                        <div className="mt-3 p-3 bg-red-100 border border-red-200 rounded-md">
                          <div className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="text-sm font-medium text-red-800 mb-1">Error Details:</p>
                              <p className="text-sm text-red-700">{task.errorMessage}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Completed Tasks Swimlane */}
        <Collapsible defaultOpen={DEFAULT_SWIMLANE_STATES.completed}>
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-semibold">Completed Tasks</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {completedTasks?.length || 0} completed
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                {(completedTasks?.length || 0) > 0 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async (e) => {
                        e.stopPropagation(); // Prevent collapsible trigger
                        if (completedTasks && completedTasks.length > 0) {
                          for (const task of completedTasks) {
                            await handleTaskAction(task.id, 'delete');
                          }
                        }
                      }}
                      className="text-red-600 hover:text-red-700"
                      title="Clear All Completed Tasks"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear All
                    </Button>
                  </div>
                )}
                <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-2 max-h-96 overflow-y-auto">
            {(completedTasks?.length || 0) === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p>No completed tasks in recent history</p>
              </div>
            ) : (
              <div className="space-y-3">
                {(completedTasks || []).slice(0, 20).map((task) => {
                  const typeColor = TaskTypeColors[task.type] || 'text-gray-600 bg-gray-50';

                  return (
                    <div key={task.id} className="border rounded-lg p-4 bg-green-50/30 border-green-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Badge className={typeColor}>
                            {getTaskTypeDisplayName(task.type)}
                          </Badge>
                          <div>
                            <h4 className="font-medium text-gray-900">{task.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center space-x-1">
                                <Database className="h-3 w-3" />
                                <span>{task.repository}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3" />
                                <span>{task.groupName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>Completed {safeFormatRelativeTime(task.completedAt)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Timer className="h-3 w-3" />
                                <span>Duration: {formatDuration(task.duration)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.repository && task.groupName && (
                            <Button variant="outline" size="sm" asChild>
                              <a href={`/usage/${task.repository}/${task.groupName}`} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-3 w-3 mr-1" />
                                View Results
                              </a>
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleTaskExpansion(task.id)}
                            title="Show Details"
                          >
                            <ChevronRight className={`h-3 w-3 transition-transform ${expandedTasks.has(task.id) ? 'rotate-90' : ''}`} />
                          </Button>
                        </div>
                      </div>

                      {/* Expanded Task Details */}
                      {expandedTasks.has(task.id) && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Started:</span>
                              <span className="ml-2 text-gray-600">{formatDateTime(task.startedAt)}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Completed:</span>
                              <span className="ml-2 text-gray-600">{formatDateTime(task.completedAt)}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Duration:</span>
                              <span className="ml-2 text-gray-600">{formatDuration(task.duration)}</span>
                            </div>

                          </div>
                        </div>
                      )}

                      {/* Task Results Summary */}
                      {(task.resultsCount !== undefined || task.filesScanned !== undefined) && (
                        <div className="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                          {task.resultsCount !== undefined && (
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="h-3 w-3 text-green-600" />
                              <span className="font-medium">{task.resultsCount}</span>
                              <span>results found</span>
                            </div>
                          )}
                          {task.filesScanned !== undefined && (
                            <div className="flex items-center space-x-1">
                              <Database className="h-3 w-3 text-blue-600" />
                              <span className="font-medium">{task.filesScanned}</span>
                              <span>files scanned</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}

                {(completedTasks?.length || 0) > 20 && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">
                      Showing 20 most recent completed tasks out of {completedTasks?.length || 0} total
                    </p>
                  </div>
                )}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
    );
  } catch (error) {
    console.error('TasksQueue render error:', error);
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Tasks Queue Error</span>
          </CardTitle>
          <CardDescription>
            An error occurred while rendering the tasks queue. Please refresh the page.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Button onClick={() => window.location.reload()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
};

export default TasksQueue;
