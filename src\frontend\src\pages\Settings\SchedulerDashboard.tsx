import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

import TaskHistoryLogs from "@/components/scheduler/TaskHistoryLogs"
import TasksQueue from "@/components/scheduler/TasksQueue"
import { AutoScanManagement } from "@/components/scheduler/AutoScanManagement"
import { LoadingState } from "@/components/scheduler/LoadingState"
import { ErrorState } from "@/components/scheduler/ErrorState"
import { DashboardHeader } from "@/components/scheduler/DashboardHeader"
import { SystemOverview } from "@/components/scheduler/SystemOverview"
import { useSchedulerData } from "@/hooks/useSchedulerData"
import { useTaskActions } from "@/hooks/useTaskActions"
import { useSchedulerWebSocket } from "@/hooks/useSchedulerWebSocket"
import { formatTaskQueueTabLabel, hasSchedulerData } from "@/utils/schedulerUtils"







const SchedulerDashboard = () => {

  // Track recently retried tasks to prevent them from being moved back to failed immediately
  const [recentlyRetriedTasks, setRecentlyRetriedTasks] = useState<Set<string>>(new Set())
  const [logsExpanded, setLogsExpanded] = useState(false)

  // Data management hook
  const {
    status,
    overview,
    plannedTasks,
    activeTasks,
    failedTasks,
    completedTasks,
    loading,
    refreshing,
    error,
    lastRefreshTime,
    loadSchedulerData,
    debouncedRefresh,
    handleRefresh,
    setFailedTasksFiltered,
    setPlannedTasksFiltered,
    setActiveTasksFiltered
  } = useSchedulerData(recentlyRetriedTasks)

  // Task actions hook
  const {
    handleRetryTask,
    handleDeleteTask,
    handleCancelTask,
    handleTriggerTask,
    handleDelayTask,
    handlePauseTask,
    handleResumeTask,
    handleReorderTasks,
    handleEditTask,
    handleCleanupStaleTasks,
    handleDeleteAllScheduledTasks,
    handleDeleteAllFailedTasks,
    handleTaskRestarted
  } = useTaskActions({
    recentlyRetriedTasks,
    setRecentlyRetriedTasks,
    loadSchedulerData,
    setFailedTasksFiltered,
    setPlannedTasksFiltered,
    setActiveTasksFiltered,
    failedTasks,
    onRefresh: loadSchedulerData
  })

  // Real-time monitoring state
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true)
  const [recentlyCancelledTasks, setRecentlyCancelledTasks] = useState<Set<string>>(new Set())

  // WebSocket hook for real-time monitoring
  const {
    isConnected,
    isConnecting,
    wsError,
    lastUpdate,
    isAdminSubscribed,
    realTimeProgress,
    globalScans,
    connect
  } = useSchedulerWebSocket({
    isRealTimeEnabled,
    setIsRealTimeEnabled,
    activeTasks,
    recentlyCancelledTasks,
    setRecentlyCancelledTasks,
    handleTaskRestarted
  })

  const toggleRealTimeMonitoring = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled)
    if (!isConnected && !isRealTimeEnabled) {
      connect()
    }
  }























  if (loading) {
    return <LoadingState />
  }

  if (error) {
    return (
      <ErrorState
        error={error}
        onRetry={handleRefresh}
        loading={loading}
        refreshing={refreshing}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <DashboardHeader
        lastRefreshTime={lastRefreshTime}
        isConnected={isConnected}
        isConnecting={isConnecting}
        wsError={wsError}
        onConnect={() => {
          setIsRealTimeEnabled(true)
          connect()
        }}
        onDisconnect={() => setIsRealTimeEnabled(false)}
        onRefresh={handleRefresh}
        onCleanupStaleTasks={handleCleanupStaleTasks}
        onDeleteAllScheduledTasks={handleDeleteAllScheduledTasks}
        refreshing={refreshing}
      />

      {/* Overall Health Status & Statistics */}
      {hasSchedulerData(status, overview) && (
        <SystemOverview status={status!} overview={overview!} />
      )}



      {/* Main Content Tabs */}
      <Tabs defaultValue="queue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="queue">
            {formatTaskQueueTabLabel(plannedTasks, activeTasks, failedTasks)}
          </TabsTrigger>
          <TabsTrigger value="logs">Task History & Logs</TabsTrigger>
          <TabsTrigger value="auto-scan-config">Scan Auto-Scheduler Configuration</TabsTrigger>
        </TabsList>

        {/* Tasks Queue Tab - Unified View */}
        <TabsContent value="queue">
          <TasksQueue
            plannedTasks={plannedTasks}
            activeTasks={activeTasks}
            failedTasks={failedTasks}
            completedTasks={completedTasks}
            loading={loading}
            // WebSocket data for real-time progress
            isConnected={isConnected}
            isConnecting={isConnecting}
            wsError={wsError}
            lastUpdate={lastUpdate}
            isAdminSubscribed={isAdminSubscribed}
            realTimeProgress={realTimeProgress}
            globalScans={globalScans}
            // Event handlers
            onRefresh={debouncedRefresh}
            onTriggerTask={handleTriggerTask}
            onDelayTask={handleDelayTask}
            onPauseTask={handlePauseTask}
            onResumeTask={handleResumeTask}
            onCancelTask={handleCancelTask}
            onRetryTask={handleRetryTask}
            onDeleteTask={handleDeleteTask}
            onEditTask={handleEditTask}
            onReorderTasks={handleReorderTasks}
            onDeleteAllFailedTasks={handleDeleteAllFailedTasks}
            onDeleteAllScheduledTasks={handleDeleteAllScheduledTasks}
            // Real-time monitoring controls
            enableRealTimeMonitoring={isRealTimeEnabled}
            onToggleRealTimeMonitoring={toggleRealTimeMonitoring}
            onConnect={connect}
            // Retry fix - pass the retry tracking state and handlers
            recentlyRetriedTasks={recentlyRetriedTasks}
            onTaskRestarted={handleTaskRestarted}
          />
        </TabsContent>

        {/* Task History & Logs Tab - Integrated scan logs functionality */}
        <TabsContent value="logs">
          <TaskHistoryLogs
            isExpanded={logsExpanded}
            onToggleExpanded={() => setLogsExpanded(!logsExpanded)}
          />
        </TabsContent>

        {/* Auto-Scan Configuration Tab */}
        <TabsContent value="auto-scan-config">
          <AutoScanManagement />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SchedulerDashboard
