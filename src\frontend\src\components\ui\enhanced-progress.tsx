import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Clock,
  File,
  Layers,
  Zap,
  AlertCircle,
  CheckCircle,
  Loader2,
  TrendingUp
} from 'lucide-react';
import { Progress as ProgressType, ChunkProcessingStatus } from '@/types/scanLogs';

interface EnhancedProgressProps {
  progress: ProgressType;
  currentChunk?: ChunkProcessingStatus;
  title?: string;
  showDetails?: boolean;
  className?: string;
}

export function EnhancedProgress({
  progress,
  currentChunk,
  title = "Processing Progress",
  showDetails = true,
  className = ""
}: EnhancedProgressProps) {

  // Format time duration
  const formatDuration = (milliseconds?: number): string => {
    if (!milliseconds || milliseconds <= 0) return "Calculating...";

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Format files per second
  const formatSpeed = (filesPerSecond?: number): string => {
    if (!filesPerSecond || filesPerSecond <= 0) return "Calculating...";

    if (filesPerSecond < 1) {
      return `${(filesPerSecond * 60).toFixed(1)} files/min`;
    } else {
      return `${filesPerSecond.toFixed(1)} files/sec`;
    }
  };

  // Calculate elapsed time
  const getElapsedTime = (): string => {
    if (!progress.startTime) return "Unknown";

    const start = new Date(progress.startTime);
    const now = new Date();
    const elapsed = now.getTime() - start.getTime();

    return formatDuration(elapsed);
  };

  // Get progress color based on percentage
  const getProgressColor = (percentage: number): string => {
    if (percentage < 30) return "bg-blue-500";
    if (percentage < 70) return "bg-yellow-500";
    return "bg-green-500";
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
          {title}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Main Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span className="font-medium">Overall Progress</span>
            <span className="text-muted-foreground">
              {progress.current} / {progress.total} ({progress.percentage}%)
            </span>
          </div>

          <div className="relative">
            <Progress
              value={progress.percentage}
              className="h-3"
            />
            <div
              className={`absolute top-0 left-0 h-full rounded-full transition-all duration-300 ${getProgressColor(progress.percentage)}`}
              style={{ width: `${progress.percentage}%` }}
            />
          </div>

          {progress.description && (
            <p className="text-sm text-muted-foreground mt-1">
              {progress.description}
            </p>
          )}
        </div>

        {/* Current File Information */}
        {progress.currentFile && (
          <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
            <File className="h-4 w-4 text-blue-500" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium">Currently Processing:</p>
              <p className="text-xs text-muted-foreground truncate" title={progress.currentFile}>
                {progress.currentFile}
              </p>
            </div>
          </div>
        )}

        {/* Chunked Processing Information */}
        {progress.totalChunks && progress.totalChunks > 1 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Chunk Progress</span>
            </div>

            <div className="flex justify-between items-center text-sm">
              <span>Chunk {(progress.currentChunk || 0) + 1} of {progress.totalChunks}</span>
              <Badge variant="outline">
                {progress.chunkSize} files per chunk
              </Badge>
            </div>

            {currentChunk && (
              <div className="space-y-1">
                <Progress
                  value={(currentChunk.processedFiles / currentChunk.totalFiles) * 100}
                  className="h-2"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{currentChunk.processedFiles} / {currentChunk.totalFiles} files in chunk</span>
                  <span>{progress.totalResultsFound || 0} results found</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Performance Metrics */}
        {showDetails && (
          <div className="grid grid-cols-2 gap-4">
            {/* Processing Speed */}
            <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-xs text-muted-foreground">Speed</p>
                <p className="text-sm font-medium">
                  {formatSpeed(progress.filesPerSecond)}
                </p>
              </div>
            </div>

            {/* Time Remaining */}
            <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-xs text-muted-foreground">ETA</p>
                <p className="text-sm font-medium">
                  {formatDuration(progress.estimatedTimeRemaining)}
                </p>
              </div>
            </div>

            {/* Elapsed Time */}
            <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg">
              <Zap className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-xs text-muted-foreground">Elapsed</p>
                <p className="text-sm font-medium">
                  {getElapsedTime()}
                </p>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-xs text-muted-foreground">Status</p>
                <p className="text-sm font-medium">
                  {progress.percentage === 100 ? 'Complete' : 'Processing'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Last Update Time */}
        {progress.lastUpdateTime && (
          <div className="text-xs text-muted-foreground text-center pt-2 border-t">
            Last updated: {new Date(progress.lastUpdateTime).toLocaleTimeString()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for smaller spaces
export function CompactProgress({
  progress,
  className = "",
  showCurrentFile = false
}: {
  progress: ProgressType;
  className?: string;
  showCurrentFile?: boolean;
}) {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex justify-between items-center text-sm">
        <span className="font-medium">
          {progress.current} / {progress.total} files
        </span>
        <span className="text-muted-foreground">
          {progress.percentage}%
        </span>
      </div>

      <Progress value={progress.percentage} className="h-2" />

      {/* Only show current file if explicitly requested (to avoid duplication with EnhancedProgress) */}
      {showCurrentFile && progress.currentFile && (
        <p className="text-xs text-muted-foreground truncate" title={progress.currentFile}>
          Processing: {progress.currentFile}
        </p>
      )}

      {progress.estimatedTimeRemaining && progress.estimatedTimeRemaining > 0 && (
        <p className="text-xs text-muted-foreground">
          ETA: {Math.ceil(progress.estimatedTimeRemaining / 1000 / 60)} min remaining
        </p>
      )}
    </div>
  );
}
