import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { EnhancedProgress, CompactProgress } from '@/components/ui/enhanced-progress';
import { useWebSocketProgress } from '@/hooks/useWebSocketProgress';
import {
  Wifi,
  WifiOff,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Activity
} from 'lucide-react';
import { RealTimeProgressUpdate, Progress, ChunkProcessingStatus } from '@/types/scanLogs';

interface RealTimeScanProgressProps {
  scanId: string;
  repoId: string;
  groupName: string;
  onScanComplete?: (results: any) => void;
  onScanError?: (error: string) => void;
  compact?: boolean;
  autoSubscribe?: boolean;
}

export function RealTimeScanProgress({
  scanId,
  repoId,
  groupName,
  onScanComplete,
  onScanError,
  compact = false,
  autoSubscribe = true
}: RealTimeScanProgressProps) {

  // Helper function to detect git fallback scenarios
  const isGitFallbackScenario = (errorMessage?: string): boolean => {
    if (!errorMessage) return false;
    return errorMessage.includes('git_fallback') ||
           errorMessage.includes('using local repository copy') ||
           errorMessage.includes('Git operation failed but local repository exists');
  };

  // Helper function to format error messages for better user experience
  const formatErrorMessage = (errorMessage?: string): string => {
    if (!errorMessage) return '';

    if (isGitFallbackScenario(errorMessage)) {
      return 'Repository sync failed, but scan is continuing with local copy. Results may not reflect the latest changes.';
    }

    // Handle other common error scenarios
    if (errorMessage.includes('git clone failed') || errorMessage.includes('git pull failed')) {
      return 'Repository synchronization failed. Please check your network connection and repository access permissions.';
    }

    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return 'Repository operation timed out. The scan will continue with the available local copy.';
    }

    return errorMessage;
  };
  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    connect,
    disconnect,
    subscribeToScan,
    unsubscribeFromScan,
    getLatestUpdateForScan
  } = useWebSocketProgress();

  const [currentProgress, setCurrentProgress] = useState<Progress | null>(null);
  const [currentChunk, setCurrentChunk] = useState<ChunkProcessingStatus | null>(null);
  const [scanStatus, setScanStatus] = useState<'idle' | 'running' | 'completed' | 'failed'>('idle');
  const [lastMessage, setLastMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Subscribe to scan updates when connected
  useEffect(() => {
    console.log(`RealTimeScanProgress: Connection state - isConnected: ${isConnected}, autoSubscribe: ${autoSubscribe}, scanId: ${scanId}`);

    if (isConnected && autoSubscribe && scanId) {
      console.log(`RealTimeScanProgress: Subscribing to scan: ${scanId}`);
      subscribeToScan(scanId);
      setScanStatus('running');
    }

    return () => {
      if (scanId) {
        console.log(`RealTimeScanProgress: Unsubscribing from scan: ${scanId}`);
        unsubscribeFromScan(scanId);
      }
    };
  }, [isConnected, autoSubscribe, scanId, subscribeToScan, unsubscribeFromScan]);

  // Handle progress updates
  useEffect(() => {
    if (!lastUpdate) {
      return;
    }

    if (lastUpdate.scanId !== scanId) {
      console.log(`RealTimeScanProgress: Ignoring update for different scan. Expected: ${scanId}, Got: ${lastUpdate.scanId}`);
      return;
    }

    console.log(`RealTimeScanProgress: Processing update for scan ${scanId}:`, lastUpdate.eventType, lastUpdate.progress?.percentage);
    const update = lastUpdate;

    // Update progress data
    if (update.progress) {
      setCurrentProgress(update.progress);
    }

    if (update.currentChunk) {
      setCurrentChunk(update.currentChunk);
    }

    // Update status and messages
    setLastMessage(update.message || '');

    switch (update.eventType) {
      case 'scan_start':
        setScanStatus('running');
        setErrorMessage('');
        break;

      case 'scan_complete':
        setScanStatus('completed');
        if (onScanComplete) {
          onScanComplete(update.summary);
        }
        break;

      case 'scan_error':
        setScanStatus('failed');
        const error = update.error || 'Unknown error occurred';
        setErrorMessage(error);
        if (onScanError) {
          onScanError(error);
        }
        break;

      case 'progress':
      case 'file_progress':
      case 'chunk_start':
      case 'chunk_complete':
        setScanStatus('running');
        break;
    }
  }, [lastUpdate, scanId, onScanComplete, onScanError]);

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2">
      {isConnecting ? (
        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      ) : isConnected ? (
        <Wifi className="h-4 w-4 text-green-500" />
      ) : (
        <WifiOff className="h-4 w-4 text-red-500" />
      )}

      <span className="text-sm text-muted-foreground">
        {isConnecting ? 'Connecting...' : isConnected ? 'Connected' : 'Disconnected'}
      </span>

      {!isConnected && !isConnecting && (
        <Button
          size="sm"
          variant="outline"
          onClick={connect}
          className="h-6 px-2"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  );

  // Status badge
  const StatusBadge = () => {
    const statusConfig = {
      idle: { icon: Activity, color: 'bg-gray-500', text: 'Idle' },
      running: { icon: Loader2, color: 'bg-blue-500', text: 'Running' },
      completed: { icon: CheckCircle, color: 'bg-green-500', text: 'Completed' },
      failed: { icon: XCircle, color: 'bg-red-500', text: 'Failed' }
    };

    const config = statusConfig[scanStatus];
    const Icon = config.icon;

    return (
      <Badge variant="outline" className="gap-1">
        <Icon className={`h-3 w-3 ${scanStatus === 'running' ? 'animate-spin' : ''}`} />
        {config.text}
      </Badge>
    );
  };

  // Compact version
  if (compact) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">Scan Progress</CardTitle>
            <div className="flex items-center gap-2">
              <StatusBadge />
              <ConnectionStatus />
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {currentProgress ? (
            <CompactProgress progress={currentProgress} showCurrentFile={true} />
          ) : (
            <div className="text-center text-muted-foreground py-4">
              {scanStatus === 'idle' ? 'Waiting for scan to start...' : 'Loading progress...'}
            </div>
          )}

          {lastMessage && !lastMessage.startsWith('Processing file:') && (
            <p className="text-xs text-muted-foreground mt-2 truncate" title={lastMessage}>
              {lastMessage}
            </p>
          )}
        </CardContent>
      </Card>
    );
  }

  // Full version
  return (
    <div className="space-y-4">
      {/* Connection and Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Real-time Scan Progress</CardTitle>
            <div className="flex items-center gap-3">
              <StatusBadge />
              <ConnectionStatus />
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            Scanning group "{groupName}" in repository "{repoId}"
          </div>
        </CardHeader>
      </Card>

      {/* Error Alert */}
      {(wsError || errorMessage) && (
        <Alert className={`${isGitFallbackScenario(errorMessage) ? 'border-yellow-200 bg-yellow-50' : 'border-red-200 bg-red-50'}`}>
          <AlertCircle className={`h-4 w-4 ${isGitFallbackScenario(errorMessage) ? 'text-yellow-600' : 'text-red-600'}`} />
          <AlertDescription className={`${isGitFallbackScenario(errorMessage) ? 'text-yellow-800' : 'text-red-800'}`}>
            {formatErrorMessage(errorMessage || wsError || undefined)}
          </AlertDescription>
        </Alert>
      )}

      {/* Progress Display */}
      {currentProgress ? (
        <EnhancedProgress
          progress={currentProgress}
          currentChunk={currentChunk || undefined}
          title="File Processing Progress"
          showDetails={true}
        />
      ) : (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              {scanStatus === 'idle' ? (
                <div className="space-y-2">
                  <Activity className="h-8 w-8 mx-auto text-gray-400" />
                  <p>Waiting for scan to start...</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Loader2 className="h-8 w-8 mx-auto animate-spin text-blue-500" />
                  <p>Initializing scan progress...</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Last Message - Only show if it's not just a file processing message */}
      {lastMessage && !lastMessage.startsWith('Processing file:') && (
        <Card>
          <CardContent className="py-3">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-500" />
              <span className="text-sm">{lastMessage}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
