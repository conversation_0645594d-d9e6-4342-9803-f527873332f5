package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/patrickmn/go-cache"
)

// AuthCacheService provides enhanced caching for authentication operations
type AuthCacheService struct {
	cache         *cache.Cache
	mutex         sync.RWMutex
	config        models.AuthCacheConfig
	hitCount      int64
	missCount     int64
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
	vectorDB      *VectorDatabaseService
}

// AuthCacheEntry represents a cached authentication entry
type AuthCacheEntry struct {
	Key          string                 `json:"key"`
	AuthType     string                 `json:"authType"` // "git", "api", "bitbucket", "gitlab"
	SourceID     string                 `json:"sourceId"`
	SourceType   string                 `json:"sourceType"`
	AuthMethod   string                 `json:"authMethod"` // "token", "basic", "bearer"
	Success      bool                   `json:"success"`
	ErrorMessage string                 `json:"errorMessage,omitempty"`
	CreatedAt    time.Time              `json:"createdAt"`
	LastUsed     time.Time              `json:"lastUsed"`
	UsageCount   int                    `json:"usageCount"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// AuthCacheStats represents authentication cache statistics
type AuthCacheStats struct {
	TotalEntries int               `json:"totalEntries"`
	HitCount     int64             `json:"hitCount"`
	MissCount    int64             `json:"missCount"`
	HitRate      float64           `json:"hitRate"`
	CacheSize    int64             `json:"cacheSize"`
	OldestEntry  *time.Time        `json:"oldestEntry,omitempty"`
	NewestEntry  *time.Time        `json:"newestEntry,omitempty"`
	TopSources   []AuthSourceStats `json:"topSources"`
}

// AuthSourceStats represents statistics for a specific source
type AuthSourceStats struct {
	SourceID   string    `json:"sourceId"`
	SourceType string    `json:"sourceType"`
	HitCount   int64     `json:"hitCount"`
	MissCount  int64     `json:"missCount"`
	HitRate    float64   `json:"hitRate"`
	LastUsed   time.Time `json:"lastUsed"`
}

// NewAuthCacheService creates a new authentication cache service
func NewAuthCacheService(config models.AuthCacheConfig) *AuthCacheService {
	if !config.Enabled {
		log.Println("Authentication cache is disabled")
		return &AuthCacheService{
			config: config,
		}
	}

	// Create cache with configured TTL and cleanup interval
	cacheInstance := cache.New(config.TTL, config.CleanupInterval)

	service := &AuthCacheService{
		cache:       cacheInstance,
		config:      config,
		stopCleanup: make(chan struct{}),
	}

	// Start cleanup ticker for statistics and maintenance
	if config.CleanupInterval > 0 {
		service.cleanupTicker = time.NewTicker(config.CleanupInterval)
		go service.cleanupLoop()
	}

	log.Printf("Authentication cache service initialized with TTL: %v, MaxEntries: %d, CleanupInterval: %v",
		config.TTL, config.MaxEntries, config.CleanupInterval)

	return service
}

// SetVectorDatabase sets the vector database for storing cache metrics
func (a *AuthCacheService) SetVectorDatabase(vectorDB *VectorDatabaseService) {
	a.vectorDB = vectorDB
}

// CacheGitAuth caches Git authentication result
func (a *AuthCacheService) CacheGitAuth(sourceID, repoURL, authType, authMethod string, success bool, errorMessage string, metadata map[string]interface{}) {
	if !a.config.Enabled {
		return
	}

	key := a.generateGitAuthKey(sourceID, repoURL, authType, authMethod)
	entry := AuthCacheEntry{
		Key:          key,
		AuthType:     "git",
		SourceID:     sourceID,
		SourceType:   "git",
		AuthMethod:   authMethod,
		Success:      success,
		ErrorMessage: errorMessage,
		CreatedAt:    time.Now(),
		LastUsed:     time.Now(),
		UsageCount:   1,
		Metadata:     metadata,
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	// Check cache size limit
	if a.config.MaxEntries > 0 && a.cache.ItemCount() >= a.config.MaxEntries {
		a.evictOldestEntry()
	}

	a.cache.Set(key, entry, cache.DefaultExpiration)
	log.Printf("Cached Git auth result for source %s (success: %v)", sourceID, success)
}

// GetGitAuth retrieves cached Git authentication result
func (a *AuthCacheService) GetGitAuth(sourceID, repoURL, authType, authMethod string) (*AuthCacheEntry, bool) {
	if !a.config.Enabled {
		a.recordMiss()
		return nil, false
	}

	key := a.generateGitAuthKey(sourceID, repoURL, authType, authMethod)

	a.mutex.Lock()
	defer a.mutex.Unlock()

	if cached, found := a.cache.Get(key); found {
		entry := cached.(AuthCacheEntry)
		entry.LastUsed = time.Now()
		entry.UsageCount++
		a.cache.Set(key, entry, cache.DefaultExpiration)
		a.recordHit()
		log.Printf("Cache hit for Git auth: source %s", sourceID)
		return &entry, true
	}

	a.recordMiss()
	log.Printf("Cache miss for Git auth: source %s", sourceID)
	return nil, false
}

// CacheAPIAuth caches API authentication result
func (a *AuthCacheService) CacheAPIAuth(sourceID, endpoint, authType string, success bool, errorMessage string, metadata map[string]interface{}) {
	if !a.config.Enabled {
		return
	}

	key := a.generateAPIAuthKey(sourceID, endpoint, authType)
	entry := AuthCacheEntry{
		Key:          key,
		AuthType:     "api",
		SourceID:     sourceID,
		SourceType:   "api",
		AuthMethod:   authType,
		Success:      success,
		ErrorMessage: errorMessage,
		CreatedAt:    time.Now(),
		LastUsed:     time.Now(),
		UsageCount:   1,
		Metadata:     metadata,
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	// Check cache size limit
	if a.config.MaxEntries > 0 && a.cache.ItemCount() >= a.config.MaxEntries {
		a.evictOldestEntry()
	}

	a.cache.Set(key, entry, cache.DefaultExpiration)
	log.Printf("Cached API auth result for source %s (success: %v)", sourceID, success)
}

// GetAPIAuth retrieves cached API authentication result
func (a *AuthCacheService) GetAPIAuth(sourceID, endpoint, authType string) (*AuthCacheEntry, bool) {
	if !a.config.Enabled {
		a.recordMiss()
		return nil, false
	}

	key := a.generateAPIAuthKey(sourceID, endpoint, authType)

	a.mutex.Lock()
	defer a.mutex.Unlock()

	if cached, found := a.cache.Get(key); found {
		entry := cached.(AuthCacheEntry)
		entry.LastUsed = time.Now()
		entry.UsageCount++
		a.cache.Set(key, entry, cache.DefaultExpiration)
		a.recordHit()
		log.Printf("Cache hit for API auth: source %s", sourceID)
		return &entry, true
	}

	a.recordMiss()
	log.Printf("Cache miss for API auth: source %s", sourceID)
	return nil, false
}

// InvalidateAuth invalidates cached authentication for a source
func (a *AuthCacheService) InvalidateAuth(sourceID string) {
	if !a.config.Enabled {
		return
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	// Find and remove all entries for this source
	items := a.cache.Items()
	for key, item := range items {
		if entry, ok := item.Object.(AuthCacheEntry); ok {
			if entry.SourceID == sourceID {
				a.cache.Delete(key)
				log.Printf("Invalidated auth cache for source %s", sourceID)
			}
		}
	}
}

// GetStats returns authentication cache statistics
func (a *AuthCacheService) GetStats() AuthCacheStats {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	// Handle disabled cache
	if !a.config.Enabled || a.cache == nil {
		return AuthCacheStats{
			TotalEntries: 0,
			HitCount:     a.hitCount,
			MissCount:    a.missCount,
			CacheSize:    0,
			HitRate:      0,
			TopSources:   []AuthSourceStats{},
		}
	}

	stats := AuthCacheStats{
		TotalEntries: a.cache.ItemCount(),
		HitCount:     a.hitCount,
		MissCount:    a.missCount,
		CacheSize:    int64(a.cache.ItemCount()),
	}

	if stats.HitCount+stats.MissCount > 0 {
		stats.HitRate = float64(stats.HitCount) / float64(stats.HitCount+stats.MissCount) * 100
	}

	// Analyze cache entries
	sourceStats := make(map[string]*AuthSourceStats)
	var oldestTime, newestTime *time.Time

	items := a.cache.Items()
	for _, item := range items {
		if entry, ok := item.Object.(AuthCacheEntry); ok {
			// Track oldest and newest entries
			if oldestTime == nil || entry.CreatedAt.Before(*oldestTime) {
				oldestTime = &entry.CreatedAt
			}
			if newestTime == nil || entry.CreatedAt.After(*newestTime) {
				newestTime = &entry.CreatedAt
			}

			// Track per-source statistics
			if _, exists := sourceStats[entry.SourceID]; !exists {
				sourceStats[entry.SourceID] = &AuthSourceStats{
					SourceID:   entry.SourceID,
					SourceType: entry.SourceType,
					LastUsed:   entry.LastUsed,
				}
			}
			sourceStats[entry.SourceID].HitCount += int64(entry.UsageCount)
		}
	}

	stats.OldestEntry = oldestTime
	stats.NewestEntry = newestTime

	// Convert source stats to slice
	for _, sourceStat := range sourceStats {
		if sourceStat.HitCount > 0 {
			sourceStat.HitRate = 100.0 // All cached entries are hits by definition
		}
		stats.TopSources = append(stats.TopSources, *sourceStat)
	}

	return stats
}

// ClearCache clears all cached authentication entries
func (a *AuthCacheService) ClearCache() {
	if !a.config.Enabled {
		return
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	a.cache.Flush()
	a.hitCount = 0
	a.missCount = 0
	log.Println("Authentication cache cleared")
}

// generateGitAuthKey generates a cache key for Git authentication
func (a *AuthCacheService) generateGitAuthKey(sourceID, repoURL, authType, authMethod string) string {
	data := fmt.Sprintf("git:%s:%s:%s:%s", sourceID, repoURL, authType, authMethod)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// generateAPIAuthKey generates a cache key for API authentication
func (a *AuthCacheService) generateAPIAuthKey(sourceID, endpoint, authType string) string {
	data := fmt.Sprintf("api:%s:%s:%s", sourceID, endpoint, authType)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// recordHit records a cache hit
func (a *AuthCacheService) recordHit() {
	a.hitCount++
}

// recordMiss records a cache miss
func (a *AuthCacheService) recordMiss() {
	a.missCount++
}

// evictOldestEntry removes the oldest cache entry to make room for new ones
func (a *AuthCacheService) evictOldestEntry() {
	items := a.cache.Items()
	var oldestKey string
	var oldestTime time.Time

	for key, item := range items {
		if entry, ok := item.Object.(AuthCacheEntry); ok {
			if oldestKey == "" || entry.CreatedAt.Before(oldestTime) {
				oldestKey = key
				oldestTime = entry.CreatedAt
			}
		}
	}

	if oldestKey != "" {
		a.cache.Delete(oldestKey)
		log.Printf("Evicted oldest cache entry: %s", oldestKey)
	}
}

// cleanupLoop performs periodic cleanup and maintenance
func (a *AuthCacheService) cleanupLoop() {
	for {
		select {
		case <-a.stopCleanup:
			return
		case <-a.cleanupTicker.C:
			a.performMaintenance()
		}
	}
}

// performMaintenance performs periodic maintenance tasks
func (a *AuthCacheService) performMaintenance() {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// Store cache metrics in vector database if available
	if a.vectorDB != nil {
		stats := a.GetStats()
		ctx := context.Background()

		// Store performance metrics
		metrics := ScanPerformanceMetrics{
			AuthCacheHits:   int(stats.HitCount),
			AuthCacheMisses: int(stats.MissCount),
		}

		err := a.vectorDB.StorePerformanceMetrics(ctx, "auth_cache_maintenance", metrics)
		if err != nil {
			log.Printf("Warning: Failed to store auth cache metrics in vector database: %v", err)
		}
	}

	log.Printf("Auth cache maintenance: %d entries, hit rate: %.2f%%",
		a.cache.ItemCount(),
		float64(a.hitCount)/float64(a.hitCount+a.missCount)*100)
}

// Close closes the authentication cache service
func (a *AuthCacheService) Close() {
	if a.cleanupTicker != nil {
		a.cleanupTicker.Stop()
	}
	if a.stopCleanup != nil {
		close(a.stopCleanup)
	}
	log.Println("Authentication cache service closed")
}
