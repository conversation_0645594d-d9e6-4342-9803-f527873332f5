# Backend API Contracts

## Overview

This document provides a comprehensive specification of the backend API endpoints, request/response formats, error handling, and integration patterns. It serves as the authoritative reference for the client-server contract.

## API Architecture

### Base Configuration
- **Base URL**: `/api`
- **Protocol**: HTTP/HTTPS
- **Content Type**: `application/json`
- **Authentication**: Repository-based access (no user auth currently)

### Response Format Standards
```json
{
  "data": {},           // Success response data
  "error": {            // Error response (when applicable)
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {}       // Additional error context
  }
}
```

### HTTP Status Codes
- `200 OK` - Successful GET/PUT operations
- `201 Created` - Successful POST operations
- `204 No Content` - Successful DELETE operations
- `400 Bad Request` - Invalid request data
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server-side errors

## Repository Management APIs

### Repository Configuration

#### List Repositories
```http
GET /api/repo/configurations
```

**Response**:
```json
[
  {
    "id": "string",
    "name": "string",
    "url": "string",
    "branch": "string",
    "status": "active|inactive|error",
    "lastSync": "2024-01-01T00:00:00Z",
    "lastCommit": "string",
    "pollFrequency": 300,
    "hasChanges": false,
    "syncInProgress": false
  }
]
```

#### Create Repository
```http
POST /api/repo/configurations
Content-Type: application/json

{
  "name": "string",
  "url": "string",
  "branch": "string",
  "pollFrequency": 300
}
```

**Response**: `201 Created` with repository object

#### Update Repository
```http
PUT /api/repo/configurations/:id
Content-Type: application/json

{
  "name": "string",
  "url": "string",
  "branch": "string",
  "pollFrequency": 300
}
```

#### Delete Repository
```http
DELETE /api/repo/configurations/:id
```

**Response**: `204 No Content`

#### Repository Operations

##### Sync Repository
```http
POST /api/repo/:id/sync
```

**Response**:
```json
{
  "status": "started|completed|failed",
  "message": "string",
  "hasChanges": false,
  "lastCommit": "string"
}
```

##### Duplicate Repository
```http
POST /api/repo/:id/duplicate
Content-Type: application/json

{
  "name": "string"
}
```

##### Get Repository Status
```http
GET /api/repo/status/:id
```

**Response**:
```json
{
  "id": "string",
  "status": "active|inactive|error",
  "lastSync": "2024-01-01T00:00:00Z",
  "lastCommit": "string",
  "hasChanges": false,
  "syncInProgress": false,
  "errorMessage": "string"
}
```

##### Get Repository Logs
```http
GET /api/repo/:id/logs?level=info&limit=100
```

**Response**:
```json
[
  {
    "timestamp": "2024-01-01T00:00:00Z",
    "level": "info|warning|error|success",
    "message": "string",
    "details": {}
  }
]
```

## Search APIs

### Group Search

#### Search Groups
```http
GET /api/repo/:id/search/groups?q={query}&page={page}&limit={limit}
```

**Query Parameters**:
- `q` - Search query with syntax support
- `page` - Page number (default: 1)
- `limit` - Results per page (default: 20, max: 100)

**Response**:
```json
{
  "data": [
    {
      "name": "string",
      "description": "string",
      "lob": "string",
      "type": "string",
      "members": ["string"],
      "memberGroups": ["string"],
      "resolvedMembers": ["string"]
    }
  ],
  "total": 0,
  "page": 1,
  "limit": 20,
  "hasMore": false
}
```

#### Search Users
```http
GET /api/repo/:id/search/users?q={query}&page={page}&limit={limit}
```

**Response**:
```json
{
  "data": [
    {
      "username": "string",
      "displayName": "string",
      "email": "string",
      "lob": "string",
      "groups": ["string"]
    }
  ],
  "total": 0,
  "page": 1,
  "limit": 20,
  "hasMore": false
}
```

#### Search Suggestions
```http
GET /api/repo/:id/search/suggestions?type={groups|users}&q={query}
```

**Response**:
```json
["suggestion1", "suggestion2", "suggestion3"]
```

### Search Index Management

#### Reindex Repository
```http
POST /api/repo/:id/search/reindex
```

**Response**:
```json
{
  "status": "started",
  "message": "Reindexing started"
}
```

#### Get Index Status
```http
GET /api/repo/:id/search/status
```

**Response**:
```json
{
  "status": "ready|indexing|error",
  "lastIndexed": "2024-01-01T00:00:00Z",
  "documentCount": 1000,
  "indexSize": "10MB"
}
```

## Report Management APIs

### Report Presets

#### List Report Presets
```http
GET /api/repo/:id/report-presets
```

**Response**:
```json
[
  {
    "id": "string",
    "name": "string",
    "description": "string",
    "reportType": "groups|users",
    "query": "string",
    "schedule": {
      "enabled": false,
      "cronExpression": "string",
      "timezone": "string"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
]
```

#### Create Report Preset
```http
POST /api/repo/:id/report-presets
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "reportType": "groups|users",
  "query": "string",
  "schedule": {
    "enabled": false,
    "cronExpression": "0 0 * * *",
    "timezone": "UTC"
  }
}
```

#### Update Report Preset
```http
PUT /api/repo/:id/report-presets/:presetId
```

#### Delete Report Preset
```http
DELETE /api/repo/:id/report-presets/:presetId
```

### Report Generation

#### Generate Report
```http
POST /api/repo/:id/reports/generate
Content-Type: application/json

{
  "presetId": "string",
  "format": "json|csv"
}
```

**Response**:
```json
{
  "reportId": "string",
  "downloadUrl": "/api/repo/:id/reports/:reportId/download",
  "filename": "string",
  "size": 1024,
  "format": "json|csv",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### List Reports
```http
GET /api/repo/:id/reports?limit=50&offset=0
```

**Response**:
```json
{
  "data": [
    {
      "id": "string",
      "name": "string",
      "presetId": "string",
      "filename": "string",
      "size": 1024,
      "format": "json|csv",
      "downloadUrl": "string",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "limit": 50,
  "offset": 0
}
```

#### Download Report
```http
GET /api/repo/:id/reports/:reportId/download
```

**Response**: File download with appropriate Content-Type

## Usage Tracking APIs

### Usage Sources

#### List Usage Sources
```http
GET /api/usage-sources
```

**Response**:
```json
[
  {
    "id": "string",
    "name": "string",
    "type": "git|api|file",
    "scanFrequency": 3600,
    "enabled": true,
    "lastScan": "2024-01-01T00:00:00Z",
    "gitConfig": {
      "repoURL": "string",
      "branch": "string",
      "authType": "none|token|ssh",
      "token": "string"
    },
    "apiConfig": {
      "endpoint": "string",
      "method": "GET",
      "authType": "none|bearer|basic",
      "headers": {}
    },
    "fileConfig": {
      "basePath": "string",
      "patterns": ["*.yml", "*.yaml"],
      "excludePatterns": ["node_modules/*"]
    }
  }
]
```

#### Create Usage Source
```http
POST /api/usage-sources
Content-Type: application/json

{
  "name": "string",
  "type": "git|api|file",
  "scanFrequency": 3600,
  "enabled": true,
  "gitConfig": {
    "repoURL": "string",
    "branch": "string",
    "authType": "none|token|ssh",
    "token": "string"
  }
}
```

#### Update Usage Source
```http
PUT /api/usage-sources/:id
```

#### Delete Usage Source
```http
DELETE /api/usage-sources/:id
```

### Group Usage Scanning

#### Start Usage Scan
```http
POST /api/repo/:id/usage/scan
Content-Type: application/json

{
  "groupName": "string",
  "sourceIds": ["string"]  // Optional: specific sources to scan
}
```

**Response**:
```json
{
  "scanId": "string",
  "status": "started",
  "groupName": "string",
  "totalSources": 3,
  "startTime": "2024-01-01T00:00:00Z"
}
```

#### Get Scan Status
```http
GET /api/repo/:id/usage/scan/:scanId/status
```

**Response**:
```json
{
  "scanId": "string",
  "status": "running|completed|failed|cancelled",
  "groupName": "string",
  "progress": {
    "current": 2,
    "total": 5,
    "percentage": 40,
    "currentFile": "string",
    "speed": "10 files/sec",
    "eta": "2 minutes"
  },
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-01-01T00:05:00Z",
  "duration": 300,
  "errorMessage": "string"
}
```

#### Get Scan Results
```http
GET /api/repo/:id/usage/scan/:scanId/results
```

**Response**:
```json
{
  "scanId": "string",
  "groupName": "string",
  "results": [
    {
      "sourceId": "string",
      "sourceName": "string",
      "filePath": "string",
      "lineNumber": 42,
      "context": "string",
      "matchType": "exact|partial",
      "detectedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "summary": {
    "totalMatches": 10,
    "sourceCount": 3,
    "fileCount": 8
  }
}
```

## Scheduler APIs

### Task Management

#### List Active Tasks
```http
GET /api/scheduler/tasks/active
```

**Response**:
```json
[
  {
    "id": "string",
    "type": "sync|scan|report|index",
    "name": "string",
    "status": "running|paused|completed|failed",
    "progress": {
      "current": 50,
      "total": 100,
      "percentage": 50,
      "currentStep": "string",
      "eta": "5 minutes"
    },
    "startTime": "2024-01-01T00:00:00Z",
    "estimatedCompletion": "2024-01-01T00:05:00Z",
    "details": {}
  }
]
```

#### Get Task History
```http
GET /api/scheduler/tasks/history?limit=50&offset=0&type=sync
```

#### Task Control Operations

##### Pause Task
```http
POST /api/scheduler/tasks/:id/pause
```

##### Resume Task
```http
POST /api/scheduler/tasks/:id/resume
```

##### Cancel Task
```http
POST /api/scheduler/tasks/:id/cancel
```

##### Delete Task
```http
DELETE /api/scheduler/tasks/:id
```

### System Health

#### Get System Health
```http
GET /api/scheduler/health
```

**Response**:
```json
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": [
    {
      "name": "repository_manager",
      "status": "healthy|unhealthy",
      "lastCheck": "2024-01-01T00:00:00Z",
      "details": {}
    }
  ],
  "metrics": {
    "activeTasks": 3,
    "completedTasks": 150,
    "failedTasks": 2,
    "systemLoad": 0.75,
    "memoryUsage": 0.60
  }
}
```

## WebSocket APIs

### Real-time Progress Updates

#### Repository Progress
```
WS /ws/progress/:repoId
```

**Message Types**:
```json
{
  "type": "sync_progress",
  "repoId": "string",
  "progress": {
    "current": 50,
    "total": 100,
    "percentage": 50,
    "currentStep": "string"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}

{
  "type": "scan_progress",
  "scanId": "string",
  "progress": {
    "current": 25,
    "total": 100,
    "percentage": 25,
    "currentFile": "string"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Admin Task Monitoring
```
WS /ws/admin/tasks
```

#### System-wide Scan Monitoring
```
WS /ws/admin/scans
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "name",
      "reason": "Name is required"
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR` - Input validation failed
- `NOT_FOUND` - Resource not found
- `INTERNAL_ERROR` - Server-side error
- `REPOSITORY_ERROR` - Repository operation failed
- `SEARCH_ERROR` - Search operation failed
- `SCAN_ERROR` - Usage scan failed

### Rate Limiting
Currently not implemented, but headers would include:
- `X-RateLimit-Limit` - Request limit per window
- `X-RateLimit-Remaining` - Remaining requests
- `X-RateLimit-Reset` - Window reset time

## Authentication & Authorization

### Current Implementation
- No user authentication system
- Repository-based access control
- All operations require valid repository ID

### Future Considerations
- User authentication with JWT tokens
- Role-based access control (RBAC)
- API key authentication for external integrations
- Audit logging for all operations
