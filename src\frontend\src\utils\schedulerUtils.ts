/**
 * Utility functions for scheduler dashboard operations
 */

/**
 * Calculate the total number of pending tasks across all task types
 */
export const calculatePendingTasksCount = (
  plannedTasks?: any[],
  activeTasks?: any[],
  failedTasks?: any[]
): number => {
  return (plannedTasks?.length || 0) + (activeTasks?.length || 0) + (failedTasks?.length || 0)
}

/**
 * Format task queue tab label with count
 */
export const formatTaskQueueTabLabel = (
  plannedTasks?: any[],
  activeTasks?: any[],
  failedTasks?: any[]
): string => {
  const count = calculatePendingTasksCount(plannedTasks, activeTasks, failedTasks)
  return `Tasks Queue (${count} pending)`
}

/**
 * Check if scheduler data is available
 */
export const hasSchedulerData = (status: any, overview: any): boolean => {
  return Boolean(status && overview)
}
