import { Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { WebSocketConnectionStatus } from "@/components/common/ConnectionStatusIndicator"
import { formatRelativeTime } from "@/types/scheduler"

interface DashboardHeaderProps {
  title?: string
  lastRefreshTime?: Date | null
  // WebSocket connection props
  isConnected: boolean
  isConnecting: boolean
  wsError?: string | null
  onConnect: () => void
  onDisconnect: () => void
  // Action handlers
  onRefresh: () => void
  onCleanupStaleTasks: () => void
  onDeleteAllScheduledTasks: () => void
  refreshing?: boolean
}

export const DashboardHeader = ({
  title = "Scheduler Dashboard",
  lastRefreshTime,
  isConnected,
  isConnecting,
  wsError,
  onConnect,
  onDisconnect,
  onRefresh,
  onCleanupStaleTasks,
  onDeleteAllScheduledTasks,
  refreshing = false
}: DashboardHeaderProps) => {
  return (
    <div className="flex justify-between items-center">
      <h1 className="text-3xl font-bold">{title}</h1>
      <div className="flex items-center space-x-4">
        {/* Real-time monitoring connection status */}
        <WebSocketConnectionStatus
          isConnected={isConnected}
          isConnecting={isConnecting}
          error={wsError}
          onConnect={onConnect}
          onDisconnect={onDisconnect}
        />

        <div className="flex items-center space-x-2">
          {lastRefreshTime && (
            <span className="text-sm text-gray-500">
              Last updated: {formatRelativeTime(lastRefreshTime.toISOString())}
            </span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCleanupStaleTasks}
            className="text-orange-600 border-orange-300 hover:bg-orange-50"
          >
            <AlertCircle className="h-4 w-4 mr-2" />
            Cleanup Stale
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onDeleteAllScheduledTasks}
            className="text-red-600 border-red-300 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete All Scheduled
          </Button>
        </div>
      </div>
    </div>
  )
}
