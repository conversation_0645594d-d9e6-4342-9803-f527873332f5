# Frontend API Usage Analysis

## Overview

This document analyzes how the frontend consumes backend APIs, including request patterns, data flow, error handling, and integration points. It serves as a reference for understanding the client-server contract.

## API Client Architecture

### Base Configuration
**Location**: `src/api/client.ts`

```typescript
const API_CONFIG = {
  BASE_URL: '/api',
  TIMEOUT: 30000, // 30 seconds
  WS_BASE_URL: '/ws', // WebSocket base URL
};
```

### Request Management
- **Request Deduplication**: Prevents duplicate API calls for the same endpoint
- **Request Cancellation**: Automatic cleanup on component unmount
- **Response Caching**: TTL-based caching for GET requests
- **Error Handling**: Consistent error response processing

## API Endpoints Usage

### 1. Repository Management APIs

#### Get Repository Configurations
```typescript
GET /api/repo/configurations
Response: Repository[]

interface Repository {
  id: string;
  name: string;
  url: string;
  branch: string;
  status: 'active' | 'inactive' | 'error';
  lastSync?: string;
  lastCommit?: string;
  pollFrequency: number;
}
```

#### Repository Status
```typescript
GET /api/repo/status/:id
Response: RepositoryStatus

interface RepositoryStatus {
  id: string;
  status: string;
  lastSync: string;
  lastCommit: string;
  hasChanges: boolean;
  syncInProgress: boolean;
}
```

#### Repository Operations
```typescript
POST /api/repo/configurations
PUT /api/repo/configurations/:id
DELETE /api/repo/configurations/:id
POST /api/repo/:id/sync
POST /api/repo/:id/duplicate
```

### 2. Search APIs

#### Group Search
```typescript
GET /api/repo/:id/search/groups?q={query}&page={page}&limit={limit}
Response: SearchResponse<Group>

interface Group {
  name: string;
  description: string;
  lob: string;
  type: string;
  members: string[];
  memberGroups: string[];
  resolvedMembers: string[];
}

interface SearchResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
```

#### User Search
```typescript
GET /api/repo/:id/search/users?q={query}&page={page}&limit={limit}
Response: SearchResponse<User>

interface User {
  username: string;
  displayName: string;
  email: string;
  lob: string;
  groups: string[];
}
```

#### Search Suggestions
```typescript
GET /api/repo/:id/search/suggestions?type={groups|users}&q={query}
Response: string[]
```

### 3. Report Management APIs

#### Report Presets
```typescript
GET /api/repo/:id/report-presets
POST /api/repo/:id/report-presets
PUT /api/repo/:id/report-presets/:presetId
DELETE /api/repo/:id/report-presets/:presetId

interface ReportPreset {
  id: string;
  name: string;
  description: string;
  reportType: 'groups' | 'users';
  query: string;
  schedule?: ScheduleConfig;
  createdAt: string;
  updatedAt: string;
}

interface ScheduleConfig {
  enabled: boolean;
  cronExpression: string;
  timezone: string;
}
```

#### Report Generation
```typescript
POST /api/repo/:id/reports/generate
Body: {
  presetId: string;
  format: 'json' | 'csv';
}
Response: {
  reportId: string;
  downloadUrl: string;
  filename: string;
  size: number;
}
```

#### Report Listing
```typescript
GET /api/repo/:id/reports
Response: Report[]

interface Report {
  id: string;
  name: string;
  presetId: string;
  filename: string;
  size: number;
  createdAt: string;
  downloadUrl: string;
}
```

### 4. Usage Tracking APIs

#### Usage Sources
```typescript
GET /api/usage-sources
POST /api/usage-sources
PUT /api/usage-sources/:id
DELETE /api/usage-sources/:id

interface UsageSource {
  id: string;
  name: string;
  type: 'git' | 'api' | 'file';
  scanFrequency: number;
  enabled: boolean;
  gitConfig?: GitSourceConfig;
  apiConfig?: ApiSourceConfig;
  fileConfig?: FileSourceConfig;
}
```

#### Group Usage Scanning
```typescript
POST /api/repo/:id/usage/scan
Body: {
  groupName: string;
  sourceIds?: string[];
}
Response: {
  scanId: string;
  status: 'started';
}

GET /api/repo/:id/usage/scan/:scanId/status
Response: ScanStatus

interface ScanStatus {
  scanId: string;
  status: 'running' | 'completed' | 'failed';
  progress: Progress;
  results: UsageResult[];
}
```

### 5. Scheduler APIs

#### Active Tasks
```typescript
GET /api/scheduler/tasks/active
Response: ActiveTask[]

interface ActiveTask {
  id: string;
  type: string;
  status: 'running' | 'paused' | 'completed' | 'failed';
  progress: Progress;
  startTime: string;
  estimatedCompletion?: string;
  details: any;
}
```

#### Task Control
```typescript
POST /api/scheduler/tasks/:id/pause
POST /api/scheduler/tasks/:id/resume
POST /api/scheduler/tasks/:id/cancel
DELETE /api/scheduler/tasks/:id
```

#### System Health
```typescript
GET /api/scheduler/health
Response: SystemHealth

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: ServiceStatus[];
  metrics: SystemMetrics;
}
```

## WebSocket Integration

### Connection Management
```typescript
// WebSocket endpoints
WS /ws/progress/:repoId
WS /ws/admin/tasks
WS /ws/admin/scans
```

### Message Types
```typescript
interface ProgressMessage {
  type: 'progress';
  scanId: string;
  progress: Progress;
  timestamp: string;
}

interface TaskUpdateMessage {
  type: 'task_update';
  taskId: string;
  status: string;
  progress: Progress;
}

interface SystemMessage {
  type: 'system';
  level: 'info' | 'warning' | 'error';
  message: string;
}
```

## Data Flow Patterns

### 1. Repository Selection Flow
```typescript
// 1. Load repositories on app start
const repositories = await api.get('/api/repo/configurations');

// 2. Set selected repository in context
setSelectedRepoId(repoId);

// 3. All subsequent API calls include repoId
const groups = await api.get(`/api/repo/${repoId}/search/groups`);
```

### 2. Search Flow
```typescript
// 1. User types in search input (debounced)
const handleSearch = debounce(async (query: string) => {
  // 2. Get suggestions
  const suggestions = await api.get(`/api/repo/${repoId}/search/suggestions`, {
    params: { type: 'groups', q: query }
  });
  
  // 3. Execute search
  const results = await api.get(`/api/repo/${repoId}/search/groups`, {
    params: { q: query, page: 1, limit: 20 }
  });
}, 300);
```

### 3. Real-time Progress Flow
```typescript
// 1. Start operation (scan, sync, etc.)
const { scanId } = await api.post(`/api/repo/${repoId}/usage/scan`, {
  groupName: 'example-group'
});

// 2. Subscribe to WebSocket updates
const ws = new WebSocket(`/ws/progress/${repoId}`);
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  if (message.scanId === scanId) {
    updateProgress(message.progress);
  }
};

// 3. Handle completion
if (message.progress.status === 'completed') {
  const results = await api.get(`/api/repo/${repoId}/usage/scan/${scanId}/status`);
}
```

## Error Handling Patterns

### API Error Types
```typescript
interface ApiErrorResponse {
  error: string;
  code?: string;
  details?: any;
}

class ApiError extends Error {
  status: number;
  data: any;
  
  constructor(message: string, status: number, data?: any) {
    super(message);
    this.status = status;
    this.data = data;
  }
}
```

### Error Handling Strategy
```typescript
// 1. Global error handling in API client
const handleApiError = (error: any): never => {
  if (error.response) {
    // Server responded with error status
    throw new ApiError(
      error.response.data.error || 'Server error',
      error.response.status,
      error.response.data
    );
  } else if (error.request) {
    // Network error
    throw new ApiError('Network error', 0);
  } else {
    // Other error
    throw new ApiError(error.message, 0);
  }
};

// 2. Component-level error handling
try {
  const data = await api.getData();
  setData(data);
} catch (error) {
  if (error instanceof ApiError) {
    if (error.status === 404) {
      setError('Data not found');
    } else if (error.status >= 500) {
      setError('Server error, please try again');
    } else {
      setError(error.message);
    }
  } else {
    setError('An unexpected error occurred');
  }
}
```

## Caching Strategy

### Cache Implementation
```typescript
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

class ApiCache {
  private cache = new Map<string, CacheEntry>();
  
  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (entry && Date.now() - entry.timestamp < entry.ttl) {
      return entry.data;
    }
    this.cache.delete(key);
    return null;
  }
  
  set(key: string, data: any, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
}
```

### Cache Policies
- **Repository configurations**: 5 minutes TTL
- **Search results**: 2 minutes TTL
- **Report presets**: 10 minutes TTL
- **Usage sources**: 5 minutes TTL
- **No caching**: Real-time data (progress, active tasks)

## Request Optimization

### Request Deduplication
```typescript
const activeRequests = new Map<string, AbortController>();

const makeRequest = async (endpoint: string, options: RequestOptions) => {
  // Cancel existing request for same endpoint
  if (activeRequests.has(endpoint)) {
    activeRequests.get(endpoint)?.abort();
  }
  
  // Create new abort controller
  const controller = new AbortController();
  activeRequests.set(endpoint, controller);
  
  try {
    const response = await fetch(endpoint, {
      ...options,
      signal: controller.signal
    });
    return response;
  } finally {
    activeRequests.delete(endpoint);
  }
};
```

### Pagination Handling
```typescript
interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

const usePaginatedData = <T>(endpoint: string) => {
  const [data, setData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  
  const loadMore = async () => {
    const response = await api.get<PaginatedResponse<T>>(endpoint, {
      params: { page, limit: 20 }
    });
    
    setData(prev => [...prev, ...response.data]);
    setHasMore(response.hasMore);
    setPage(prev => prev + 1);
  };
  
  return { data, hasMore, loadMore };
};
```

## Integration Points

### 1. Repository Context Integration
- All API calls respect selected repository
- Repository changes trigger data refresh
- Repository status updates via WebSocket

### 2. Search Integration
- Debounced search inputs
- Auto-suggestions with caching
- Query syntax validation
- Result pagination

### 3. Real-time Integration
- WebSocket connection management
- Progress update broadcasting
- Connection status indicators
- Automatic reconnection

### 4. Form Integration
- React Hook Form validation
- Optimistic updates
- Error state management
- Loading state handling

## Security Considerations

### 1. Request Security
- CSRF protection (if implemented)
- Request validation
- Input sanitization
- Rate limiting awareness

### 2. Data Validation
- Response schema validation
- Type safety with TypeScript
- Error response handling
- Malformed data protection

### 3. Authentication
- Session management
- Token handling (if implemented)
- Automatic logout on auth errors
- Secure storage practices
