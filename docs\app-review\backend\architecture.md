# Backend Architecture Analysis

## Overview

The ADGitOps UI backend is a Go-based application following a layered architecture pattern with clear separation of concerns. It provides RESTful APIs for repository management, search functionality, report generation, and real-time monitoring capabilities.

## Technology Stack

### Core Technologies
- **Go 1.21+** - Primary programming language
- **Gin Web Framework** - HTTP router and middleware
- **Bleve** - Full-text search engine
- **WebSocket** - Real-time communication
- **JSON** - Data serialization format

### External Dependencies
- **GitLab/Bitbucket APIs** - Repository synchronization
- **File System** - Local repository storage and data persistence
- **Cron Scheduler** - Background task scheduling
- **go-cache** - In-memory caching

### Storage
- **File-based Storage** - JSON files for configuration and data
- **Local Git Repositories** - Cloned repository data
- **Search Indexes** - Bleve indexes for fast searching
- **Report Files** - Generated reports in various formats

## Architecture Patterns

### Layered Architecture
```
┌─────────────────────────────────────┐
│           API Layer                 │
│  (Controllers, Middleware, Routes)  │
├─────────────────────────────────────┤
│         Service Layer               │
│    (Business Logic, Orchestration)  │
├─────────────────────────────────────┤
│        Repository Layer             │
│     (Data Access, Persistence)      │
├─────────────────────────────────────┤
│         Model Layer                 │
│    (Data Structures, Validation)    │
└─────────────────────────────────────┘
```

### Directory Structure
```
src/backend/
├── main.go                 # Application entry point
├── api/                    # API layer
│   ├── server.go          # HTTP server setup
│   └── middleware/        # HTTP middleware
├── controllers/           # Request handlers
│   ├── data_controller.go
│   ├── repository_controller.go
│   ├── scheduler_controller.go
│   ├── usage_controller.go
│   └── bleve_search_controller.go
├── services/              # Business logic
│   ├── data_processor.go
│   ├── repository_manager.go
│   ├── scheduler_service.go
│   ├── usage_source_manager.go
│   ├── websocket_hub.go
│   └── search/
├── repository/            # Data access layer
│   └── interfaces.go
├── models/                # Data models
│   ├── repository.go
│   ├── group.go
│   ├── user.go
│   ├── report.go
│   ├── usage.go
│   └── scheduler.go
├── configs/               # Configuration
└── interfaces/            # Interface definitions
```

## Core Components

### 1. API Server (`api/server.go`)
**Responsibilities**:
- HTTP server initialization and configuration
- Route registration and middleware setup
- Static file serving for frontend
- WebSocket endpoint management
- Graceful shutdown handling

**Key Features**:
- Gin-based HTTP router
- CORS middleware for cross-origin requests
- Request logging and error handling
- WebSocket hub integration
- Static file serving with fallback routing

### 2. Controllers Layer
**Pattern**: Each controller handles a specific domain area

#### Repository Controller (`controllers/repository_controller.go`)
- Repository configuration management
- Repository synchronization operations
- Status monitoring and logging
- Repository duplication functionality

#### Data Controller (`controllers/data_controller.go`)
- Group and user data processing
- Report generation and management
- Report preset operations
- Data export functionality

#### Search Controller (`controllers/bleve_search_controller.go`)
- Full-text search implementation
- Index management and optimization
- Query parsing and execution
- Search suggestions and auto-complete

#### Usage Controller (`controllers/usage_controller.go`)
- Usage source management
- Group usage scanning operations
- Scan progress monitoring
- Usage result aggregation

#### Scheduler Controller (`controllers/scheduler_controller.go`)
- Background task management
- Task scheduling and execution
- System health monitoring
- Task control operations (pause, resume, cancel)

### 3. Services Layer
**Pattern**: Business logic encapsulation with dependency injection

#### Repository Manager (`services/repository_manager.go`)
- Git repository operations (clone, pull, status)
- Repository configuration management
- Synchronization orchestration
- Change detection and logging

#### Data Processor (`services/data_processor.go`)
- JSON file parsing and processing
- Group membership resolution
- Report generation with multiple formats
- Data caching and optimization
- Migration utilities

#### Scheduler Service (`services/scheduler_service.go`)
- Cron-based task scheduling
- Background job execution
- Task lifecycle management
- Resource management and cleanup

#### WebSocket Hub (`services/websocket_hub.go`)
- WebSocket connection management
- Real-time message broadcasting
- Client subscription handling
- Connection cleanup and error recovery

#### Usage Source Manager (`services/usage_source_manager.go`)
- Usage source configuration
- Multi-source scanning coordination
- Source-specific handlers (Git, API, File)
- Scan result aggregation

### 4. Repository Layer
**Pattern**: Data access abstraction

#### Interfaces (`repository/interfaces.go`)
- Repository pattern implementation
- Data access interface definitions
- Storage abstraction layer
- Testability through interface mocking

### 5. Models Layer
**Pattern**: Domain model definitions with validation

#### Core Models
- `Repository` - Repository configuration and status
- `Group` - Group data structure with membership
- `User` - User information and group associations
- `Report` - Report metadata and generation config
- `UsageSource` - Usage tracking source configuration
- `ScheduledTask` - Background task definition

## Design Patterns

### 1. Dependency Injection
```go
// Service initialization with dependencies
func NewDataController(
    dataProcessor *services.DataProcessor,
    repoManager *services.RepositoryManager,
    schedulerService *services.SchedulerService,
) *DataController {
    return &DataController{
        dataProcessor:    dataProcessor,
        repoManager:     repoManager,
        schedulerService: schedulerService,
    }
}
```

### 2. Interface Segregation
```go
// Separate interfaces for different concerns
type RepositoryReader interface {
    GetRepository(id string) (*models.Repository, error)
    ListRepositories() ([]*models.Repository, error)
}

type RepositoryWriter interface {
    SaveRepository(repo *models.Repository) error
    DeleteRepository(id string) error
}
```

### 3. Observer Pattern (WebSocket)
```go
// WebSocket hub for broadcasting updates
type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
}

func (h *Hub) Run() {
    for {
        select {
        case client := <-h.register:
            h.clients[client] = true
        case message := <-h.broadcast:
            for client := range h.clients {
                select {
                case client.send <- message:
                default:
                    close(client.send)
                    delete(h.clients, client)
                }
            }
        }
    }
}
```

### 4. Strategy Pattern (Source Handlers)
```go
// Different strategies for different source types
type SourceHandler interface {
    ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error)
    ValidateConfiguration() error
}

type GitSourceHandler struct {
    source *models.UsageSource
}

type ApiSourceHandler struct {
    source *models.UsageSource
}

type FileSourceHandler struct {
    source *models.UsageSource
}
```

## Data Flow Architecture

### 1. Request Processing Flow
```
HTTP Request → Middleware → Controller → Service → Repository → Model
                    ↓
HTTP Response ← JSON Serialization ← Business Logic ← Data Access ← Validation
```

### 2. Real-time Update Flow
```
Background Process → Service → WebSocket Hub → Connected Clients
                         ↓
Database/File Update → Cache Invalidation → Broadcast Update
```

### 3. Repository Synchronization Flow
```
Scheduler → Repository Manager → Git Operations → Data Processor → Search Index Update
                                      ↓
                              WebSocket Broadcast → Frontend Updates
```

## Concurrency Architecture

### 1. Goroutine Management
- WebSocket hub runs in dedicated goroutine
- Background tasks use worker pool pattern
- Repository synchronization uses bounded concurrency
- Scan operations use context-based cancellation

### 2. Synchronization Patterns
```go
// Mutex for thread-safe operations
type SyncLogger struct {
    logs  map[string][]models.LogEntry
    mutex sync.RWMutex
}

func (s *SyncLogger) AddLog(repoID, message string, level models.LogLevel) {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    // Safe concurrent access
}
```

### 3. Context-based Cancellation
```go
// Cancellable operations
func (s *SourceHandler) ScanForGroupUsage(ctx context.Context, groupName string) error {
    select {
    case <-ctx.Done():
        return ctx.Err()
    default:
        // Continue processing
    }
}
```

## Configuration Management

### 1. Application Configuration
```go
// Command-line flags with defaults
const (
    defaultPort        = "8080"
    defaultReportsDir  = "reports"
    defaultReposDir    = "../../repos"
    defaultConfigsFile = "configs/repositories.json"
    defaultPollSeconds = 300
    defaultStaticDir   = "static"
    defaultDataDir     = "data"
)
```

### 2. Repository Configuration
- JSON-based repository definitions
- Environment-specific settings
- Runtime configuration updates
- Configuration validation

### 3. Service Configuration
- Dependency injection setup
- Service lifecycle management
- Resource allocation
- Error handling configuration

## Error Handling Architecture

### 1. Error Types
```go
// Custom error types for different scenarios
type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

type ApiError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details any    `json:"details,omitempty"`
}
```

### 2. Error Propagation
- Structured error responses
- Error logging and monitoring
- Graceful degradation
- Client-friendly error messages

### 3. Recovery Mechanisms
- Panic recovery middleware
- Circuit breaker patterns
- Retry logic for external services
- Fallback strategies

## Security Architecture

### 1. Input Validation
- Request parameter validation
- JSON schema validation
- File path sanitization
- Query injection prevention

### 2. Access Control
- Repository-based access control
- Operation-level permissions
- Resource isolation
- Audit logging

### 3. Data Protection
- Secure file operations
- Git credential management
- Sensitive data handling
- Error message sanitization

## Performance Architecture

### 1. Caching Strategy
```go
// Multi-level caching
const (
    CacheKeyGroups      = "groups_%s"
    CacheKeyPresets     = "presets"
    CacheKeyReports     = "reports"
)

// TTL-based cache invalidation
cache := cache.New(5*time.Minute, 10*time.Minute)
```

### 2. Resource Management
- Connection pooling for external APIs
- Memory management for large datasets
- Disk space monitoring
- CPU usage optimization

### 3. Scalability Considerations
- Stateless service design
- Horizontal scaling readiness
- Load balancing compatibility
- Resource cleanup patterns

## Monitoring and Observability

### 1. Logging Strategy
- Structured logging with levels
- Request/response logging
- Error tracking and alerting
- Performance metrics

### 2. Health Checks
- Service health endpoints
- Dependency health monitoring
- Resource utilization tracking
- Automated recovery procedures

### 3. Metrics Collection
- API response times
- Error rates and types
- Resource usage patterns
- Business metrics tracking

## Deployment Architecture

### 1. Build Configuration
- Go module management
- Cross-platform compilation
- Static binary generation
- Dependency vendoring

### 2. Runtime Environment
- Container-ready design
- Environment variable configuration
- Signal handling for graceful shutdown
- Resource limit awareness

### 3. Integration Points
- Frontend static file serving
- External API integrations
- File system dependencies
- Network service discovery
