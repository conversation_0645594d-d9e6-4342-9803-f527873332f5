package services

import (
	"testing"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
)

func TestAutoScanMatchesPattern(t *testing.T) {
	service := &AutoScanService{}

	tests := []struct {
		name      string
		groupName string
		pattern   string
		expected  bool
	}{
		{
			name:      "FM prefix wildcard match",
			groupName: "FM-Argo-Dev-Admin",
			pattern:   "FM-*",
			expected:  true,
		},
		{
			name:      "FM prefix wildcard no match",
			groupName: "DGB-BFS-Standard-User",
			pattern:   "FM-*",
			expected:  false,
		},
		{
			name:      "Admin suffix wildcard match",
			groupName: "FM-Argo-Dev-Admin",
			pattern:   "*Admin",
			expected:  true,
		},
		{
			name:      "Admin suffix wildcard no match",
			groupName: "FM-Argo-Dev-View",
			pattern:   "*Admin",
			expected:  false,
		},
		{
			name:      "Contains wildcard match",
			groupName: "DGB-BFS-Business-Super-User",
			pattern:   "*Super*",
			expected:  true,
		},
		{
			name:      "Exact match",
			groupName: "FM-Argo-Dev-Admin",
			pattern:   "FM-Argo-Dev-Admin",
			expected:  true,
		},
		{
			name:      "Exact no match",
			groupName: "FM-Argo-Dev-Admin",
			pattern:   "FM-Argo-Stable-Admin",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.matchesPattern(tt.groupName, tt.pattern)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestEnsureRuleDefaults(t *testing.T) {
	service := &AutoScanService{}

	tests := []struct {
		name     string
		rules    []models.PriorityRule
		expected []models.PriorityRule
	}{
		{
			name: "rules with operators",
			rules: []models.PriorityRule{
				{
					ID:       "rule1",
					Pattern:  "FM-*",
					Weight:   100,
					Enabled:  true,
					Operator: models.PriorityRuleExclusive,
				},
			},
			expected: []models.PriorityRule{
				{
					ID:       "rule1",
					Pattern:  "FM-*",
					Weight:   100,
					Enabled:  true,
					Operator: models.PriorityRuleExclusive,
				},
			},
		},
		{
			name: "rules without operators (backward compatibility)",
			rules: []models.PriorityRule{
				{
					ID:      "rule1",
					Pattern: "FM-*",
					Weight:  100,
					Enabled: true,
					// Operator is empty string (default value)
				},
			},
			expected: []models.PriorityRule{
				{
					ID:       "rule1",
					Pattern:  "FM-*",
					Weight:   100,
					Enabled:  true,
					Operator: models.PriorityRuleInclude, // Should be set to default
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.ensureRuleDefaults(tt.rules)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestApplyPriorityRules(t *testing.T) {
	service := &AutoScanService{}

	// Mock groups for testing
	groups := []models.Group{
		{Groupname: "FM-Argo-Dev-Admin"},
		{Groupname: "FM-Argo-Stable-Admin"},
		{Groupname: "DGB-BFS-Standard-User"},
		{Groupname: "DGB-BFS-Business-Super-User"},
		{Groupname: "BI-Business-Standard-Users"},
	}

	tests := []struct {
		name           string
		priorityConfig models.PriorityConfig
		expectedGroups []string
	}{
		{
			name: "disabled priority config",
			priorityConfig: models.PriorityConfig{
				Enabled: false,
			},
			expectedGroups: []string{
				"FM-Argo-Dev-Admin",
				"FM-Argo-Stable-Admin",
				"DGB-BFS-Standard-User",
				"DGB-BFS-Business-Super-User",
				"BI-Business-Standard-Users",
			},
		},
		{
			name: "exclusive FM groups only",
			priorityConfig: models.PriorityConfig{
				Enabled:       true,
				DefaultWeight: 50,
				Rules: []models.PriorityRule{
					{
						ID:       "fm-exclusive",
						Pattern:  "FM-*",
						Weight:   100,
						Enabled:  true,
						Operator: models.PriorityRuleExclusive,
					},
				},
			},
			expectedGroups: []string{
				"FM-Argo-Dev-Admin",
				"FM-Argo-Stable-Admin",
			},
		},
		{
			name: "exclude Super groups",
			priorityConfig: models.PriorityConfig{
				Enabled:       true,
				DefaultWeight: 50,
				Rules: []models.PriorityRule{
					{
						ID:       "exclude-super",
						Pattern:  "*Super*",
						Weight:   100,
						Enabled:  true,
						Operator: models.PriorityRuleExclude,
					},
				},
			},
			expectedGroups: []string{
				"FM-Argo-Dev-Admin",
				"FM-Argo-Stable-Admin",
				"DGB-BFS-Standard-User",
				"BI-Business-Standard-Users",
			},
		},
		{
			name: "required FM groups with exclude Super",
			priorityConfig: models.PriorityConfig{
				Enabled:       true,
				DefaultWeight: 50,
				Rules: []models.PriorityRule{
					{
						ID:       "exclude-super",
						Pattern:  "*Super*",
						Weight:   50,
						Enabled:  true,
						Operator: models.PriorityRuleExclude,
					},
					{
						ID:       "require-fm",
						Pattern:  "FM-*",
						Weight:   100,
						Enabled:  true,
						Operator: models.PriorityRuleRequired,
					},
				},
			},
			expectedGroups: []string{
				"FM-Argo-Dev-Admin",
				"FM-Argo-Stable-Admin",
				"DGB-BFS-Standard-User",
				"BI-Business-Standard-Users",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.applyPriorityRules(groups, tt.priorityConfig)

			// Extract group names for comparison
			resultNames := make([]string, len(result))
			for i, group := range result {
				resultNames[i] = group.Groupname
			}

			assert.ElementsMatch(t, tt.expectedGroups, resultNames)
		})
	}
}

func TestFMGroupsSchedulingIssue(t *testing.T) {
	// This test specifically addresses the user's issue where FM-* groups
	// were not being scheduled due to pattern matching problems
	service := &AutoScanService{}

	// Real FM groups from the system
	groups := []models.Group{
		{Groupname: "FM-Argo-Dev-Admin"},
		{Groupname: "FM-Argo-Stable-Admin"},
		{Groupname: "FM-ArtemisMQ-Dev-Admin"},
		{Groupname: "FM-Business-Super-Users"},
		{Groupname: "DGB-BFS-Standard-User"},
		{Groupname: "BI-Business-Standard-Users"},
	}

	// Test the user's configuration: FM-* pattern with include operator
	priorityConfig := models.PriorityConfig{
		Enabled:       true,
		DefaultWeight: 50,
		Rules: []models.PriorityRule{
			{
				ID:       "fm-groups",
				Name:     "FM Groups Priority",
				Pattern:  "FM-*",
				Weight:   100,
				Enabled:  true,
				Operator: models.PriorityRuleInclude,
			},
		},
	}

	result := service.applyPriorityRules(groups, priorityConfig)

	// All groups should be included (include operator doesn't exclude others)
	assert.Len(t, result, 6)

	// Verify FM groups are present
	fmGroups := []string{}
	for _, group := range result {
		if service.matchesPattern(group.Groupname, "FM-*") {
			fmGroups = append(fmGroups, group.Groupname)
		}
	}

	expectedFMGroups := []string{
		"FM-Argo-Dev-Admin",
		"FM-Argo-Stable-Admin",
		"FM-ArtemisMQ-Dev-Admin",
		"FM-Business-Super-Users",
	}

	assert.ElementsMatch(t, expectedFMGroups, fmGroups, "FM-* groups should be properly matched and included")

	// Test exclusive FM groups (only FM groups should be scheduled)
	exclusiveConfig := models.PriorityConfig{
		Enabled:       true,
		DefaultWeight: 50,
		Rules: []models.PriorityRule{
			{
				ID:       "fm-exclusive",
				Name:     "Only FM Groups",
				Pattern:  "FM-*",
				Weight:   100,
				Enabled:  true,
				Operator: models.PriorityRuleExclusive,
			},
		},
	}

	exclusiveResult := service.applyPriorityRules(groups, exclusiveConfig)

	// Only FM groups should be included
	assert.Len(t, exclusiveResult, 4)
	for _, group := range exclusiveResult {
		assert.True(t, service.matchesPattern(group.Groupname, "FM-*"),
			"Only FM groups should be included with exclusive rule, but found: %s", group.Groupname)
	}
}

func TestSchedulerWebSocketEvents(t *testing.T) {
	// This test verifies that scheduler events are properly formatted for WebSocket broadcasting
	// Note: This is a unit test for the event structure, not the actual WebSocket broadcasting

	// Test event creation for different scheduler actions
	testCases := []struct {
		name      string
		taskID    string
		taskType  string
		action    string
		message   string
		success   bool
		expectErr bool
	}{
		{
			name:     "successful task cancellation",
			taskID:   "task-123",
			taskType: "usage_scan",
			action:   "cancel",
			message:  "Task cancelled successfully",
			success:  true,
		},
		{
			name:     "failed task restart",
			taskID:   "task-456",
			taskType: "usage_scan",
			action:   "restart",
			message:  "Failed to restart task",
			success:  false,
		},
		{
			name:     "successful task removal",
			taskID:   "task-789",
			taskType: "usage_scan",
			action:   "remove",
			message:  "Task removed successfully",
			success:  true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Verify that the event structure would be correct
			// In a real implementation, this would test the actual WebSocket event creation

			expectedEventType := "scheduler_refresh"
			switch tc.action {
			case "cancel":
				expectedEventType = "task_cancelled"
			case "restart":
				expectedEventType = "task_restarted"
			case "remove":
				expectedEventType = "task_removed"
			}

			// Verify event type mapping
			assert.Equal(t, expectedEventType, getExpectedEventType(tc.action))

			// Verify required fields are present
			assert.NotEmpty(t, tc.taskID, "Task ID should not be empty")
			assert.NotEmpty(t, tc.taskType, "Task type should not be empty")
			assert.NotEmpty(t, tc.action, "Action should not be empty")
			assert.NotEmpty(t, tc.message, "Message should not be empty")
		})
	}
}

// Helper function to test event type mapping
func getExpectedEventType(action string) string {
	switch action {
	case "cancel":
		return "task_cancelled"
	case "restart":
		return "task_restarted"
	case "remove":
		return "task_removed"
	case "pause":
		return "task_paused"
	case "resume":
		return "task_resumed"
	default:
		return "scheduler_refresh"
	}
}
