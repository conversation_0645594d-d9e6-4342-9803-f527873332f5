# Frontend Architecture Analysis

## Overview

The ADGitOps UI frontend is a modern React application built with TypeScript, using Vite as the build tool and Tailwind CSS for styling. The application follows a component-based architecture with clear separation of concerns.

## Technology Stack

### Core Technologies
- **React 18.2.0** - Component-based UI library
- **TypeScript 5.2.2** - Type-safe JavaScript
- **Vite 5.1.0** - Fast build tool and dev server
- **Tailwind CSS 3.4.1** - Utility-first CSS framework

### UI Component Library
- **Radix UI** - Headless, accessible UI primitives
  - Alert Dialog, Dialog, Dropdown Menu, Label, Popover
  - Select, Separator, Slot, Switch, Tabs, Toast
- **shadcn/ui** - Pre-built components based on Radix UI
- **Lucide React** - Icon library
- **class-variance-authority** - Component variant management

### State Management & Data Flow
- **React Context API** - Global state management
- **React Hook Form** - Form state and validation
- **Custom Hooks** - Reusable stateful logic
- **WebSocket Integration** - Real-time updates

### Routing & Navigation
- **React Router DOM 6.22.1** - Client-side routing
- **Nested Routes** - Layout-based routing structure

## Architecture Patterns

### Component Organization
```
src/
├── components/
│   ├── ui/           # Reusable UI components (shadcn/ui)
│   ├── common/       # Shared business components
│   ├── layout/       # Layout components
│   ├── dashboard/    # Dashboard-specific components
│   ├── repository/   # Repository management components
│   ├── scheduler/    # Scheduler monitoring components
│   ├── settings/     # Settings page components
│   └── usage/        # Usage tracking components
├── pages/            # Page-level components
├── hooks/            # Custom React hooks
├── context/          # React Context providers
├── types/            # TypeScript type definitions
├── utils/            # Utility functions
├── api/              # API client and types
└── configs/          # Configuration constants
```

### Design Patterns

#### 1. Repository Pattern
- Centralized API client (`api/client.ts`)
- Consistent error handling and request cancellation
- Caching layer for GET requests
- Request deduplication

#### 2. Context Provider Pattern
- `RepositoryContext` for global repository state
- Repository change callbacks for component updates
- Centralized repository management

#### 3. Custom Hooks Pattern
- `useWebSocketProgress` - WebSocket connection management
- `useAdminWebSocket` - Admin-level WebSocket subscriptions
- Encapsulated stateful logic with cleanup

#### 4. Component Composition
- Layout components with slot-based content
- Reusable UI components with variant support
- Consistent prop interfaces

## State Management Architecture

### Global State (Context)
```typescript
interface RepositoryContextType {
  selectedRepoId: string;
  setSelectedRepoId: (id: string) => void;
  isLoading: boolean;
  repositories: Repository[];
  duplicateRepository: (id: string) => Promise<void>;
  refreshRepositories: (forceRefresh?: boolean) => Promise<boolean>;
  onRepositoryChange: (callback: RepositoryChangeCallback) => () => void;
}
```

### Local State Management
- Component-level state with `useState`
- Form state with React Hook Form
- Loading states and error handling
- Optimistic updates for better UX

### Real-time Updates
- WebSocket connections for progress monitoring
- Automatic reconnection with exponential backoff
- Client-side subscription management
- Progress broadcasting to multiple components

## API Integration

### Client Architecture
- Centralized API client with TypeScript types
- Request/response interceptors
- Error handling with custom `ApiError` class
- Request cancellation and cleanup
- Caching with TTL for GET requests

### Error Handling Strategy
- Consistent error boundaries
- Toast notifications for user feedback
- Graceful degradation for failed requests
- Retry mechanisms for critical operations

## Performance Optimizations

### Code Splitting
- Route-based code splitting
- Lazy loading of page components
- Dynamic imports for heavy components

### Rendering Optimizations
- React.memo for expensive components
- Callback memoization with useCallback
- Effect dependency optimization
- Debounced search inputs

### Asset Optimization
- Vite's built-in optimizations
- Tree shaking for unused code
- CSS purging with Tailwind
- Icon optimization with Lucide

## Build Configuration

### Vite Configuration
- React plugin for JSX support
- Path aliases for clean imports
- Proxy configuration for API and WebSocket
- Hot module replacement (HMR)
- Production optimizations

### TypeScript Configuration
- Strict type checking
- Path mapping for imports
- Build-specific configurations
- Node.js compatibility

## Development Workflow

### Code Quality Tools
- ESLint for code linting
- TypeScript for type safety
- Prettier for code formatting (implied)
- Hot reload for development

### Testing Strategy
- Cypress for end-to-end testing
- Component testing capabilities
- Integration test support

## Security Considerations

### Input Validation
- TypeScript type safety
- Form validation with React Hook Form
- API response validation
- XSS prevention through React's built-in escaping

### Authentication & Authorization
- Repository-based access control
- Session management through context
- Secure API communication

## Accessibility

### UI Accessibility
- Radix UI provides ARIA attributes
- Keyboard navigation support
- Screen reader compatibility
- Focus management

### Design System
- Consistent color schemes
- Proper contrast ratios
- Responsive design patterns
- Touch-friendly interfaces

## Browser Compatibility

### Modern Browser Support
- ES2020+ features
- CSS Grid and Flexbox
- WebSocket support
- Local Storage API

### Progressive Enhancement
- Graceful fallbacks for WebSocket failures
- Offline-capable caching
- Responsive design for mobile devices

## Deployment Architecture

### Build Output
- Static assets for CDN deployment
- Optimized bundle sizes
- Source maps for debugging
- Environment-specific configurations

### Runtime Configuration
- Environment variables
- API endpoint configuration
- Feature flags support
- Dynamic configuration loading
