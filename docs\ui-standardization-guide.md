# UI Standardization Guide: Active Task Displays

This document outlines the standardized user interface design system for active task displays across the ADGitOps UI application. All components that show running operations, progress indicators, or ongoing processes now follow consistent visual patterns based on the RealTimeScanProgress component.

## Overview

The standardization effort ensures that all active task monitoring interfaces look and behave similarly, providing a unified user experience across:

- Real-time scan progress displays
- Scheduler dashboard tasks
- Background operations monitoring
- Loading states and transitions
- Connection status indicators

## Core Components

### 1. UnifiedActiveTaskProgress

**Location**: `src/frontend/src/components/common/UnifiedActiveTaskProgress.tsx`

A standardized component for displaying active task progress with consistent styling and behavior.

**Key Features**:
- Unified task status badges (Running, Paused, Completed, Failed, Cancelled)
- Consistent progress bars using EnhancedProgress and CompactProgress
- Standardized connection status indicators
- Task control buttons (Pause, Resume, Cancel, Refresh)
- Both compact and full display modes
- Real-time toggle functionality

**Usage**:
```typescript
<UnifiedActiveTaskProgress
  task={taskData}
  connectionStatus="connected"
  showControls={true}
  realTimeEnabled={true}
  onPause={handlePause}
  onCancel={handleCancel}
/>
```

### 2. ConnectionStatusIndicator

**Location**: `src/frontend/src/components/common/ConnectionStatusIndicator.tsx`

Standardized connection status display for real-time monitoring interfaces.

**Variants**:
- `ConnectionStatusIndicator` - Full version with controls
- `WebSocketConnectionStatus` - Specialized for WebSocket connections
- `CompactConnectionStatus` - Minimal version for tight spaces
- `ConnectionStatusDot` - Just a status dot with tooltip

**Key Features**:
- Consistent status icons (Connected, Connecting, Disconnected, Error)
- Standardized color coding (Green=Connected, Blue=Connecting, Red=Disconnected/Error)
- Toggle controls for monitoring on/off
- Error display with alerts
- Refresh/reconnect functionality

### 3. StandardizedLoadingStates

**Location**: `src/frontend/src/components/common/StandardizedLoadingStates.tsx`

Consistent loading states and skeleton components for all active task displays.

**Components**:
- `StandardizedSpinner` - Consistent loading spinner
- `ActiveTaskLoadingSkeleton` - Loading state for task cards
- `StatusCardLoadingSkeleton` - Loading state for dashboard cards
- `MonitorCardLoadingSkeleton` - Loading state for monitoring interfaces
- `CenteredLoadingState` - Centered loading with custom messages
- `EmptyActiveTasksState` - Empty state for no active tasks
- `ListLoadingSkeleton` - Loading state for lists
- `InlineLoadingIndicator` - Small inline loading indicators

## Design Patterns

### Status Badge System

All status badges follow consistent color coding:
- **Running**: Blue background (`bg-blue-100 text-blue-800 border-blue-200`)
- **Paused**: Yellow background (`bg-yellow-100 text-yellow-800 border-yellow-200`)
- **Completed**: Green background (`bg-green-100 text-green-800 border-green-200`)
- **Failed**: Red background (`bg-red-100 text-red-800 border-red-200`)
- **Cancelled**: Gray background (`bg-gray-100 text-gray-800 border-gray-200`)

### Progress Bar Styling

All progress bars use the enhanced progress system:
- **Main Progress**: 3px height (`h-3`) with smooth transitions
- **Compact Progress**: 2px height (`h-2`) for smaller spaces
- **Color Progression**: Blue → Green as progress increases
- **Performance Metrics**: Speed, ETA, and file counts consistently displayed

### Connection Status Colors

Standardized connection status indicators:
- **Connected**: Green (`text-green-500`, `bg-green-500`)
- **Connecting**: Blue with animation (`text-blue-500`, `animate-spin`)
- **Disconnected**: Red (`text-red-500`, `bg-red-500`)
- **Error**: Red with alert icon (`text-red-600`)

### Loading State Patterns

All loading states follow consistent patterns:
- **Skeleton Loading**: Gray animated placeholders (`animate-pulse bg-muted`)
- **Spinner Loading**: Blue spinning icons (`text-blue-500 animate-spin`)
- **Card Loading**: Consistent skeleton structure for cards
- **Empty States**: Centered icons with descriptive text

## Updated Components

### ActiveTasksMonitor

**Location**: `src/frontend/src/components/scheduler/ActiveTasksMonitor.tsx`

**Changes**:
- Uses `MonitorCardLoadingSkeleton` for loading states
- Implements `WebSocketConnectionStatus` for connection display
- Uses `EmptyActiveTasksState` for empty states
- Consistent progress bar styling with `CompactProgress`

### GlobalScanMonitor

**Location**: `src/frontend/src/components/scheduler/GlobalScanMonitor.tsx`

**Changes**:
- Uses `ConnectionStatusDot` for compact connection status
- Implements `EmptyActiveTasksState` for empty states
- Consistent error handling with standardized alerts
- Unified scan status icons and colors

### Dashboard Status Components

**RepositoryStatus, IndexStatus, ScheduleStats**

**Changes**:
- All use `StatusCardLoadingSkeleton` for loading states
- Consistent refresh button styling with `StandardizedSpinner`
- Unified card structure and spacing
- Consistent error handling patterns

## Implementation Guidelines

### 1. Loading States

Always use standardized loading components:
```typescript
// Instead of custom skeletons
if (loading) {
  return <StatusCardLoadingSkeleton />;
}

// Instead of custom spinners
<StandardizedSpinner size="sm" />
```

### 2. Connection Status

Use appropriate connection status components:
```typescript
// For full monitoring interfaces
<WebSocketConnectionStatus
  isConnected={isConnected}
  isConnecting={isConnecting}
  error={error}
  onConnect={connect}
  onDisconnect={disconnect}
/>

// For compact displays
<ConnectionStatusDot
  isConnected={isConnected}
  isConnecting={isConnecting}
  error={error}
/>
```

### 3. Progress Displays

Use enhanced progress components consistently:
```typescript
// For detailed progress
<EnhancedProgress
  progress={progress}
  currentChunk={currentChunk}
  showDetails={true}
/>

// For compact spaces
<CompactProgress progress={progress} />
```

### 4. Empty States

Use standardized empty state components:
```typescript
<EmptyActiveTasksState 
  title="No Active Tasks"
  description="All tasks are currently idle."
  icon={Clock}
/>
```

## Benefits

1. **Consistency**: All active task displays look and behave similarly
2. **Maintainability**: Centralized components reduce code duplication
3. **User Experience**: Familiar patterns across the application
4. **Accessibility**: Consistent color coding and status indicators
5. **Performance**: Optimized loading states and transitions
6. **Scalability**: Easy to add new active task displays using existing patterns

## Migration Checklist

When updating existing components to use the standardized system:

- [ ] Replace custom loading states with `StandardizedLoadingStates` components
- [ ] Update connection status displays to use `ConnectionStatusIndicator` variants
- [ ] Implement consistent progress bars using `EnhancedProgress`/`CompactProgress`
- [ ] Use standardized status badges and color coding
- [ ] Replace custom empty states with `EmptyActiveTasksState`
- [ ] Ensure consistent error handling and display patterns
- [ ] Test real-time updates and WebSocket reconnection behavior
- [ ] Verify responsive design across different screen sizes

This standardization creates a cohesive and professional user interface that enhances the overall user experience while making the codebase more maintainable and scalable.
