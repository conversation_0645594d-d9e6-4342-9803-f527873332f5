// Navigation utilities for the application

/**
 * Generate a URL for a specific settings tab
 * @param tab - The settings tab to navigate to
 * @returns The URL with the tab parameter
 */
export const getSettingsTabUrl = (tab: 'general' | 'repository' | 'usage-sources' | 'scan-logs'): string => {
  return `/settings?tab=${tab}`
}

/**
 * Settings tab constants for type safety
 */
export const SETTINGS_TABS = {
  GENERAL: 'general' as const,
  REPOSITORY: 'repository' as const,
  USAGE_SOURCES: 'usage-sources' as const,
  SCAN_LOGS: 'scan-logs' as const,
} as const

export type SettingsTab = typeof SETTINGS_TABS[keyof typeof SETTINGS_TABS]
