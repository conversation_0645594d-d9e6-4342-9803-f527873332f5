package services

import (
	"os"
	"testing"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestScanStatusManager_GetAllActiveScans(t *testing.T) {
	// Create temporary directory for test
	tempDir, err := os.MkdirTemp("", "scan_status_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create scan status manager
	manager := NewScanStatusManager(tempDir)

	t.Run("EmptyManager", func(t *testing.T) {
		activeScans, err := manager.GetAllActiveScans()
		assert.NoError(t, err)
		assert.Empty(t, activeScans)
	})

	t.Run("WithActiveScans", func(t *testing.T) {
		// Add some test scan statuses
		activeStatus1 := models.UsageScanStatus{
			GroupName:        "test-group-1",
			RepoID:           "test-repo-1",
			SourcesTotal:     5,
			SourcesScanned:   2,
			InProgress:       true,
			LastScanTime:     time.Now(),
			CompletedSources: []string{"source1", "source2"},
			PendingSources:   []string{"source3", "source4", "source5"},
			FailedSources:    []models.SourceScanFailure{},
			TotalUsages:      10,
		}

		activeStatus2 := models.UsageScanStatus{
			GroupName:        "test-group-2",
			RepoID:           "test-repo-1",
			SourcesTotal:     3,
			SourcesScanned:   1,
			InProgress:       true,
			LastScanTime:     time.Now(),
			CompletedSources: []string{"source1"},
			PendingSources:   []string{"source2", "source3"},
			FailedSources:    []models.SourceScanFailure{},
			TotalUsages:      5,
		}

		completedStatus := models.UsageScanStatus{
			GroupName:        "test-group-3",
			RepoID:           "test-repo-1",
			SourcesTotal:     2,
			SourcesScanned:   2,
			InProgress:       false, // This one is completed
			LastScanTime:     time.Now(),
			CompletedSources: []string{"source1", "source2"},
			PendingSources:   []string{},
			FailedSources:    []models.SourceScanFailure{},
			TotalUsages:      8,
		}

		// Update statuses
		err := manager.UpdateStatus(activeStatus1)
		require.NoError(t, err)

		err = manager.UpdateStatus(activeStatus2)
		require.NoError(t, err)

		err = manager.UpdateStatus(completedStatus)
		require.NoError(t, err)

		// Get active scans
		activeScans, err := manager.GetAllActiveScans()
		assert.NoError(t, err)
		assert.Len(t, activeScans, 2) // Only the two active scans

		// Verify the active scans are correct
		foundGroup1 := false
		foundGroup2 := false
		for _, scan := range activeScans {
			assert.True(t, scan.InProgress)
			if scan.GroupName == "test-group-1" {
				foundGroup1 = true
				assert.Equal(t, "test-repo-1", scan.RepoID)
				assert.Equal(t, 5, scan.SourcesTotal)
				assert.Equal(t, 2, scan.SourcesScanned)
			} else if scan.GroupName == "test-group-2" {
				foundGroup2 = true
				assert.Equal(t, "test-repo-1", scan.RepoID)
				assert.Equal(t, 3, scan.SourcesTotal)
				assert.Equal(t, 1, scan.SourcesScanned)
			}
		}
		assert.True(t, foundGroup1, "Should find test-group-1")
		assert.True(t, foundGroup2, "Should find test-group-2")
	})

	t.Run("OnlyCompletedScans", func(t *testing.T) {
		// Create a new temporary directory for this test
		tempDir2, err := os.MkdirTemp("", "scan_status_test_completed")
		require.NoError(t, err)
		defer os.RemoveAll(tempDir2)

		// Create new manager with clean directory
		manager2 := NewScanStatusManager(tempDir2)

		// Add only completed scans
		completedStatus := models.UsageScanStatus{
			GroupName:        "completed-group",
			RepoID:           "test-repo",
			SourcesTotal:     2,
			SourcesScanned:   2,
			InProgress:       false,
			LastScanTime:     time.Now(),
			CompletedSources: []string{"source1", "source2"},
			PendingSources:   []string{},
			FailedSources:    []models.SourceScanFailure{},
			TotalUsages:      5,
		}

		err = manager2.UpdateStatus(completedStatus)
		require.NoError(t, err)

		// Should return empty list
		activeScans, err := manager2.GetAllActiveScans()
		assert.NoError(t, err)
		assert.Empty(t, activeScans)
	})
}

func TestScanStatusManager_GetAllActiveScans_Persistence(t *testing.T) {
	// Create temporary directory for test
	tempDir, err := os.MkdirTemp("", "scan_status_persistence_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create first manager and add active scan
	manager1 := NewScanStatusManager(tempDir)

	activeStatus := models.UsageScanStatus{
		GroupName:        "persistent-group",
		RepoID:           "persistent-repo",
		SourcesTotal:     3,
		SourcesScanned:   1,
		InProgress:       true,
		LastScanTime:     time.Now(),
		CompletedSources: []string{"source1"},
		PendingSources:   []string{"source2", "source3"},
		FailedSources:    []models.SourceScanFailure{},
		TotalUsages:      2,
	}

	err = manager1.UpdateStatus(activeStatus)
	require.NoError(t, err)

	// Create second manager (simulating restart)
	manager2 := NewScanStatusManager(tempDir)

	// Should load the active scan from disk
	activeScans, err := manager2.GetAllActiveScans()
	assert.NoError(t, err)
	assert.Len(t, activeScans, 1)

	scan := activeScans[0]
	assert.Equal(t, "persistent-group", scan.GroupName)
	assert.Equal(t, "persistent-repo", scan.RepoID)
	assert.True(t, scan.InProgress)
	assert.Equal(t, 3, scan.SourcesTotal)
	assert.Equal(t, 1, scan.SourcesScanned)
}
