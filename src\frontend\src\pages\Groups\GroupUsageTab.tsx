import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Loader2,
  Search,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Filter,
  X,
  ChevronRight,
  Settings,
  ExternalLink
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { apiClient } from '@/api/client';
import type {
  UsageResult,
  UsageResultList,
  UsageScanStatus,
  UsageScanRequest,
  SourceType
} from '@/types/usage';
import { ScanLogSummary } from '@/types/scanLogs';
import {
  getSourceTypeIcon,
  getSourceTypeColor,
  formatFileSize,
  formatTimeAgo,
  SOURCE_TYPES
} from '@/types/usage';
import { Group } from './GroupTypes';
import { Link, useNavigate } from 'react-router-dom';
import { RealTimeScanProgress } from '@/components/usage/RealTimeScanProgress';
import { ScanCompletionSummary } from '@/components/usage/ScanCompletionSummary';

interface GroupUsageTabProps {
  group: Group;
  repoId: string;
}

const GroupUsageTab: React.FC<GroupUsageTabProps> = ({ group, repoId }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [usageResults, setUsageResults] = useState<UsageResult[]>([]);
  const [scanStatus, setScanStatus] = useState<UsageScanStatus | null>(null);
  const [currentScanId, setCurrentScanId] = useState<string | null>(null);
  const [showRealTimeProgress, setShowRealTimeProgress] = useState(false);
  const [scanCompletionSummary, setScanCompletionSummary] = useState<ScanLogSummary | null>(null);
  const [showCompletionSummary, setShowCompletionSummary] = useState(false);
  const [page] = useState(1);
  const [pageSize] = useState(50);
  const [filters, setFilters] = useState<{
    sourceType?: SourceType;
    sourceId?: string;
    search?: string;
  }>({});

  // Load usage results and scan status on component mount
  useEffect(() => {
    loadUsageResults();
    loadScanStatus();
  }, [group.Groupname, repoId, page, filters]);

  // Check for active scan and restore real-time progress on component mount
  useEffect(() => {
    const checkAndRestoreActiveScan = async () => {
      try {
        const status = await apiClient.usageResults.getScanStatus(repoId, group.Groupname);
        if (status.inProgress) {
          // There's an active scan, restore real-time progress
          // Use consistent format matching backend (includes :active suffix)
          const scanId = `${group.Groupname}:${repoId}:active`;
          setCurrentScanId(scanId);
          setShowRealTimeProgress(true);
          setIsScanning(true);

          console.log(`GroupUsageTab: Restored real-time progress for active scan: ${scanId}`);
        }
      } catch (error) {
        // No active scan found, which is normal
      }
    };

    checkAndRestoreActiveScan();
  }, [group.Groupname, repoId]);

  // Poll scan status when scanning is in progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (scanStatus?.inProgress) {
      interval = setInterval(() => {
        loadScanStatus();
      }, 2000); // Poll every 2 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [scanStatus?.inProgress]);

  const loadUsageResults = async () => {
    try {
      setIsLoading(true);
      const response: UsageResultList = await apiClient.usageResults.getForGroup(
        repoId,
        group.Groupname,
        {
          page,
          pageSize,
          sourceType: filters.sourceType,
          sourceId: filters.sourceId,
        }
      );

      let results = response.results;

      // Apply client-side search filter if specified
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        results = results.filter(result =>
          result.filePath.toLowerCase().includes(searchTerm) ||
          result.context.toLowerCase().includes(searchTerm) ||
          result.sourceName.toLowerCase().includes(searchTerm)
        );
      }

      setUsageResults(results);
    } catch (error) {
      console.error('Failed to load usage results:', error);
      setUsageResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadScanStatus = async () => {
    try {
      const status = await apiClient.usageResults.getScanStatus(repoId, group.Groupname);
      setScanStatus(status);
      setIsScanning(status.inProgress);

      // If scan completed, reload results
      if (!status.inProgress && scanStatus?.inProgress) {
        loadUsageResults();
      }
    } catch (error) {
      // Scan status not found is expected for groups that haven't been scanned
      setScanStatus(null);
      setIsScanning(false);
    }
  };

  const handleManualScan = async () => {
    try {
      setIsScanning(true);

      // Clear any existing completion summary when starting a new scan
      setShowCompletionSummary(false);
      setScanCompletionSummary(null);

      const request: Omit<UsageScanRequest, 'groupName' | 'repoId'> = {
        force: true,
      };

      const response = await apiClient.usageResults.scanGroup(repoId, group.Groupname, request);

      // Use the scan ID returned by the backend for real-time tracking
      const scanId = response.scanId;
      setCurrentScanId(scanId);
      setShowRealTimeProgress(true);

      toast({
        title: 'Scan Started',
        description: `Usage scan started for group "${group.Groupname}" with real-time progress`,
      });

      // Still poll for status as fallback, but less frequently
      setTimeout(loadScanStatus, 5000);
    } catch (error) {
      console.error('Failed to start scan:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to start usage scan',
        variant: 'destructive',
      });
      setIsScanning(false);
      setShowRealTimeProgress(false);
    }
  };

  // Handle scan completion from real-time updates
  const handleScanComplete = (results: any) => {
    console.log('GroupUsageTab: Scan completion received:', results);
    setIsScanning(false);
    setShowRealTimeProgress(false);
    setCurrentScanId(null);

    // Store scan completion summary if available
    if (results && typeof results === 'object') {
      setScanCompletionSummary(results as ScanLogSummary);
      setShowCompletionSummary(true);

      // Auto-dismiss completion summary after 30 seconds
      setTimeout(() => {
        setShowCompletionSummary(false);
      }, 30000);
    }

    toast({
      title: 'Scan Completed',
      description: `Usage scan completed for group "${group.Groupname}"`,
    });

    // Reload results to show updated data
    console.log('GroupUsageTab: Reloading usage results after scan completion');
    loadUsageResults();
    loadScanStatus();
  };

  // Handle scan error from real-time updates
  const handleScanError = (error: string) => {
    setIsScanning(false);
    setShowRealTimeProgress(false);
    setCurrentScanId(null);

    // Check if this is a git fallback scenario (warning) or actual error
    const isGitFallback = error.includes('git_fallback') ||
                         error.includes('using local repository copy') ||
                         error.includes('Git operation failed but local repository exists');

    if (isGitFallback) {
      toast({
        title: 'Repository Sync Warning',
        description: 'Repository sync failed, but scan continued with local copy. Results may not reflect the latest changes.',
        variant: 'default', // Use default variant for warnings
      });
    } else {
      toast({
        title: 'Scan Failed',
        description: error.includes('git clone failed') || error.includes('git pull failed')
          ? 'Repository synchronization failed. Please check your network connection and repository access permissions.'
          : error,
        variant: 'destructive',
      });
    }
  };

  // Handle dismissing the scan completion summary
  const handleDismissCompletionSummary = () => {
    setShowCompletionSummary(false);
    setScanCompletionSummary(null);
  };

  // Extract related groups from usage results for navigation links
  const extractRelatedGroups = (results: UsageResult[]): string[] => {
    const groupNames = new Set<string>();
    const currentGroupLower = group.Groupname.toLowerCase();

    results.forEach(result => {
      // Extract group names from file paths and context
      const text = `${result.filePath} ${result.context}`.toLowerCase();

      // Simple regex to find potential group names (alphanumeric with underscores/hyphens)
      const groupMatches = text.match(/\b[a-z][a-z0-9_-]*[a-z0-9]\b/g) || [];

      groupMatches.forEach(match => {
        // Filter out common words and the current group
        if (match.length > 2 &&
            match !== currentGroupLower &&
            !['the', 'and', 'for', 'with', 'from', 'this', 'that', 'file', 'path', 'config', 'test'].includes(match)) {
          groupNames.add(match);
        }
      });
    });

    return Array.from(groupNames).slice(0, 10); // Limit to 10 related groups
  };

  // Handle navigation to a group page
  const handleGroupNavigation = (groupName: string) => {
    const params = new URLSearchParams();
    const searchValue = `groupname:"${groupName}"`;
    params.set('search', searchValue);
    params.set('page', '1');
    navigate(`/groups?${params.toString()}`);
  };

  const handleClearResults = async () => {
    try {
      await apiClient.usageResults.clearResults(repoId, group.Groupname);
      toast({
        title: 'Results Cleared',
        description: `Usage results cleared for group "${group.Groupname}"`,
      });
      setUsageResults([]);
      setScanStatus(null);
    } catch (error) {
      console.error('Failed to clear results:', error);
      toast({
        title: 'Clear Failed',
        description: 'Failed to clear usage results',
        variant: 'destructive',
      });
    }
  };



  const renderScanStatus = () => {
    if (!scanStatus) {
      return (
        <div className="flex items-center text-muted-foreground">
          <AlertCircle className="w-4 h-4 mr-2" />
          No scan completed yet
        </div>
      );
    }

    if (scanStatus.inProgress) {
      return (
        <div className="flex items-center text-blue-600">
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Scanning... ({scanStatus.sourcesScanned}/{scanStatus.sourcesTotal} sources)
        </div>
      );
    }

    const hasFailures = scanStatus.failedSources.length > 0;
    const completedSources = scanStatus.completedSources.length;
    const totalSources = scanStatus.sourcesTotal;

    return (
      <div className="flex items-center space-x-4">
        <div className={`flex items-center ${hasFailures ? 'text-yellow-600' : 'text-green-600'}`}>
          {hasFailures ? (
            <AlertCircle className="w-4 h-4 mr-2" />
          ) : (
            <CheckCircle className="w-4 h-4 mr-2" />
          )}
          Scan completed ({completedSources}/{totalSources} sources)
        </div>
        <div className="flex items-center text-muted-foreground">
          <Clock className="w-4 h-4 mr-1" />
          {formatTimeAgo(scanStatus.lastScanTime)}
        </div>
        <Badge variant="outline">
          {scanStatus.totalUsages} usage{scanStatus.totalUsages !== 1 ? 's' : ''} found
        </Badge>
      </div>
    );
  };

  // Helper function to highlight search terms in text with line-level highlighting
  const highlightSearchTerms = (text: string, searchTerm: string) => {
    if (!searchTerm || !text) return text;

    // Escape special regex characters but preserve the original term for matching
    const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedTerm})`, 'gi');

    // Split text into lines and process each line
    const lines = text.split('\n');
    const processedLines = lines.map(line => {
      if (regex.test(line)) {
        // Reset regex for this line
        const lineRegex = new RegExp(`(${escapedTerm})`, 'gi');
        const highlightedLine = line.replace(lineRegex, (match) => {
          // Use slightly darker green for the match text
          return `<span style="background-color: #bdf8d2ff; padding: 1px 3px; border-radius: 2px; font-weight: bold;">${match}</span>`;
        });
        // Wrap the entire line with light green background
        return `<span style="background-color: #dcfce7; display: block; padding: 2px 4px; margin: 1px 0; border-radius: 3px;">${highlightedLine}</span>`;
      }
      return line;
    });

    return processedLines.join('\n');
  };

  // Helper function to render highlighted context
  const renderHighlightedContext = (context: string) => {
    if (!context) return '';

    // Always highlight the group name, plus any additional search terms
    const searchTerms = [
      group.Groupname, // Always highlight the group name first
      ...(filters.search ? [filters.search] : []) // Add search filter if present
    ].filter(Boolean);

    let highlightedContext = context;
    searchTerms.forEach((term) => {
      if (term && term.trim()) {
        highlightedContext = highlightSearchTerms(highlightedContext, term.trim());
      }
    });

    return highlightedContext;
  };

  const renderUsageResult = (result: UsageResult) => {
    // Always highlight if group name is found, plus any additional search matches
    const hasGroupMatch = (
      result.filePath.toLowerCase().includes(group.Groupname.toLowerCase()) ||
      result.context.toLowerCase().includes(group.Groupname.toLowerCase()) ||
      result.sourceName.toLowerCase().includes(group.Groupname.toLowerCase())
    );

    const hasSearchMatch = filters.search && (
      result.filePath.toLowerCase().includes(filters.search.toLowerCase()) ||
      result.context.toLowerCase().includes(filters.search.toLowerCase()) ||
      result.sourceName.toLowerCase().includes(filters.search.toLowerCase())
    );

    const shouldHighlight = hasGroupMatch || hasSearchMatch;

    return (
      <div key={result.id} className={`border rounded-lg p-3 space-y-2 bg-white hover:bg-gray-50 transition-colors ${shouldHighlight ? 'ring-1 ring-green-300 bg-green-50' : ''}`}>
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <FileText className="w-4 h-4 text-muted-foreground flex-shrink-0" />
            <span
              className="font-medium text-sm truncate"
              dangerouslySetInnerHTML={{
                __html: (() => {
                  let highlighted = highlightSearchTerms(result.filePath, group.Groupname);
                  if (filters.search) {
                    highlighted = highlightSearchTerms(highlighted, filters.search);
                  }
                  return highlighted;
                })()
              }}
            />
            {result.lineNumber > 0 && (
              <Badge variant="outline" className="text-xs flex-shrink-0">
                Line {result.lineNumber}
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2 flex-shrink-0">
            {result.fileSize && (
              <span className="text-xs text-muted-foreground">
                {formatFileSize(result.fileSize)}
              </span>
            )}
          </div>
        </div>

        {result.context && (
          <div className={`bg-gray-50 rounded-md p-2 text-xs font-mono whitespace-pre-wrap border-l-2 border-blue-200`}>
            <div
              dangerouslySetInnerHTML={{
                __html: renderHighlightedContext(result.context)
              }}
            />
          </div>
        )}

        {/* Related Groups Navigation Links */}
        {(() => {
          const relatedGroups = extractRelatedGroups([result]);
          if (relatedGroups.length > 0) {
            return (
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-xs text-muted-foreground">Related groups:</span>
                {relatedGroups.slice(0, 3).map((relatedGroup) => (
                  <Button
                    key={relatedGroup}
                    variant="ghost"
                    size="sm"
                    className="h-5 px-2 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                    onClick={() => handleGroupNavigation(relatedGroup)}
                  >
                    {relatedGroup}
                    <ExternalLink className="h-2 w-2 ml-1" />
                  </Button>
                ))}
                {relatedGroups.length > 3 && (
                  <span className="text-xs text-muted-foreground">+{relatedGroups.length - 3} more</span>
                )}
              </div>
            );
          }
          return null;
        })()}

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            {result.fileType && (
              <span>{result.fileType.toUpperCase()} file</span>
            )}
            {result.commitHash && (
              <span>Commit: {result.commitHash.substring(0, 8)}</span>
            )}
            {result.branch && (
              <span>Branch: {result.branch}</span>
            )}
          </div>
          <span>Found {formatTimeAgo(result.detectedAt)}</span>
        </div>
      </div>
    );
  };

  const groupResultsBySource = (results: UsageResult[]) => {
    const grouped: Record<string, UsageResult[]> = {};
    results.forEach(result => {
      const key = `${result.sourceId}:${result.sourceName}`;
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(result);
    });
    return grouped;
  };

  const renderUsageResults = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </div>
      );
    }

    if (usageResults.length === 0) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground mb-4">
            {scanStatus ? 'No usage found for this group' : 'No scan completed yet'}
          </p>
          <Button onClick={handleManualScan} disabled={isScanning}>
            {isScanning ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            {scanStatus ? 'Scan Again' : 'Start Scan'}
          </Button>
        </div>
      );
    }

    const groupedResults = groupResultsBySource(usageResults);

    return (
      <div className="space-y-4">
        {Object.entries(groupedResults).map(([sourceKey, sourceResults]) => {
          const [, sourceName] = sourceKey.split(':');
          const sourceType = sourceResults[0].sourceType;

          const filteredSourceResults = sourceResults.filter(result => {
            if (filters.search) {
              const searchTerm = filters.search.toLowerCase();
              return result.filePath.toLowerCase().includes(searchTerm) ||
                     result.context.toLowerCase().includes(searchTerm) ||
                     result.sourceName.toLowerCase().includes(searchTerm);
            }
            return true;
          });

          const hasActiveFilter = filters.search || filters.sourceType;
          const isFiltered = hasActiveFilter && filteredSourceResults.length !== sourceResults.length;

          return (
            <Collapsible key={sourceKey} defaultOpen>
              <CollapsibleTrigger asChild>
                <Card className={`cursor-pointer hover:bg-gray-50 transition-colors ${isFiltered ? 'ring-1 ring-blue-300 bg-blue-50' : ''}`}>
                  <CardHeader className="py-3 px-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90 text-muted-foreground" />
                        <span className="text-lg">{getSourceTypeIcon(sourceType)}</span>
                        <div>
                          <CardTitle className="text-base font-medium">{sourceName}</CardTitle>
                          <CardDescription className="text-sm">
                            {isFiltered ? (
                              <>
                                <span className="font-medium text-blue-600">{filteredSourceResults.length}</span>
                                <span className="text-muted-foreground"> of {sourceResults.length}</span>
                                <span> usage{sourceResults.length !== 1 ? 's' : ''} shown</span>
                              </>
                            ) : (
                              <>
                                {sourceResults.length} usage{sourceResults.length !== 1 ? 's' : ''} found
                              </>
                            )}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {isFiltered && (
                          <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">
                            Filtered
                          </Badge>
                        )}
                        <Badge variant="outline" className={`${getSourceTypeColor(sourceType)} text-xs`}>
                          {sourceType.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="px-4 pb-4">
                  {filteredSourceResults.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      <Filter className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No results match the current filter</p>
                      <p className="text-xs">Try adjusting your search terms</p>
                    </div>
                  ) : (
                    <>
                      <div className="max-h-80 overflow-y-auto space-y-3 pr-2 usage-scroll">
                        {filteredSourceResults.map(renderUsageResult)}
                      </div>
                      {filteredSourceResults.length > 3 && (
                        <div className="mt-2 text-xs text-muted-foreground text-center">
                          Scroll to see all {filteredSourceResults.length} results
                          {isFiltered && ` (${sourceResults.length - filteredSourceResults.length} hidden by filter)`}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-medium">Usage Tracking</h3>
          <p className="text-sm text-muted-foreground">
            External references to group "{group.Groupname}"
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {renderScanStatus()}
          <Button
            onClick={handleManualScan}
            disabled={isScanning}
            size="sm"
          >
            {isScanning ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            {isScanning ? 'Scanning...' : 'Scan Now'}
          </Button>
          {usageResults.length > 0 && (
            <Button
              onClick={handleClearResults}
              variant="outline"
              size="sm"
            >
              <X className="h-4 w-4 mr-2" />
              Clear All Results
            </Button>
          )}
          <Link to="/settings?tab=usage-tracking">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Usage Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Real-time Scan Progress */}
      {showRealTimeProgress && currentScanId && (
        <RealTimeScanProgress
          scanId={currentScanId}
          repoId={repoId}
          groupName={group.Groupname}
          onScanComplete={handleScanComplete}
          onScanError={handleScanError}
          compact={false}
          autoSubscribe={true}
        />
      )}

      {/* Scan Completion Summary */}
      {showCompletionSummary && scanCompletionSummary && (
        <ScanCompletionSummary
          summary={scanCompletionSummary}
          groupName={group.Groupname}
          onDismiss={handleDismissCompletionSummary}
          relatedGroups={extractRelatedGroups(usageResults)}
        />
      )}

      {/* Global Filters - Compact version when there are results */}
      {usageResults.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">Filter all results:</span>
            </div>
            <div className="flex-1">
              <Input
                placeholder="Search across all sources..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="h-8 border-blue-300 focus:border-blue-500"
              />
            </div>
            <Select
              value={filters.sourceType || 'all'}
              onValueChange={(value) => setFilters(prev => ({
                ...prev,
                sourceType: value === 'all' ? undefined : value as SourceType
              }))}
            >
              <SelectTrigger className="w-36 h-8 border-blue-300">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                {SOURCE_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center">
                      <span className="mr-2">{getSourceTypeIcon(type.value)}</span>
                      {type.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {(filters.search || filters.sourceType) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({})}
                className="h-8 px-2 border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                <X className="h-3 w-3 mr-1" />
                Clear Filters
              </Button>
            )}
          </div>
          {(filters.search || filters.sourceType) && (
            <div className="mt-2 text-xs text-blue-600">
              <strong>{usageResults.length}</strong> result{usageResults.length !== 1 ? 's' : ''} shown
              {filters.search && ` matching "${filters.search}"`}
              {filters.sourceType && ` from ${filters.sourceType.toUpperCase()} sources`}
            </div>
          )}
        </div>
      )}

      {/* Results */}
      {renderUsageResults()}
    </div>
  );
};

export default GroupUsageTab;
