import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CheckCircle,
  Clock,
  FileText,
  Users,
  X,
  ExternalLink,
  TrendingUp,
  Database
} from 'lucide-react';
import { ScanLogSummary } from '@/types/scanLogs';
import { useNavigate } from 'react-router-dom';

interface ScanCompletionSummaryProps {
  summary: ScanLogSummary;
  groupName: string;
  onDismiss: () => void;
  relatedGroups?: string[];
}

export function ScanCompletionSummary({
  summary,
  groupName,
  onDismiss,
  relatedGroups = []
}: ScanCompletionSummaryProps) {
  const navigate = useNavigate();

  // Format duration from milliseconds to human readable
  const formatDuration = (milliseconds?: number): string => {
    if (!milliseconds || milliseconds <= 0) return "< 1s";
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Format performance metrics
  const formatPerformance = () => {
    if (!summary.performance) return null;
    
    const itemsPerSecond = summary.performance.itemsPerSecond?.toFixed(1) || '0';
    return `${itemsPerSecond} files/sec`;
  };

  // Handle navigation to a group
  const handleGroupClick = (targetGroupName: string) => {
    const params = new URLSearchParams();
    const searchValue = `groupname:"${targetGroupName}"`;
    params.set('search', searchValue);
    params.set('page', '1');
    navigate(`/groups?${params.toString()}`);
  };

  // Get completion time
  const getCompletionTime = () => {
    if (!summary.endTime) return 'Just now';
    
    const endTime = new Date(summary.endTime);
    return endTime.toLocaleTimeString();
  };

  return (
    <Alert className="border-green-200 bg-green-50 mb-6">
      <CheckCircle className="h-4 w-4 text-green-600" />
      <div className="flex-1">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <AlertDescription className="text-green-800">
              <div className="flex items-center gap-2 mb-3">
                <span className="font-semibold">Scan completed successfully!</span>
                <Badge variant="outline" className="text-green-700 border-green-300">
                  {getCompletionTime()}
                </Badge>
              </div>
              
              {/* Key Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-green-600" />
                  <div>
                    <div className="font-medium">{summary.resultsFound}</div>
                    <div className="text-xs text-green-600">Results Found</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-green-600" />
                  <div>
                    <div className="font-medium">{formatDuration(summary.duration)}</div>
                    <div className="text-xs text-green-600">Duration</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-green-600" />
                  <div>
                    <div className="font-medium">{summary.sourcesScanned}/{summary.totalSources}</div>
                    <div className="text-xs text-green-600">Sources Scanned</div>
                  </div>
                </div>
                
                {summary.performance && (
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <div>
                      <div className="font-medium">{formatPerformance()}</div>
                      <div className="text-xs text-green-600">Processing Speed</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Related Groups Navigation */}
              {relatedGroups.length > 0 && (
                <div className="mb-3">
                  <div className="text-sm font-medium text-green-700 mb-2 flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    Related Groups Found:
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {relatedGroups.slice(0, 8).map((relatedGroup) => (
                      <Button
                        key={relatedGroup}
                        variant="outline"
                        size="sm"
                        className="h-6 px-2 text-xs border-green-300 text-green-700 hover:bg-green-100"
                        onClick={() => handleGroupClick(relatedGroup)}
                      >
                        {relatedGroup}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Button>
                    ))}
                    {relatedGroups.length > 8 && (
                      <Badge variant="outline" className="text-green-600 border-green-300">
                        +{relatedGroups.length - 8} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Source Status Summary */}
              {summary.sourceStatuses && summary.sourceStatuses.length > 0 && (
                <div className="text-sm">
                  <span className="font-medium">Sources: </span>
                  {summary.sourceStatuses.map((source, index) => (
                    <span key={source.sourceId}>
                      <span className={source.status === 'completed' ? 'text-green-700' : 'text-orange-600'}>
                        {source.sourceName} ({source.resultCount} results)
                      </span>
                      {index < summary.sourceStatuses!.length - 1 && ', '}
                    </span>
                  ))}
                </div>
              )}
            </AlertDescription>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="text-green-600 hover:text-green-700 hover:bg-green-100 ml-2"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Alert>
  );
}
