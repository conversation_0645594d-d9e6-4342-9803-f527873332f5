import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, AlertCircle, Pause } from "lucide-react"
import type { ServiceStatus } from "@/types/scheduler"
import { ServiceStatusColors } from "@/types/scheduler"

interface ServiceStatusCardProps {
  service: ServiceStatus
}

export const ServiceStatusCard: React.FC<ServiceStatusCardProps> = ({ service }) => (
  <Card className="h-16">
    <CardContent className="p-3">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600">{service.name}</p>
          <div className="flex items-center space-x-1 mt-0.5">
            <Badge className={`${ServiceStatusColors[service.status]} border-0 text-xs px-1 py-0`}>
              {service.status}
            </Badge>
            {service.isRunning && (
              <span className="text-xs text-green-600">Running</span>
            )}
          </div>
        </div>
        <div className="h-4 w-4 flex items-center justify-center">
          {service.status === 'running' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : service.status === 'error' ? (
            <AlertCircle className="h-4 w-4 text-red-600" />
          ) : (
            <Pause className="h-4 w-4 text-yellow-600" />
          )}
        </div>
      </div>
    </CardContent>
  </Card>
)

export default ServiceStatusCard
