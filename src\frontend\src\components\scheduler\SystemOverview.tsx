import { Activity, Calendar, Zap, AlertCircle, CheckCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StatisticsCard } from "@/components/scheduler/StatisticsCard"
import { ServiceStatusCard } from "@/components/scheduler/ServiceStatusCard"
import { OverallHealthColors } from "@/types/scheduler"

interface SystemOverviewProps {
  status: {
    overallHealth: 'healthy' | 'degraded' | 'unhealthy'
    reportScheduler: any
    usageScanner: any
    autoScanService: any
  }
  overview: {
    totalPlannedTasks: number
    activeTasks: number
    failedTasks: number
    completedToday: number
  }
}

export const SystemOverview = ({ status, overview }: SystemOverviewProps) => {
  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2">
          <Activity className="h-5 w-5" />
          <span>System Overview</span>
          <Badge className={`${OverallHealthColors[status.overallHealth]} border-0 ml-2`}>
            {status.overallHealth}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-3">
          <ServiceStatusCard service={status.reportScheduler} />
          <ServiceStatusCard service={status.usageScanner} />
          <ServiceStatusCard service={status.autoScanService} />
          <StatisticsCard
            title="Planned"
            value={overview.totalPlannedTasks}
            icon={<Calendar className="h-4 w-4" />}
            color="text-blue-600"
          />
          <StatisticsCard
            title="Active"
            value={overview.activeTasks}
            icon={<Zap className="h-4 w-4" />}
            color="text-green-600"
          />
          <StatisticsCard
            title="Failed"
            value={overview.failedTasks}
            icon={<AlertCircle className="h-4 w-4" />}
            color="text-red-600"
          />
          <StatisticsCard
            title="Completed today"
            value={overview.completedToday}
            icon={<CheckCircle className="h-4 w-4" />}
            color="text-purple-600"
          />
        </div>
      </CardContent>
    </Card>
  )
}
