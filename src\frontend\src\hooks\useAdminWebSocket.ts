import { useState, useEffect, useRef, useCallback } from 'react';
import { RealTimeProgressUpdate } from '@/types/scanLogs';
import { API_CONFIG } from '@/configs';

// Helper function to detect scheduler events
const isSchedulerEvent = (update: RealTimeProgressUpdate): boolean => {
  const schedulerEventTypes = [
    'task_cancelled',
    'task_restarted',
    'task_removed',
    'task_paused',
    'task_resumed',
    'scheduler_refresh',
    // Auto-scan specific events
    'task_started',
    'task_completed',
    'task_failed'
  ];
  return schedulerEventTypes.includes(update.eventType);
};

interface UseAdminWebSocketOptions {
  url?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  onSchedulerEvent?: (event: RealTimeProgressUpdate) => void;
}

interface AdminWebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastUpdate: RealTimeProgressUpdate | null;
  allUpdates: RealTimeProgressUpdate[];
  isAdminSubscribed: boolean;
}

export function useAdminWebSocket(options: UseAdminWebSocketOptions = {}) {
  // Determine WebSocket protocol based on current page protocol
  const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  // Use the configured WebSocket base URL which will be proxied to the backend
  const defaultUrl = `${wsProtocol}//${window.location.host}${API_CONFIG.WS_BASE_URL}/progress`;

  const {
    url = defaultUrl,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    onSchedulerEvent
  } = options;

  const [state, setState] = useState<AdminWebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastUpdate: null,
    allUpdates: [],
    isAdminSubscribed: false
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);

  // Clear reconnect timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Send message to WebSocket
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const clientId = `admin-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const wsUrl = `${url}?clientId=${clientId}`;

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        const wasAdminSubscribed = state.isAdminSubscribed;
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null
        }));
        reconnectCountRef.current = 0;

        // Auto-subscribe to admin monitoring if previously subscribed
        if (wasAdminSubscribed) {
          // Use setTimeout to avoid calling subscribeToAdminMonitoring during render
          setTimeout(() => {
            if (wsRef.current?.readyState === WebSocket.OPEN) {
              wsRef.current.send(JSON.stringify({ type: 'subscribe_admin' }));
              setState(prev => ({ ...prev, isAdminSubscribed: true }));
            }
          }, 0);
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const update: RealTimeProgressUpdate = JSON.parse(event.data);

          setState(prev => ({
            ...prev,
            lastUpdate: update,
            allUpdates: [...prev.allUpdates.slice(-199), update] // Keep last 200 updates
          }));

          // Handle scheduler-specific events
          if (onSchedulerEvent && isSchedulerEvent(update)) {
            onSchedulerEvent(update);
          }
        } catch (error) {
          console.error('Failed to parse admin WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }));

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;

          // Use exponential backoff for reconnection attempts
          const backoffDelay = Math.min(reconnectInterval * Math.pow(2, reconnectCountRef.current - 1), 30000);

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, backoffDelay);
        } else if (reconnectCountRef.current >= reconnectAttempts) {
          console.error('Admin WebSocket: Maximum reconnection attempts reached');
          setState(prev => ({
            ...prev,
            error: 'Connection lost. Please refresh the page to reconnect.'
          }));
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Admin WebSocket error:', error);
        console.error('Failed to connect to:', wsUrl);

        // Provide more specific error messages based on the connection state
        let errorMessage = 'WebSocket connection failed';
        if (reconnectCountRef.current > 0) {
          errorMessage = `Connection lost. Reconnection attempt ${reconnectCountRef.current}/${reconnectAttempts} failed.`;
        } else {
          errorMessage = 'Failed to establish WebSocket connection. Please check your network connection.';
        }

        setState(prev => ({
          ...prev,
          error: errorMessage,
          isConnecting: false
        }));
      };

    } catch (error) {
      console.error('Failed to create admin WebSocket connection:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to create admin WebSocket connection',
        isConnecting: false
      }));
    }
  }, [url, reconnectAttempts, reconnectInterval]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    clearReconnectTimeout();

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      isAdminSubscribed: false
    }));
  }, [clearReconnectTimeout]);

  // Subscribe to admin monitoring (all scans)
  const subscribeToAdminMonitoring = useCallback(() => {
    if (sendMessage({ type: 'subscribe_admin' })) {
      setState(prev => ({ ...prev, isAdminSubscribed: true }));
      return true;
    }
    return false;
  }, [sendMessage]);

  // Unsubscribe from admin monitoring
  const unsubscribeFromAdminMonitoring = useCallback(() => {
    if (sendMessage({ type: 'unsubscribe_admin' })) {
      setState(prev => ({ ...prev, isAdminSubscribed: false }));
      return true;
    }
    return false;
  }, [sendMessage]);

  // Subscribe to specific repository
  const subscribeToRepository = useCallback((repoId: string) => {
    return sendMessage({ type: 'subscribe_repo', repoId });
  }, [sendMessage]);

  // Unsubscribe from specific repository
  const unsubscribeFromRepository = useCallback((repoId: string) => {
    return sendMessage({ type: 'unsubscribe_repo', repoId });
  }, [sendMessage]);

  // Subscribe to specific group
  const subscribeToGroup = useCallback((groupName: string) => {
    return sendMessage({ type: 'subscribe_group', groupName });
  }, [sendMessage]);

  // Unsubscribe from specific group
  const unsubscribeFromGroup = useCallback((groupName: string) => {
    return sendMessage({ type: 'unsubscribe_group', groupName });
  }, [sendMessage]);

  // Get updates for a specific scan
  const getUpdatesForScan = useCallback((scanId: string): RealTimeProgressUpdate[] => {
    return state.allUpdates.filter(update => update.scanId === scanId);
  }, [state.allUpdates]);

  // Get updates for a specific repository
  const getUpdatesForRepository = useCallback((repoId: string): RealTimeProgressUpdate[] => {
    return state.allUpdates.filter(update => update.repoId === repoId);
  }, [state.allUpdates]);

  // Get updates for a specific group
  const getUpdatesForGroup = useCallback((groupName: string): RealTimeProgressUpdate[] => {
    return state.allUpdates.filter(update => update.groupName === groupName);
  }, [state.allUpdates]);

  // Get latest update for a specific scan
  const getLatestUpdateForScan = useCallback((scanId: string): RealTimeProgressUpdate | null => {
    const scanUpdates = getUpdatesForScan(scanId);
    return scanUpdates.length > 0 ? scanUpdates[scanUpdates.length - 1] : null;
  }, [getUpdatesForScan]);

  // Clear all updates
  const clearUpdates = useCallback(() => {
    setState(prev => ({
      ...prev,
      allUpdates: [],
      lastUpdate: null
    }));
  }, []);

  // Get active scans (scans that have recent updates)
  const getActiveScans = useCallback((timeWindowMs: number = 5 * 60 * 1000): string[] => {
    const now = new Date().getTime();
    const recentUpdates = state.allUpdates.filter(update => {
      const updateTime = new Date(update.timestamp).getTime();
      return (now - updateTime) <= timeWindowMs;
    });

    const activeScanIds = new Set<string>();
    recentUpdates.forEach(update => {
      if (update.eventType !== 'scan_complete' && update.eventType !== 'scan_error') {
        activeScanIds.add(update.scanId);
      }
    });

    return Array.from(activeScanIds);
  }, [state.allUpdates]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      clearReconnectTimeout();
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmount');
      }
    };
  }, [autoConnect, clearReconnectTimeout]);

  return {
    // Connection state
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,

    // Subscription state
    isAdminSubscribed: state.isAdminSubscribed,

    // Progress data
    lastUpdate: state.lastUpdate,
    allUpdates: state.allUpdates,

    // Connection control
    connect,
    disconnect,

    // Admin subscription control
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,

    // Specific subscription control
    subscribeToRepository,
    unsubscribeFromRepository,
    subscribeToGroup,
    unsubscribeFromGroup,

    // Data utilities
    clearUpdates,
    getUpdatesForScan,
    getUpdatesForRepository,
    getUpdatesForGroup,
    getLatestUpdateForScan,
    getActiveScans,

    // Connection info
    reconnectCount: reconnectCountRef.current
  };
}
