package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestUsageSource_Validate(t *testing.T) {
	tests := []struct {
		name        string
		source      UsageSource
		expectError bool
		errorField  string
	}{
		{
			name: "valid git source",
			source: UsageSource{
				Name:          "Test Git Source",
				Type:          SourceTypeGit,
				ScanFrequency: 300,
				GitConfig: &GitSourceConfig{
					RepoURL:  "https://github.com/test/repo.git",
					Branch:   "main",
					AuthType: "token",
					Token:    "test-token",
				},
			},
			expectError: false,
		},
		{
			name: "valid api source",
			source: UsageSource{
				Name:          "Test API Source",
				Type:          SourceTypeAPI,
				ScanFrequency: 600,
				ApiConfig: &ApiSourceConfig{
					Endpoint: "https://api.example.com/data",
					Method:   "GET",
					AuthType: "bearer",
					Token:    "test-token",
				},
			},
			expectError: false,
		},
		{
			name: "valid file source",
			source: UsageSource{
				Name:          "Test File Source",
				Type:          SourceTypeFile,
				ScanFrequency: 900,
				FileConfig: &FileSourceConfig{
					BasePath:  "/path/to/files",
					Recursive: true,
					FileTypes: []string{".yaml", ".json"},
				},
			},
			expectError: false,
		},
		{
			name: "missing name",
			source: UsageSource{
				Type:          SourceTypeGit,
				ScanFrequency: 300,
				GitConfig: &GitSourceConfig{
					RepoURL: "https://github.com/test/repo.git",
				},
			},
			expectError: true,
			errorField:  "name",
		},
		{
			name: "missing type",
			source: UsageSource{
				Name:          "Test Source",
				ScanFrequency: 300,
			},
			expectError: true,
			errorField:  "type",
		},
		{
			name: "invalid scan frequency",
			source: UsageSource{
				Name:          "Test Source",
				Type:          SourceTypeGit,
				ScanFrequency: 100, // Less than minimum 300
				GitConfig: &GitSourceConfig{
					RepoURL: "https://github.com/test/repo.git",
				},
			},
			expectError: true,
			errorField:  "scanFrequency",
		},
		{
			name: "git source missing config",
			source: UsageSource{
				Name:          "Test Git Source",
				Type:          SourceTypeGit,
				ScanFrequency: 300,
				// GitConfig is nil
			},
			expectError: true,
			errorField:  "gitConfig",
		},
		{
			name: "api source missing config",
			source: UsageSource{
				Name:          "Test API Source",
				Type:          SourceTypeAPI,
				ScanFrequency: 300,
				// ApiConfig is nil
			},
			expectError: true,
			errorField:  "apiConfig",
		},
		{
			name: "file source missing config",
			source: UsageSource{
				Name:          "Test File Source",
				Type:          SourceTypeFile,
				ScanFrequency: 300,
				// FileConfig is nil
			},
			expectError: true,
			errorField:  "fileConfig",
		},
		{
			name: "invalid source type",
			source: UsageSource{
				Name:          "Test Source",
				Type:          "invalid",
				ScanFrequency: 300,
			},
			expectError: true,
			errorField:  "type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.source.Validate()
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" {
					validationErr, ok := err.(*ValidationError)
					assert.True(t, ok, "Expected ValidationError")
					assert.Equal(t, tt.errorField, validationErr.Field)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGitSourceConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      GitSourceConfig
		expectError bool
		errorField  string
	}{
		{
			name: "valid config with all fields",
			config: GitSourceConfig{
				RepoURL:  "https://github.com/test/repo.git",
				Branch:   "main",
				AuthType: "token",
				Token:    "test-token",
			},
			expectError: false,
		},
		{
			name: "valid config with defaults",
			config: GitSourceConfig{
				RepoURL: "https://github.com/test/repo.git",
				// Branch and AuthType will be set to defaults
			},
			expectError: false,
		},
		{
			name: "missing repo URL",
			config: GitSourceConfig{
				Branch:   "main",
				AuthType: "token",
			},
			expectError: true,
			errorField:  "repoURL",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" {
					validationErr, ok := err.(*ValidationError)
					assert.True(t, ok, "Expected ValidationError")
					assert.Equal(t, tt.errorField, validationErr.Field)
				}
			} else {
				assert.NoError(t, err)
				// Check defaults are set
				if tt.config.Branch == "" {
					assert.Equal(t, "main", tt.config.Branch)
				}
				if tt.config.AuthType == "" {
					assert.Equal(t, "none", tt.config.AuthType)
				}
			}
		})
	}
}

func TestApiSourceConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      ApiSourceConfig
		expectError bool
		errorField  string
	}{
		{
			name: "valid config with all fields",
			config: ApiSourceConfig{
				Endpoint: "https://api.example.com/data",
				Method:   "POST",
				AuthType: "bearer",
				Token:    "test-token",
			},
			expectError: false,
		},
		{
			name: "valid config with defaults",
			config: ApiSourceConfig{
				Endpoint: "https://api.example.com/data",
				// Method and AuthType will be set to defaults
			},
			expectError: false,
		},
		{
			name: "missing endpoint",
			config: ApiSourceConfig{
				Method:   "GET",
				AuthType: "none",
			},
			expectError: true,
			errorField:  "endpoint",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" {
					validationErr, ok := err.(*ValidationError)
					assert.True(t, ok, "Expected ValidationError")
					assert.Equal(t, tt.errorField, validationErr.Field)
				}
			} else {
				assert.NoError(t, err)
				// Check defaults are set
				if tt.config.Method == "" {
					assert.Equal(t, "GET", tt.config.Method)
				}
				if tt.config.AuthType == "" {
					assert.Equal(t, "none", tt.config.AuthType)
				}
			}
		})
	}
}

func TestFileSourceConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      FileSourceConfig
		expectError bool
		errorField  string
	}{
		{
			name: "valid config",
			config: FileSourceConfig{
				BasePath:  "/path/to/files",
				Recursive: true,
				FileTypes: []string{".yaml", ".json"},
			},
			expectError: false,
		},
		{
			name: "missing base path",
			config: FileSourceConfig{
				Recursive: true,
			},
			expectError: true,
			errorField:  "basePath",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorField != "" {
					validationErr, ok := err.(*ValidationError)
					assert.True(t, ok, "Expected ValidationError")
					assert.Equal(t, tt.errorField, validationErr.Field)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUsageResult_Creation(t *testing.T) {
	now := time.Now()
	result := UsageResult{
		ID:         "test-result-1",
		GroupName:  "test-group",
		SourceID:   "source-1",
		SourceName: "Test Source",
		SourceType: SourceTypeGit,
		FilePath:   "/path/to/file.yaml",
		LineNumber: 42,
		Context:    "groups: [test-group]",
		MatchType:  "exact",
		DetectedAt: now,
		RepoID:     "repo-1",
		FileSize:   1024,
		FileType:   "yaml",
		CommitHash: "abc123",
		Branch:     "main",
	}

	assert.Equal(t, "test-result-1", result.ID)
	assert.Equal(t, "test-group", result.GroupName)
	assert.Equal(t, "source-1", result.SourceID)
	assert.Equal(t, "Test Source", result.SourceName)
	assert.Equal(t, SourceTypeGit, result.SourceType)
	assert.Equal(t, "/path/to/file.yaml", result.FilePath)
	assert.Equal(t, 42, result.LineNumber)
	assert.Equal(t, "groups: [test-group]", result.Context)
	assert.Equal(t, "exact", result.MatchType)
	assert.Equal(t, now, result.DetectedAt)
	assert.Equal(t, "repo-1", result.RepoID)
	assert.Equal(t, int64(1024), result.FileSize)
	assert.Equal(t, "yaml", result.FileType)
	assert.Equal(t, "abc123", result.CommitHash)
	assert.Equal(t, "main", result.Branch)
}

func TestUsageScanStatus_Creation(t *testing.T) {
	now := time.Now()
	status := UsageScanStatus{
		GroupName:        "test-group",
		SourcesTotal:     3,
		SourcesScanned:   2,
		InProgress:       true,
		LastScanTime:     now,
		CompletedSources: []string{"source-1", "source-2"},
		PendingSources:   []string{"source-3"},
		FailedSources: []SourceScanFailure{
			{
				SourceID:   "source-4",
				SourceName: "Failed Source",
				Error:      "connection timeout",
				FailedAt:   now,
			},
		},
		RepoID:       "repo-1",
		TotalUsages:  5,
		ScanDuration: time.Minute * 2,
	}

	assert.Equal(t, "test-group", status.GroupName)
	assert.Equal(t, 3, status.SourcesTotal)
	assert.Equal(t, 2, status.SourcesScanned)
	assert.True(t, status.InProgress)
	assert.Equal(t, now, status.LastScanTime)
	assert.Equal(t, []string{"source-1", "source-2"}, status.CompletedSources)
	assert.Equal(t, []string{"source-3"}, status.PendingSources)
	assert.Len(t, status.FailedSources, 1)
	assert.Equal(t, "source-4", status.FailedSources[0].SourceID)
	assert.Equal(t, "repo-1", status.RepoID)
	assert.Equal(t, 5, status.TotalUsages)
	assert.Equal(t, time.Minute*2, status.ScanDuration)
}

func TestValidationError(t *testing.T) {
	err := NewValidationError("testField", "test error message")
	
	assert.Equal(t, "testField", err.Field)
	assert.Equal(t, "test error message", err.Message)
	assert.Equal(t, "test error message", err.Error())
}
