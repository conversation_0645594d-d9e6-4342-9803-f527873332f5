# Usage Tracking System Architecture

## Overview

The Group Usage Tracking system is designed to monitor and track where groups are referenced across multiple sources including Git repositories, APIs, and file systems. This document describes the technical architecture, components, and design decisions.

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Usage Dashboard │  │ Source Config   │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/REST
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Usage Controller│  │ Source Manager  │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Service Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Scanner Service │  │ Status Manager  │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Worker Pool Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Scan Workers    │  │ Results Processor│                 │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Handler Layer                               │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Git Handler     │  │ API Handler     │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Storage Layer                               │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Source Config   │  │ Scan Results    │                  │
│  │ (JSON Files)    │  │ (JSON Files)    │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Usage Source Manager

**Purpose**: Manages configuration and lifecycle of usage sources.

**Key Features**:
- CRUD operations for usage sources
- Source validation and connection testing
- Configuration persistence
- Cache management for performance

**Implementation**: `services/usage_source_manager.go`

### 2. Usage Scanner Service

**Purpose**: Orchestrates scanning operations and manages scheduling.

**Key Features**:
- Automatic scheduling based on source configuration
- Manual scan triggering
- Scan status tracking
- Worker pool management

**Implementation**: `services/usage_scanner.go`

### 3. Scan Worker Pool

**Purpose**: Provides concurrent processing of scan jobs.

**Key Features**:
- Configurable worker count
- Job queuing and distribution
- Result batching for performance
- Graceful shutdown handling

**Implementation**: `services/scan_worker_pool.go`

### 4. Source Handlers

**Purpose**: Implement source-specific scanning logic.

**Supported Types**:
- **Git Handler**: Clones repositories and scans files
- **API Handler**: Queries REST APIs for group data
- **File Handler**: Scans local/network file systems

**Implementation**: `services/*_source_handler.go`

### 5. Scan Status Manager

**Purpose**: Tracks scan progress and stores results.

**Key Features**:
- Real-time status updates
- Result persistence
- Failure tracking
- Performance metrics

**Implementation**: `services/scan_status_manager.go`

## Data Models

### Usage Source

```go
type UsageSource struct {
    ID              string                 `json:"id"`
    Name            string                 `json:"name"`
    Type            SourceType             `json:"type"`
    IsActive        bool                   `json:"isActive"`
    ScanFrequency   int                    `json:"scanFrequency"`
    IncludePatterns []string               `json:"includePatterns"`
    ExcludePatterns []string               `json:"excludePatterns"`
    GitConfig       *GitSourceConfig       `json:"gitConfig,omitempty"`
    ApiConfig       *ApiSourceConfig       `json:"apiConfig,omitempty"`
    FileConfig      *FileSourceConfig      `json:"fileConfig,omitempty"`
    CreatedAt       time.Time              `json:"createdAt"`
    UpdatedAt       time.Time              `json:"updatedAt"`
}
```

### Usage Result

```go
type UsageResult struct {
    ID          string    `json:"id"`
    GroupName   string    `json:"groupName"`
    SourceID    string    `json:"sourceId"`
    SourceName  string    `json:"sourceName"`
    SourceType  string    `json:"sourceType"`
    FilePath    string    `json:"filePath"`
    LineNumber  int       `json:"lineNumber"`
    Context     string    `json:"context"`
    MatchType   string    `json:"matchType"`
    DetectedAt  time.Time `json:"detectedAt"`
    RepoID      string    `json:"repoId"`
    FileSize    int64     `json:"fileSize"`
    FileType    string    `json:"fileType"`
    CommitHash  string    `json:"commitHash,omitempty"`
    Branch      string    `json:"branch,omitempty"`
}
```

### Scan Status

```go
type UsageScanStatus struct {
    GroupName        string               `json:"groupName"`
    RepoID           string               `json:"repoId"`
    SourcesTotal     int                  `json:"sourcesTotal"`
    SourcesScanned   int                  `json:"sourcesScanned"`
    InProgress       bool                 `json:"inProgress"`
    LastScanTime     time.Time            `json:"lastScanTime"`
    CompletedSources []string             `json:"completedSources"`
    PendingSources   []string             `json:"pendingSources"`
    FailedSources    []SourceScanFailure  `json:"failedSources"`
    TotalUsages      int                  `json:"totalUsages"`
    ScanDuration     time.Duration        `json:"scanDuration"`
}
```

## Concurrency and Performance

### Worker Pool Design

The system uses a configurable worker pool to handle concurrent scanning:

- **Default Workers**: 5 concurrent workers
- **Job Queue**: Buffered channel (2x worker count)
- **Result Processing**: Batched processing for efficiency
- **Graceful Shutdown**: Proper cleanup on service stop

### Performance Optimizations

1. **Batched Result Processing**: Results are processed in batches to reduce I/O overhead
2. **Connection Caching**: Git repositories are cached to avoid repeated clones
3. **File Pattern Filtering**: Efficient file filtering using glob patterns
4. **Context Cancellation**: Proper cancellation support for long-running operations

### Scalability Considerations

- **Horizontal Scaling**: Worker pool can be increased based on system resources
- **Memory Management**: Streaming file processing to handle large files
- **Rate Limiting**: Built-in delays to avoid overwhelming external systems
- **Circuit Breaker**: Failure detection and recovery mechanisms

## Error Handling and Resilience

### Error Categories

1. **Configuration Errors**: Invalid source configuration
2. **Connection Errors**: Network or authentication failures
3. **Processing Errors**: File parsing or data processing issues
4. **System Errors**: Resource exhaustion or internal failures

### Resilience Patterns

- **Retry Logic**: Automatic retry with exponential backoff
- **Circuit Breaker**: Temporary failure isolation
- **Graceful Degradation**: Partial results on source failures
- **Health Checks**: Regular source connectivity validation

## Security Considerations

### Authentication

- **Git Sources**: Support for tokens, SSH keys, and basic auth
- **API Sources**: Bearer tokens, API keys, and basic auth
- **Credential Storage**: Secure storage with encryption at rest

### Access Control

- **Source Isolation**: Each source operates in isolation
- **Permission Validation**: Regular validation of source permissions
- **Audit Logging**: Comprehensive logging of all operations

### Data Protection

- **Sensitive Data Filtering**: Automatic detection and masking
- **Secure Transmission**: HTTPS/TLS for all external communications
- **Data Retention**: Configurable retention policies

## Monitoring and Observability

### Metrics

- **Scan Performance**: Duration, throughput, success rates
- **System Health**: Worker utilization, queue depth, memory usage
- **Source Status**: Availability, response times, error rates

### Logging

- **Structured Logging**: JSON format with consistent fields
- **Log Levels**: Configurable verbosity levels
- **Correlation IDs**: Request tracing across components

### Alerting

- **Scan Failures**: Immediate alerts on source failures
- **Performance Degradation**: Alerts on slow or failing scans
- **System Health**: Resource utilization alerts

## Configuration

### Environment Variables

```bash
# Worker pool configuration
USAGE_SCANNER_WORKERS=5
USAGE_SCANNER_BATCH_SIZE=10
USAGE_SCANNER_BATCH_TIMEOUT=5s

# Scan scheduling
USAGE_SCANNER_INTERVAL=5m
USAGE_SCANNER_MAX_CONCURRENT=10

# Storage configuration
USAGE_DATA_DIR=/data/usage-tracking
USAGE_CACHE_TTL=30m

# Performance tuning
USAGE_MAX_FILE_SIZE=10MB
USAGE_SCAN_TIMEOUT=30m
```

### Source Configuration

Sources are configured via JSON files with validation:

```json
{
  "name": "Production Config Repo",
  "type": "git",
  "isActive": true,
  "scanFrequency": 3600,
  "includePatterns": ["*.yaml", "*.json"],
  "excludePatterns": ["*.log", "node_modules/**"],
  "gitConfig": {
    "repoURL": "https://github.com/company/config.git",
    "branch": "main",
    "authType": "token",
    "token": "ghp_xxxxxxxxxxxx"
  }
}
```

## Future Enhancements

### Planned Features

1. **Real-time Scanning**: WebSocket-based real-time updates
2. **Advanced Analytics**: Usage trends and pattern analysis
3. **Integration APIs**: Webhooks and external system integration
4. **Machine Learning**: Intelligent pattern detection
5. **Distributed Scanning**: Multi-node scanning support

### Scalability Improvements

1. **Database Backend**: Migration from file-based to database storage
2. **Message Queue**: Asynchronous job processing with Redis/RabbitMQ
3. **Caching Layer**: Redis-based caching for improved performance
4. **Load Balancing**: Multiple scanner service instances

## Deployment

### System Requirements

- **CPU**: 2+ cores recommended
- **Memory**: 4GB+ RAM for typical workloads
- **Storage**: SSD recommended for scan result storage
- **Network**: Stable internet connection for external sources

### Configuration Management

- **Environment-based**: Different configs for dev/staging/prod
- **Secret Management**: External secret management integration
- **Health Checks**: Kubernetes/Docker health check endpoints
- **Graceful Shutdown**: SIGTERM handling for clean shutdowns
