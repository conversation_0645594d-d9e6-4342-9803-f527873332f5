import { useState, useEffect, useCallback, useRef } from "react"
import { useAdminWebSocket } from "@/hooks/useAdminWebSocket"

interface ProgressType {
  current: number
  total: number
  percentage: number
  description: string
  currentFile?: string
}

interface UseSchedulerWebSocketProps {
  isRealTimeEnabled: boolean
  setIsRealTimeEnabled: (enabled: boolean) => void
  activeTasks: any[]
  recentlyCancelledTasks: Set<string>
  setRecentlyCancelledTasks: (fn: (prev: Set<string>) => Set<string>) => void
  handleTaskRestarted: (taskId: string, taskType: string) => void
}

interface UseSchedulerWebSocketReturn {
  // WebSocket state
  isConnected: boolean
  isConnecting: boolean
  wsError: string | null
  lastUpdate: any
  isAdminSubscribed: boolean

  // Real-time monitoring state
  realTimeProgress: Map<string, ProgressType>
  globalScans: Map<string, any>
  processedEvents: Set<string>

  // Actions
  subscribeToAdminMonitoring: () => void
  unsubscribeFromAdminMonitoring: () => void
  connect: () => void
  getActiveScans: (timeWindowMs?: number) => string[]
  handleSchedulerEvent: (event: any) => void
}

export const useSchedulerWebSocket = ({
  isRealTimeEnabled,
  setIsRealTimeEnabled,
  activeTasks,
  recentlyCancelledTasks,
  setRecentlyCancelledTasks,
  handleTaskRestarted
}: UseSchedulerWebSocketProps): UseSchedulerWebSocketReturn => {

  // Real-time monitoring state
  const [realTimeProgress, setRealTimeProgress] = useState<Map<string, ProgressType>>(new Map())
  const [globalScans, setGlobalScans] = useState<Map<string, any>>(new Map())
  const [processedEvents, setProcessedEvents] = useState<Set<string>>(new Set())

  // Admin WebSocket for real-time progress monitoring
  const {
    isConnected,
    isConnecting,
    error: wsError,
    lastUpdate,
    isAdminSubscribed,
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,
    connect,
    getActiveScans
  } = useAdminWebSocket({
    autoConnect: isRealTimeEnabled
  })

  // Use refs to avoid circular dependencies
  const getActiveScansRef = useRef(getActiveScans)
  const subscribeToAdminMonitoringRef = useRef(subscribeToAdminMonitoring)
  const unsubscribeFromAdminMonitoringRef = useRef(unsubscribeFromAdminMonitoring)

  // Update refs when values change
  getActiveScansRef.current = getActiveScans
  subscribeToAdminMonitoringRef.current = subscribeToAdminMonitoring
  unsubscribeFromAdminMonitoringRef.current = unsubscribeFromAdminMonitoring

  // Subscribe to admin monitoring when connected and real-time is enabled
  useEffect(() => {
    if (isConnected && isRealTimeEnabled && !isAdminSubscribed) {
      subscribeToAdminMonitoringRef.current()
    } else if (isAdminSubscribed && !isRealTimeEnabled) {
      unsubscribeFromAdminMonitoringRef.current()
    }
  }, [isConnected, isRealTimeEnabled, isAdminSubscribed])

  // Sync global scans with active tasks and WebSocket data
  useEffect(() => {
    if (isConnected && isAdminSubscribed) {
      const wsActiveScans = getActiveScansRef.current()

      // Create a new map for global scans
      const newGlobalScans = new Map<string, any>()

      // Add active tasks from scheduler
      activeTasks?.forEach(task => {
        const scanKey = `${task.repository}-${task.groupName}`
        if (!newGlobalScans.has(scanKey)) {
          newGlobalScans.set(scanKey, {
            scanId: task.id,
            groupName: task.groupName,
            repoId: task.repository,
            status: 'running' as const,
            startTime: new Date(task.startTime).getTime(),
            lastUpdate: new Date(),
            progress: {
              current: 0,
              total: 1,
              percentage: 0,
              description: `Processing ${task.groupName}...`,
              currentFile: task.groupName
            }
          })
        }
      })

      // Add WebSocket active scans (scanIds)
      wsActiveScans.forEach((scanId) => {
        if (!recentlyCancelledTasks.has(scanId)) {
          // We don't have full scan data from getActiveScans, so we skip this
          // The real-time updates will populate the scan data via WebSocket events
        }
      })

      setGlobalScans(newGlobalScans)
    }
  }, [isRealTimeEnabled, isConnected, isAdminSubscribed, activeTasks, recentlyCancelledTasks])

  // Handle scheduler events from WebSocket
  const handleSchedulerEvent = useCallback((event: any) => {
    // Create unique event key for deduplication
    // For scheduler events, use taskId; for scan events, use scanId/repoId/groupName
    const eventKey = event.taskId
      ? `${event.eventType}_${event.timestamp}_${event.taskId}`
      : `${event.eventType}_${event.timestamp}_${event.scanId || ''}_${event.repoId || ''}_${event.groupName || ''}`;

    // Check if we've already processed this event
    if (processedEvents.has(eventKey)) {
      return;
    }

    // Add to processed events
    setProcessedEvents(prev => {
      const newSet = new Set(prev);
      newSet.add(eventKey);

      // Keep only recent events (last 1000) to prevent memory leaks
      if (newSet.size > 1000) {
        const eventsArray = Array.from(newSet);
        const recentEvents = eventsArray.slice(-500); // Keep last 500 events
        return new Set(recentEvents);
      }

      return newSet;
    });

    // Handle events that have progress data but no eventType (treat as progress events)
    if (!event.eventType && event.progress && event.scanId && event.repoId && event.groupName) {
      const scanKey = `${event.repoId}-${event.groupName}`;

      // Update progress by scanId
      setRealTimeProgress(prev => {
        const newProgress = new Map(prev);
        newProgress.set(event.scanId, event.progress);
        // Also set by scan key for backward compatibility
        newProgress.set(scanKey, event.progress);
        return newProgress;
      });

      // Update global scans with latest progress
      setGlobalScans(prev => {
        const newScans = new Map(prev);
        const existingScan = newScans.get(scanKey);
        if (existingScan) {
          newScans.set(scanKey, {
            ...existingScan,
            progress: event.progress,
            lastUpdate: new Date()
          });
        } else {
          // Create new scan entry if it doesn't exist
          newScans.set(scanKey, {
            scanId: event.scanId,
            groupName: event.groupName,
            repoId: event.repoId,
            status: 'running' as const,
            startTime: Date.now(),
            lastUpdate: new Date(),
            progress: event.progress
          });
        }
        return newScans;
      });
      return;
    }

    // Handle different event types
    switch (event.eventType) {
      case 'task_cancelled':
      case 'task_removed':
        // Immediately remove the task from UI state for instant feedback
        if (event.taskId) {
          // Track recently cancelled tasks to prevent restoration
          setRecentlyCancelledTasks(prev => new Set(prev).add(event.taskId));

          // Clear the cancelled task from tracking after 5 seconds
          setTimeout(() => {
            setRecentlyCancelledTasks(prev => {
              const newSet = new Set(prev);
              newSet.delete(event.taskId);
              return newSet;
            });
          }, 5000);
        }
        break;

      case 'task_restarted':
        // Handle task restart - move from failed to active state
        if (event.taskId && event.taskType) {
          handleTaskRestarted(event.taskId, event.taskType);
        }
        break;

      case 'task_started':
      case 'task_completed':
      case 'task_failed':
      case 'scheduler_refresh':
        // These events indicate task state changes that require UI refresh
        // The scheduler dashboard will refresh its data to reflect the new state
        break;

      // Handle real-time progress events with actual event types from backend
      case 'scan_init':
      case 'scan_start':
      case 'scan_started':
        // Add new scan to global scans
        if (event.scanId && event.repoId && event.groupName) {
          const scanKey = `${event.repoId}-${event.groupName}`;
          setGlobalScans(prev => {
            const newScans = new Map(prev);
            newScans.set(scanKey, {
              scanId: event.scanId,
              groupName: event.groupName,
              repoId: event.repoId,
              status: 'running' as const,
              startTime: Date.now(),
              lastUpdate: new Date(),
              progress: event.progress || {
                current: 0,
                total: 1,
                percentage: 0,
                description: 'Scan started...',
                currentFile: event.groupName
              }
            });
            return newScans;
          });
        }
        break;

      case 'progress':
      case 'file_progress':
      case 'scan_progress':
        // Update scan progress with real-time data
        if (event.scanId && event.progress && event.repoId && event.groupName) {
          const scanKey = `${event.repoId}-${event.groupName}`;

          // Update progress by scanId
          setRealTimeProgress(prev => {
            const newProgress = new Map(prev);
            newProgress.set(event.scanId, event.progress);
            // Also set by scan key for backward compatibility
            newProgress.set(scanKey, event.progress);
            return newProgress;
          });

          // Update global scans with progress
          setGlobalScans(prev => {
            const newScans = new Map(prev);
            const existingScan = newScans.get(scanKey);
            if (existingScan) {
              newScans.set(scanKey, {
                ...existingScan,
                progress: event.progress,
                lastUpdate: new Date()
              });
            } else {
              // Create new scan entry if it doesn't exist
              newScans.set(scanKey, {
                scanId: event.scanId,
                groupName: event.groupName,
                repoId: event.repoId,
                status: 'running' as const,
                startTime: Date.now(),
                progress: event.progress
              });
            }
            return newScans;
          });
        }
        break;

      case 'error':
        // Handle non-fatal scan errors (connection issues, etc.)
        // These errors don't stop the scan, so we update the scan status but keep it running
        if (event.scanId && event.repoId && event.groupName) {
          const scanKey = `${event.repoId}-${event.groupName}`;

          setGlobalScans(prev => {
            const newScans = new Map(prev);
            const existingScan = newScans.get(scanKey);
            if (existingScan) {
              newScans.set(scanKey, {
                ...existingScan,
                status: 'running' as const, // Keep status as running since scan continues
                lastUpdate: new Date(),
                error: {
                  message: event.message || 'Scan error occurred',
                  details: event.error || '',
                  timestamp: event.timestamp || new Date().toISOString()
                }
              });
            }
            return newScans;
          });
        }
        break;

      case 'scan_complete':
      case 'scan_error':
      case 'scan_completed':
      case 'scan_failed':
        // Remove completed/failed scans from global scans
        if (event.scanId && event.repoId && event.groupName) {
          const scanKey = `${event.repoId}-${event.groupName}`;
          setGlobalScans(prev => {
            const newScans = new Map(prev);
            newScans.delete(scanKey);
            return newScans;
          });

          setRealTimeProgress(prev => {
            const newProgress = new Map(prev);
            newProgress.delete(event.scanId);
            newProgress.delete(scanKey);
            return newProgress;
          });
        }
        break;

      default:
        // Unhandled event type - silently ignore
        break;
    }
  }, [processedEvents, setRecentlyCancelledTasks, handleTaskRestarted])

  // Helper function to detect scheduler events
  const isSchedulerEvent = useCallback((update: any): boolean => {
    const schedulerEventTypes = [
      'task_cancelled',
      'task_restarted',
      'task_removed',
      'task_paused',
      'task_resumed',
      'scheduler_refresh',
      // Auto-scan specific events
      'task_started',
      'task_completed',
      'task_failed',
      // Progress events for real-time updates
      'scan_init',
      'scan_start',
      'scan_started',
      'progress',
      'file_progress',
      'scan_progress',
      'chunk_start',
      'chunk_complete',
      'file_complete',
      'scan_complete',
      'scan_completed',
      'scan_error',
      'scan_failed',
      'error'
    ];
    return schedulerEventTypes.includes(update.eventType);
  }, []);

  // Process WebSocket updates
  useEffect(() => {
    if (lastUpdate) {
      if (isSchedulerEvent(lastUpdate)) {
        handleSchedulerEvent(lastUpdate);
      }
    }
  }, [lastUpdate, handleSchedulerEvent, isSchedulerEvent]);

  return {
    // WebSocket state
    isConnected,
    isConnecting,
    wsError,
    lastUpdate,
    isAdminSubscribed,

    // Real-time monitoring state
    realTimeProgress,
    globalScans,
    processedEvents,

    // Actions
    subscribeToAdminMonitoring,
    unsubscribeFromAdminMonitoring,
    connect,
    getActiveScans,
    handleSchedulerEvent
  }
}
