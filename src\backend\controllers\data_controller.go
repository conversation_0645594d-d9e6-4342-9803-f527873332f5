package controllers

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"

	"adgitops-ui/src/backend/api"
	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/repository"
	"adgitops-ui/src/backend/search"
	"adgitops-ui/src/backend/services"

	"github.com/gin-gonic/gin"
)

// SchedulerServiceInterface defines the methods needed from the scheduler service
type SchedulerServiceInterface interface {
	GetExecutions(sharedId ...string) []models.ReportExecution
	DeleteExecutionsForPreset(presetID string) error
}

// DataController handles data-related API endpoints
type DataController struct {
	dataProcessor    *services.DataProcessor
	repoManager      *repository.RepositoryManager
	searchService    *search.Service
	schedulerService SchedulerServiceInterface

	// Cache for groups and users
	groupsCache []models.Group
	usersCache  []models.User
}

// NewDataController creates a new data controller
func NewDataController(dataProcessor interface{}, repoManager interface{}, schedulerService interface{}) *DataController {
	controller := &DataController{
		searchService: search.NewService(),
	}

	// Handle dataProcessor
	if dataProcessor != nil {
		controller.dataProcessor = dataProcessor.(*services.DataProcessor)
	}

	// Handle repoManager
	if repoManager != nil {
		controller.repoManager = repoManager.(*repository.RepositoryManager)
	}

	// schedulerService can be nil
	if schedulerService != nil {
		controller.schedulerService = schedulerService.(SchedulerServiceInterface)
	}

	return controller
}

// GetGroups returns all groups from all repositories
func (c *DataController) GetGroups() []models.Group {
	// If we have a cache, return it
	if len(c.groupsCache) > 0 {
		return c.groupsCache
	}

	// Get all repositories
	configList, _ := c.repoManager.GetConfigurations()
	configs := configList.Configs

	// Get groups from all repositories
	var allGroups []models.Group
	for _, config := range configs {
		// Get groups for this repository
		groupsForRepo, err := c.GetGroupsForRepo(config.ID)
		if err == nil {
			allGroups = append(allGroups, groupsForRepo...)
		}
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(allGroups)

	// Cache the result
	c.groupsCache = allGroups

	return allGroups
}

// GetGroupsForRepo returns all groups from a specific repository
func (c *DataController) GetGroupsForRepo(repoId string) ([]models.Group, error) {
	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		return nil, fmt.Errorf("failed to get repository: %w", err)
	}
	if repo == nil {
		return nil, fmt.Errorf("repository not found: %s", repoId)
	}

	// Get the repository path
	repoPath := repo.GetLocalRepoPath()

	// Check if the repository path exists
	if _, err := os.Stat(repoPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("repository path does not exist: %s", repoPath)
	}

	// Parse the JSON files
	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON files: %w", err)
	}

	// Add repository ID to each group
	for i := range groups {
		groups[i].RepoID = repoId
	}

	// Note: Member type resolution is now handled by the calling controller
	// to ensure access to all groups across all repositories for proper resolution

	return groups, nil
}

// GetUsersForRepo returns all users from a specific repository
func (c *DataController) GetUsersForRepo(repoId string) ([]models.User, error) {
	// Get groups for this repository
	groups, err := c.GetGroupsForRepo(repoId)
	if err != nil {
		return nil, fmt.Errorf("failed to get groups: %w", err)
	}

	// Extract users from groups
	// Create a map to track unique users
	userMap := make(map[string]*models.User)

	// Process each group
	for _, group := range groups {
		// Process each member
		for _, member := range group.Members {
			// Only process user members
			if member.Type == models.UserMemberType {
				// Check if we've already seen this user
				user, exists := userMap[member.Name]
				if !exists {
					// Create a new user
					user = &models.User{
						Name:   member.Name,
						Groups: []string{},
						LOBs:   []string{},
					}
					userMap[member.Name] = user
				}

				// Add the group to the user's groups
				user.Groups = append(user.Groups, group.Groupname)

				// Add the LOB to the user's LOBs if not already present
				lobExists := false
				for _, lob := range user.LOBs {
					if lob == group.Lob {
						lobExists = true
						break
					}
				}
				if !lobExists && group.Lob != "" {
					user.LOBs = append(user.LOBs, group.Lob)
				}
			}
		}
	}

	// Convert the map to a slice
	users := make([]models.User, 0, len(userMap))
	for _, user := range userMap {
		users = append(users, *user)
	}

	// Add repository ID to each user
	for i := range users {
		users[i].RepoID = repoId
	}

	return users, nil
}

// GetUsers returns all users from all repositories
func (c *DataController) GetUsers() []models.User {
	// If we have a cache, return it
	if len(c.usersCache) > 0 {
		return c.usersCache
	}

	// Get all repositories
	configList, _ := c.repoManager.GetConfigurations()
	configs := configList.Configs

	// Get users from all repositories
	var allUsers []models.User
	for _, config := range configs {
		// Get users for this repository
		usersForRepo, err := c.GetUsersForRepo(config.ID)
		if err == nil {
			allUsers = append(allUsers, usersForRepo...)
		}
	}

	// Update the cache
	c.usersCache = allUsers

	return allUsers
}

// RegisterRoutes registers the controller's routes
func (c *DataController) RegisterRoutes(router *gin.RouterGroup) {
	// Legacy routes under /data
	dataRoutes := router.Group("/data")
	{
		// Repository data endpoints
		dataRoutes.GET("/repositories/:id/groups", c.getGroups)
		dataRoutes.GET("/repositories/:id/groups/:lob", c.getGroupsByLOB)
		dataRoutes.GET("/repositories/:id/users", c.getUsers)
		dataRoutes.GET("/repositories/:id/users/:lob", c.getUsersByLOB)
		dataRoutes.GET("/repositories/:id/group-users/:groupName", c.getUniqueUsersForGroup)
		log.Printf("Registered route: GET /repositories/:id/group-users/:groupName")

		// Report generation endpoints
		dataRoutes.POST("/repositories/:id/reports/groups", c.generateGroupReport)
		dataRoutes.POST("/repositories/:id/reports/users", c.generateUserReport)

		// Repository-specific report presets
		dataRoutes.GET("/repositories/:id/reports/presets", c.getReportPresets)
		dataRoutes.GET("/repositories/:id/reports/presets/:presetId", c.getReportPreset)
		dataRoutes.GET("/repositories/:id/reports/presets/:presetId/versions", c.getReportPresetVersions)
		dataRoutes.POST("/repositories/:id/reports/presets", c.createReportPreset)
		dataRoutes.PUT("/repositories/:id/reports/presets/:presetId", c.updateReportPreset)
		dataRoutes.PUT("/repositories/:id/reports/presets/:presetId/toggle-activation", c.togglePresetActivation)
		dataRoutes.DELETE("/repositories/:id/reports/presets/:presetId", c.deleteReportPreset)

		// Repository-specific reports management
		dataRoutes.GET("/repositories/:id/reports", c.getReports)
		dataRoutes.POST("/repositories/:id/reports/generate", c.generateReport)
		dataRoutes.POST("/repositories/:id/reports/generate/:presetId", c.generateReportFromPreset)
		dataRoutes.DELETE("/repositories/:id/reports/:reportId", c.deleteReport)
		dataRoutes.POST("/repositories/:id/reports/batch-delete", c.batchDeleteReports)

		// Report executions
		dataRoutes.GET("/repositories/:id/reports/presets/:presetId/executions", c.getReportExecutionsByPreset)

		// Report download (not repository-specific)
		dataRoutes.GET("/reports/download/:filename", c.downloadReport)

		// Cache management
		dataRoutes.POST("/cache/clear", c.clearCache)
	}

	// New type-agnostic routes under /repo/:id
	repoRoutes := router.Group("/repo/:id")
	{
		// Repository data endpoints
		repoRoutes.GET("/groups", c.getGroups)
		repoRoutes.GET("/groups/:lob", c.getGroupsByLOB)
		repoRoutes.GET("/users", c.getUsers)
		repoRoutes.GET("/users/:lob", c.getUsersByLOB)
		repoRoutes.GET("/group-users/:groupName", c.getUniqueUsersForGroup)

		// Report generation endpoints
		repoRoutes.POST("/reports/groups", c.generateGroupReport)
		repoRoutes.POST("/reports/users", c.generateUserReport)

		// Repository-specific report presets
		repoRoutes.GET("/reports/presets", c.getReportPresets)
		repoRoutes.GET("/reports/presets/:presetId", c.getReportPreset)
		repoRoutes.GET("/reports/presets/:presetId/versions", c.getReportPresetVersions)
		repoRoutes.POST("/reports/presets", c.createReportPreset)
		repoRoutes.PUT("/reports/presets/:presetId", c.updateReportPreset)
		repoRoutes.PUT("/reports/presets/:presetId/toggle-activation", c.togglePresetActivation)
		repoRoutes.DELETE("/reports/presets/:presetId", c.deleteReportPreset)

		// Repository-specific reports management
		repoRoutes.GET("/reports", c.getReports)
		repoRoutes.POST("/reports/generate", c.generateReport)
		repoRoutes.POST("/reports/generate/:presetId", c.generateReportFromPreset)
		repoRoutes.DELETE("/reports/:reportId", c.deleteReport)
		repoRoutes.POST("/reports/batch-delete", c.batchDeleteReports)

		// Report executions
		repoRoutes.GET("/reports/presets/:presetId/executions", c.getReportExecutionsByPreset)
	}
}

// getGroups returns all groups from the repository
func (c *DataController) getGroups(ctx *gin.Context) {
	repoId := ctx.Param("id")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// Pagination parameters
	page := 1
	pageSize := 10
	if pageStr := ctx.Query("page"); pageStr != "" {
		if parsedPage, err := strconv.Atoi(pageStr); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if parsedPageSize, err := strconv.Atoi(pageSizeStr); err == nil && parsedPageSize > 0 {
			pageSize = parsedPageSize
		}
	}

	// Get search query parameter
	searchQuery := ctx.Query("search")

	// Create a cache key that includes search parameters
	cacheKey := fmt.Sprintf("groups_%s_search_%s_page_%d_size_%d", repoId, searchQuery, page, pageSize)

	// Check if we have this result cached
	if cachedData, found := c.dataProcessor.GetCachedResult(cacheKey); found {
		log.Printf("Cache hit for %s", cacheKey)
		ctx.JSON(http.StatusOK, cachedData)
		return
	}
	log.Printf("Cache miss for %s, processing query", cacheKey)

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	// Check if the repository path exists
	if _, err := os.Stat(repoPath); os.IsNotExist(err) {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Repository path does not exist: " + repoPath,
		})
		return
	}

	// Check for cancellation before parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled before JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Check for cancellation after parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Check for cancellation after resolving members
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after resolving members for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Apply search filter if provided
	var filteredGroups []models.Group
	if searchQuery == "" {
		// If no search, use all groups
		filteredGroups = groups
	} else {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter groups by search criteria
		for i, group := range groups {
			// Check for cancellation periodically during filtering
			if i > 0 && i%100 == 0 && api.CheckCancellation(ctx) {
				log.Printf("Request cancelled during search filtering for %s after processing %d items", ctx.Request.URL.Path, i)
				ctx.Abort()
				return
			}

			if c.MatchesAdvancedSearch(group, searchCriteria) {
				filteredGroups = append(filteredGroups, group)
			}
		}
	}

	// Apply pagination
	totalItems := len(filteredGroups)
	totalPages := (totalItems + pageSize - 1) / pageSize

	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= totalItems {
		// If start is beyond total items, return empty array
		ctx.JSON(http.StatusOK, gin.H{
			"items": []models.Group{},
			"pagination": gin.H{
				"page":       page,
				"pageSize":   pageSize,
				"totalItems": totalItems,
				"totalPages": totalPages,
			},
		})
		return
	}

	if end > totalItems {
		end = totalItems
	}

	pagedGroups := filteredGroups[start:end]

	// Prepare response
	response := gin.H{
		"items": pagedGroups,
		"pagination": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"totalItems": totalItems,
			"totalPages": totalPages,
		},
	}

	// Cache the result for future requests
	c.dataProcessor.SetCachedResult(cacheKey, response)
	log.Printf("Cached result for %s", cacheKey)

	// Return paginated results
	ctx.JSON(http.StatusOK, response)
}

// getGroupsByLOB returns groups filtered by LOB
func (c *DataController) getGroupsByLOB(ctx *gin.Context) {
	repoId := ctx.Param("id")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	lob := ctx.Param("lob")
	if lob == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "LOB parameter is required",
		})
		return
	}

	// Pagination parameters
	page := 1
	pageSize := 10
	if pageStr := ctx.Query("page"); pageStr != "" {
		if parsedPage, err := strconv.Atoi(pageStr); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if parsedPageSize, err := strconv.Atoi(pageSizeStr); err == nil && parsedPageSize > 0 {
			pageSize = parsedPageSize
		}
	}

	// Get search query parameter
	searchQuery := ctx.Query("search")

	// Create a cache key that includes search parameters
	cacheKey := fmt.Sprintf("groups_%s_lob_%s_search_%s_page_%d_size_%d", repoId, lob, searchQuery, page, pageSize)

	// Check if we have this result cached
	if cachedData, found := c.dataProcessor.GetCachedResult(cacheKey); found {
		log.Printf("Cache hit for %s", cacheKey)
		ctx.JSON(http.StatusOK, cachedData)
		return
	}
	log.Printf("Cache miss for %s, processing query", cacheKey)

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	// Check for cancellation before parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled before JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Check for cancellation after parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Check for cancellation after resolving members
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after resolving members for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// First, filter groups by LOB
	var lobGroups []models.Group
	for i, group := range groups {
		// Check for cancellation periodically during LOB filtering
		if i > 0 && i%100 == 0 && api.CheckCancellation(ctx) {
			log.Printf("Request cancelled during LOB filtering for %s after processing %d items", ctx.Request.URL.Path, i)
			ctx.Abort()
			return
		}

		if containsLOBIgnoreCase(group, lob) {
			lobGroups = append(lobGroups, group)
		}
	}

	// Apply search filter if provided
	var filteredGroups []models.Group
	if searchQuery == "" {
		// If no search, use all groups
		filteredGroups = lobGroups
	} else {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter groups by search criteria
		for i, group := range lobGroups {
			// Check for cancellation periodically during search filtering
			if i > 0 && i%100 == 0 && api.CheckCancellation(ctx) {
				log.Printf("Request cancelled during search filtering for %s after processing %d items", ctx.Request.URL.Path, i)
				ctx.Abort()
				return
			}

			if c.MatchesAdvancedSearch(group, searchCriteria) {
				filteredGroups = append(filteredGroups, group)
			}
		}
	}

	// Apply pagination
	totalItems := len(filteredGroups)
	totalPages := (totalItems + pageSize - 1) / pageSize

	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= totalItems {
		// If start is beyond total items, return empty array
		response := gin.H{
			"items": []models.Group{},
			"pagination": gin.H{
				"page":       page,
				"pageSize":   pageSize,
				"totalItems": totalItems,
				"totalPages": totalPages,
			},
		}

		// Cache the result for future requests
		c.dataProcessor.SetCachedResult(cacheKey, response)
		log.Printf("Cached empty result for %s", cacheKey)

		ctx.JSON(http.StatusOK, response)
		return
	}

	if end > totalItems {
		end = totalItems
	}

	pagedGroups := filteredGroups[start:end]

	// Prepare response
	response := gin.H{
		"items": pagedGroups,
		"pagination": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"totalItems": totalItems,
			"totalPages": totalPages,
		},
	}

	// Cache the result for future requests
	c.dataProcessor.SetCachedResult(cacheKey, response)
	log.Printf("Cached result for %s", cacheKey)

	// Return paginated results
	ctx.JSON(http.StatusOK, response)
}

// getUsers returns all users with their group memberships
func (c *DataController) getUsers(ctx *gin.Context) {
	repoId := ctx.Param("id")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// Pagination parameters
	page := 1
	pageSize := 10
	if pageStr := ctx.Query("page"); pageStr != "" {
		if parsedPage, err := strconv.Atoi(pageStr); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if parsedPageSize, err := strconv.Atoi(pageSizeStr); err == nil && parsedPageSize > 0 {
			pageSize = parsedPageSize
		}
	}

	// Get search query parameter
	searchQuery := ctx.Query("search")

	// Create a cache key that includes search parameters
	cacheKey := fmt.Sprintf("users_%s_search_%s_page_%d_size_%d", repoId, searchQuery, page, pageSize)

	// Check if we have this result cached
	if cachedData, found := c.dataProcessor.GetCachedResult(cacheKey); found {
		log.Printf("Cache hit for %s", cacheKey)
		ctx.JSON(http.StatusOK, cachedData)
		return
	}
	log.Printf("Cache miss for %s, processing query", cacheKey)

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	// Check for cancellation before parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled before JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Check for cancellation after parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Check for cancellation after resolving members
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after resolving members for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Create query params to extract users
	query := models.QueryParams{}

	// Use the enhanced ExtractUsers method that includes LOB and source file information
	users := c.dataProcessor.ExtractUsers(groups, query)

	// Apply search filter if provided
	var filteredUsers []models.User
	if searchQuery == "" {
		// If no search, use all users
		filteredUsers = users
	} else {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter users by search criteria
		for i, user := range users {
			// Check for cancellation periodically during filtering
			if i > 0 && i%100 == 0 && api.CheckCancellation(ctx) {
				log.Printf("Request cancelled during search filtering for %s after processing %d items", ctx.Request.URL.Path, i)
				ctx.Abort()
				return
			}

			if c.MatchesAdvancedUserSearch(user, searchCriteria) {
				filteredUsers = append(filteredUsers, user)
			}
		}
	}

	// Apply pagination
	totalItems := len(filteredUsers)
	totalPages := (totalItems + pageSize - 1) / pageSize

	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= totalItems {
		// If start is beyond total items, return empty array
		response := gin.H{
			"items": []models.User{},
			"pagination": gin.H{
				"page":       page,
				"pageSize":   pageSize,
				"totalItems": totalItems,
				"totalPages": totalPages,
			},
		}

		// Cache the result for future requests
		c.dataProcessor.SetCachedResult(cacheKey, response)
		log.Printf("Cached empty result for %s", cacheKey)

		ctx.JSON(http.StatusOK, response)
		return
	}

	if end > totalItems {
		end = totalItems
	}

	pagedUsers := filteredUsers[start:end]

	// Prepare response
	response := gin.H{
		"items": pagedUsers,
		"pagination": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"totalItems": totalItems,
			"totalPages": totalPages,
		},
	}

	// Cache the result for future requests
	c.dataProcessor.SetCachedResult(cacheKey, response)
	log.Printf("Cached result for %s", cacheKey)

	// Return paginated results
	ctx.JSON(http.StatusOK, response)
}

// getUsersByLOB returns users with their group memberships, filtered by LOB
func (c *DataController) getUsersByLOB(ctx *gin.Context) {
	repoId := ctx.Param("id")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	lob := ctx.Param("lob")
	if lob == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "LOB parameter is required",
		})
		return
	}

	// Pagination parameters
	page := 1
	pageSize := 10
	if pageStr := ctx.Query("page"); pageStr != "" {
		if parsedPage, err := strconv.Atoi(pageStr); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if parsedPageSize, err := strconv.Atoi(pageSizeStr); err == nil && parsedPageSize > 0 {
			pageSize = parsedPageSize
		}
	}

	// Get search query parameter
	searchQuery := ctx.Query("search")

	// Create a cache key that includes search parameters
	cacheKey := fmt.Sprintf("users_%s_lob_%s_search_%s_page_%d_size_%d", repoId, lob, searchQuery, page, pageSize)

	// Check if we have this result cached
	if cachedData, found := c.dataProcessor.GetCachedResult(cacheKey); found {
		log.Printf("Cache hit for %s", cacheKey)
		ctx.JSON(http.StatusOK, cachedData)
		return
	}
	log.Printf("Cache miss for %s, processing query", cacheKey)

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	// Check for cancellation before parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled before JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Check for cancellation after parsing JSON files
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after JSON parsing for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Check for cancellation after resolving members
	if api.CheckCancellation(ctx) {
		log.Printf("Request cancelled after resolving members for %s", ctx.Request.URL.Path)
		ctx.Abort()
		return
	}

	// Create query params with LOB filter to extract users
	query := models.QueryParams{
		LOB: lob,
	}

	// Use the enhanced ExtractUsers method that includes LOB and source file information
	users := c.dataProcessor.ExtractUsers(groups, query)

	// Apply search filter if provided
	var filteredUsers []models.User
	if searchQuery == "" {
		// If no search, use all users
		filteredUsers = users
	} else {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter users by search criteria
		for i, user := range users {
			// Check for cancellation periodically during filtering
			if i > 0 && i%100 == 0 && api.CheckCancellation(ctx) {
				log.Printf("Request cancelled during search filtering for %s after processing %d items", ctx.Request.URL.Path, i)
				ctx.Abort()
				return
			}

			if c.MatchesAdvancedUserSearch(user, searchCriteria) {
				filteredUsers = append(filteredUsers, user)
			}
		}
	}

	// Apply pagination
	totalItems := len(filteredUsers)
	totalPages := (totalItems + pageSize - 1) / pageSize

	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= totalItems {
		// If start is beyond total items, return empty array
		response := gin.H{
			"items": []models.User{},
			"pagination": gin.H{
				"page":       page,
				"pageSize":   pageSize,
				"totalItems": totalItems,
				"totalPages": totalPages,
			},
		}

		// Cache the result for future requests
		c.dataProcessor.SetCachedResult(cacheKey, response)
		log.Printf("Cached empty result for %s", cacheKey)

		ctx.JSON(http.StatusOK, response)
		return
	}

	if end > totalItems {
		end = totalItems
	}

	pagedUsers := filteredUsers[start:end]

	// Prepare response
	response := gin.H{
		"items": pagedUsers,
		"pagination": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"totalItems": totalItems,
			"totalPages": totalPages,
		},
	}

	// Cache the result for future requests
	c.dataProcessor.SetCachedResult(cacheKey, response)
	log.Printf("Cached result for %s", cacheKey)

	// Return paginated results
	ctx.JSON(http.StatusOK, response)
}

// generateGroupReport generates a report for groups
func (c *DataController) generateGroupReport(ctx *gin.Context) {
	repoId := ctx.Param("id")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	var params struct {
		LOB      string `json:"lob"`
		Filename string `json:"filename,omitempty"`
	}

	if err := ctx.BindJSON(&params); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	if params.LOB == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "LOB parameter is required",
		})
		return
	}

	// Generate filename if not provided
	if params.Filename == "" {
		params.Filename = params.LOB + "_groups_" + services.GetCurrentDate() + ".json"
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Apply search filter if provided
	var filteredGroups []models.Group
	if searchQuery := ctx.Query("search"); searchQuery != "" {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter groups by search criteria
		for _, group := range groups {
			if c.MatchesAdvancedSearch(group, searchCriteria) {
				filteredGroups = append(filteredGroups, group)
			}
		}
	} else {
		// If no search, use all groups
		filteredGroups = groups
	}

	// Generate the report
	err = c.dataProcessor.GenerateGroupReport(filteredGroups, params.LOB, params.Filename)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate report: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":    "Report generated successfully",
		"filename":   params.Filename,
		"reportPath": filepath.Join(c.dataProcessor.GetReportsFolder(), params.Filename),
	})
}

// generateUserReport generates a report for users
func (c *DataController) generateUserReport(ctx *gin.Context) {
	repoId := ctx.Param("id")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	var params struct {
		LOB      string `json:"lob"`
		Filename string `json:"filename,omitempty"`
	}

	if err := ctx.BindJSON(&params); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	// Generate filename if not provided
	if params.Filename == "" {
		params.Filename = "users_" + services.GetCurrentDate() + ".json"
		if params.LOB != "" {
			params.Filename = params.LOB + "_" + params.Filename
		}
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Get all unique users with their group memberships
	processedMembers := make(map[string]bool)
	var users []models.User

	for _, group := range groups {
		for _, member := range group.Members {
			// Only process user members, not group references
			if member.Type == models.UserMemberType {
				memberName := member.Name
				if !processedMembers[memberName] {
					memberGroups := c.dataProcessor.FindGroupsForMember(groups, memberName)
					if len(memberGroups) > 0 {
						users = append(users, models.User{
							Name:   memberName,
							Groups: memberGroups,
						})
					}
					processedMembers[memberName] = true
				}
			}
		}
	}

	// Apply search filter if provided
	var filteredUsers []models.User
	if searchQuery := ctx.Query("search"); searchQuery != "" {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter users by search criteria
		for _, user := range users {
			if c.MatchesAdvancedUserSearch(user, searchCriteria) {
				filteredUsers = append(filteredUsers, user)
			}
		}
	} else {
		// If no search, use all users
		filteredUsers = users
	}

	// Generate the report
	err = c.dataProcessor.GenerateUserReport(groups, params.LOB, params.Filename)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate report: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":    "Report generated successfully",
		"filename":   params.Filename,
		"reportPath": filepath.Join(c.dataProcessor.GetReportsFolder(), params.Filename),
	})
}

// Note: The listReports method was removed as it was not registered in the router
// and has been replaced by the getReports method.

// getReportPresets returns all report presets for a specific repository
func (c *DataController) getReportPresets(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presets, err := c.dataProcessor.GetPresets()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get report presets: " + err.Error(),
		})
		return
	}

	// Filter presets by repository ID and ensure they have valid IDs
	var filteredPresets []models.ReportPreset
	var invalidCount int
	for _, preset := range presets {
		// Skip presets without IDs
		if preset.ID == "" {
			invalidCount++
			continue
		}

		// Include presets that match the repository ID or have no repository ID (for backward compatibility)
		if preset.RepositoryID == repoID || preset.RepositoryID == "" {
			filteredPresets = append(filteredPresets, preset)
		}
	}

	if invalidCount > 0 {
		log.Printf("Warning: Found %d presets with missing IDs that were ignored", invalidCount)
	}

	// Always return an empty array instead of null if no presets match
	if filteredPresets == nil {
		filteredPresets = []models.ReportPreset{}
	}

	log.Printf("Returning %d presets for repository %s", len(filteredPresets), repoID)
	ctx.JSON(http.StatusOK, filteredPresets)
}

// getReportPreset returns a specific report preset by ID
func (c *DataController) getReportPreset(ctx *gin.Context) {
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presetID := ctx.Param("presetId")
	if presetID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	preset, err := c.dataProcessor.GetPreset(presetID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report preset not found: " + err.Error(),
		})
		return
	}

	// Verify that the preset belongs to the specified repository
	if preset.RepositoryID != "" && preset.RepositoryID != repoID {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report preset not found in the specified repository",
		})
		return
	}

	ctx.JSON(http.StatusOK, preset)
}

// getReportPresetVersions returns all versions of a report preset
func (c *DataController) getReportPresetVersions(ctx *gin.Context) {
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presetID := ctx.Param("presetId")
	if presetID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	versions, err := c.dataProcessor.GetPresetVersions(presetID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Failed to get preset versions: " + err.Error(),
		})
		return
	}

	// Filter versions by repository ID
	var filteredVersions []models.ReportPreset
	for _, version := range versions {
		// Include versions that match the repository ID or have no repository ID (for backward compatibility)
		if version.RepositoryID == repoID || version.RepositoryID == "" {
			filteredVersions = append(filteredVersions, version)
		}
	}

	ctx.JSON(http.StatusOK, filteredVersions)
}

// createReportPreset creates a new report preset
func (c *DataController) createReportPreset(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	var preset models.ReportPreset
	if err := ctx.BindJSON(&preset); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	// Clear ID to ensure it's created as new
	preset.ID = ""

	// Set the repository ID from the path parameter
	preset.RepositoryID = repoID

	// Validate that the repository exists
	_, err := c.repoManager.GetRepository(repoID)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid repository ID: " + err.Error(),
		})
		return
	}

	savedPreset, err := c.dataProcessor.SavePreset(preset)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to save preset: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, savedPreset)
}

// updateReportPreset updates an existing report preset
func (c *DataController) updateReportPreset(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presetID := ctx.Param("presetId")
	if presetID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	var preset models.ReportPreset
	if err := ctx.BindJSON(&preset); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	// Ensure ID matches the URL param
	preset.ID = presetID

	// Set the repository ID from the path parameter
	preset.RepositoryID = repoID

	// Check if preset exists
	existingPreset, err := c.dataProcessor.GetPreset(presetID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report preset not found",
		})
		return
	}

	// Verify that the preset belongs to the specified repository
	if existingPreset.RepositoryID != "" && existingPreset.RepositoryID != repoID {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report preset not found in the specified repository",
		})
		return
	}

	updatedPreset, err := c.dataProcessor.SavePreset(preset)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update preset: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, updatedPreset)
}

// togglePresetActivation toggles the activation status of a report preset
func (c *DataController) togglePresetActivation(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presetID := ctx.Param("presetId")
	if presetID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	// Check if preset exists and belongs to the repository
	existingPreset, err := c.dataProcessor.GetPreset(presetID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report preset not found",
		})
		return
	}

	// Verify that the preset belongs to the specified repository
	if existingPreset.RepositoryID != "" && existingPreset.RepositoryID != repoID {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report preset not found in the specified repository",
		})
		return
	}

	updatedPreset, err := c.dataProcessor.TogglePresetActivation(presetID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to toggle preset activation: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, updatedPreset)
}

// deleteReportPreset deletes a report preset (now just deactivates it)
func (c *DataController) deleteReportPreset(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presetID := ctx.Param("presetId")
	if presetID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	// For testing, we only need the DeletePreset method
	// Delete or deactivate the preset, passing the scheduler service to delete execution history
	err := c.dataProcessor.DeletePreset(presetID, c.schedulerService)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete/deactivate preset: " + err.Error(),
		})
		return
	}

	// For testing, assume no reports
	hasReports := false

	if hasReports {
		ctx.JSON(http.StatusOK, gin.H{
			"message": "Preset deactivated successfully",
			"action":  "deactivated",
		})
	} else {
		ctx.JSON(http.StatusOK, gin.H{
			"message": "Preset deleted successfully",
			"action":  "deleted",
		})
	}
}

// getReports returns all reports for a specific repository
func (c *DataController) getReports(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	reports, err := c.dataProcessor.GetReports()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get reports: " + err.Error(),
		})
		return
	}

	// Filter reports by repository ID and ensure they have valid IDs
	var filteredReports []models.Report
	var invalidCount int
	for _, report := range reports {
		// Skip reports without IDs or preset IDs
		if report.ID == "" || report.PresetID == "" {
			invalidCount++
			continue
		}

		// Include reports that match the repository ID or have no repository ID (for backward compatibility)
		if report.RepositoryID == repoID || report.RepositoryID == "" {
			filteredReports = append(filteredReports, report)
			// Only log reports that match the filter criteria
			log.Printf("Report for repository %s: ID=%s, PresetID=%s, SharedPresetID=%s",
				repoID, report.ID, report.PresetID, report.SharedPresetID)
		}
	}

	if invalidCount > 0 {
		log.Printf("Warning: Found %d reports with missing IDs or preset IDs that were ignored", invalidCount)
	}

	// Always return an empty array instead of null if no reports match
	if filteredReports == nil {
		filteredReports = []models.Report{}
	}

	log.Printf("Returning %d reports for repository %s", len(filteredReports), repoID)
	ctx.JSON(http.StatusOK, filteredReports)
}

// getReportExecutionsByPreset returns report executions for a specific preset or shared preset ID
func (c *DataController) getReportExecutionsByPreset(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// The ID from the URL could be either a preset ID or a shared preset ID
	idFromURL := ctx.Param("presetId")
	if idFromURL == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	log.Printf("getReportExecutionsByPreset called for repository ID: %s, ID from URL: %s", repoID, idFromURL)

	if c.schedulerService == nil {
		log.Printf("ERROR: Scheduler service not initialized")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Scheduler service not initialized",
		})
		return
	}

	// First, try to get executions directly using the ID from the URL as a shared ID
	// This is the most common case from the frontend
	executions := c.schedulerService.GetExecutions(idFromURL)
	if len(executions) > 0 {
		log.Printf("Found %d executions using ID from URL as shared ID: %s", len(executions), idFromURL)
		ctx.JSON(http.StatusOK, executions)
		return
	}

	// If no executions found and we have a data processor, try to look up the preset
	if c.dataProcessor != nil {
		// Check if a preset exists with this ID
		preset, err := c.dataProcessor.GetPreset(idFromURL)
		if err == nil {
			// Verify that the preset belongs to the specified repository
			if preset.RepositoryID != "" && preset.RepositoryID != repoID {
				ctx.JSON(http.StatusNotFound, gin.H{
					"error": "Preset not found in the specified repository",
				})
				return
			}

			// If the preset has a shared ID, use it to get executions
			if preset.SharedID != "" {
				log.Printf("Found preset with ID %s, using its shared ID %s to get executions", idFromURL, preset.SharedID)
				executions = c.schedulerService.GetExecutions(preset.SharedID)
				log.Printf("Got %d executions for shared ID: %s", len(executions), preset.SharedID)
			} else {
				// If no shared ID, filter all executions manually by preset ID
				log.Printf("Preset has no shared ID, filtering executions for preset ID: %s", idFromURL)
				allExecutions := c.schedulerService.GetExecutions()
				for _, execution := range allExecutions {
					if execution.PresetID == idFromURL {
						executions = append(executions, execution)
					}
				}
				log.Printf("Got %d executions for preset ID: %s", len(executions), idFromURL)
			}
		} else {
			log.Printf("No preset found with ID %s, returning empty executions array", idFromURL)
		}
	}

	// Always return an empty array instead of null
	if executions == nil {
		log.Printf("Executions is nil, returning empty array")
		executions = []models.ReportExecution{}
	}

	ctx.JSON(http.StatusOK, executions)
	log.Printf("Returned %d executions for ID from URL: %s", len(executions), idFromURL)
}

// generateReport generates a report based on query parameters
func (c *DataController) generateReport(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	var params struct {
		ReportType string             `json:"reportType"`
		Query      models.QueryParams `json:"query"`
	}

	if err := ctx.BindJSON(&params); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Apply search filter if provided
	var filteredGroups []models.Group
	if searchQuery := ctx.Query("search"); searchQuery != "" {
		// Parse search query
		searchCriteria := c.ParseSearch(searchQuery)

		// Filter groups by search criteria
		for _, group := range groups {
			if c.MatchesAdvancedSearch(group, searchCriteria) {
				filteredGroups = append(filteredGroups, group)
			}
		}
	} else {
		// If no search, use all groups
		filteredGroups = groups
	}

	// Generate the report with repository ID for enhanced features
	report, err := c.dataProcessor.GenerateReportWithRepoID(filteredGroups, params.ReportType, params.Query, "", "", repoID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate report: " + err.Error(),
		})
		return
	}

	// Set the repository ID
	report.RepositoryID = repoID

	// Update the report metadata
	if err := c.dataProcessor.SaveReportMetadata(report); err != nil {
		log.Printf("Warning: Failed to update report metadata with repository ID: %v", err)
	}

	ctx.JSON(http.StatusOK, report)
}

// generateReportFromPreset generates a report based on a preset
func (c *DataController) generateReportFromPreset(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	presetID := ctx.Param("presetId")
	if presetID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Preset ID is required",
		})
		return
	}

	// Get the preset
	preset, err := c.dataProcessor.GetPreset(presetID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Preset not found: " + err.Error(),
		})
		return
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Apply search filter if available
	var filteredGroups []models.Group
	if preset.SearchQuery != "" {
		// Use the search service to filter groups
		searchQuery := preset.SearchQuery

		// Add repository filter to the query
		if searchQuery != "" {
			searchQuery = fmt.Sprintf("(%s) AND repoId:%s", searchQuery, repoID)
		} else {
			searchQuery = fmt.Sprintf("repoId:%s", repoID)
		}

		// Use the search service to filter groups
		filteredGroups, err = c.searchService.SearchGroups(ctx, searchQuery, groups)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to search groups: " + err.Error(),
			})
			return
		}
	} else {
		// Fall back to using the query parameters
		filteredGroups = c.dataProcessor.FilterGroups(groups, preset.Query)
	}

	// Add debug logging
	log.Printf("Generating report from preset %s with %d filtered groups", preset.ID, len(filteredGroups))

	// Generate the report with repository ID for enhanced features
	report, err := c.dataProcessor.GenerateReportWithRepoID(filteredGroups, preset.ReportType, preset.Query, preset.ID, preset.Name, repoID)
	if err != nil {
		log.Printf("Error generating report from preset %s: %v", preset.ID, err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate report: " + err.Error(),
		})
		return
	}

	// Set the repository ID
	report.RepositoryID = repoID

	// Update the report metadata
	if err := c.dataProcessor.SaveReportMetadata(report); err != nil {
		log.Printf("Warning: Failed to update report metadata with repository ID: %v", err)
	}

	log.Printf("Successfully generated report %s from preset %s", report.ID, preset.ID)

	ctx.JSON(http.StatusOK, report)
}

// deleteReport deletes a report
func (c *DataController) deleteReport(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	reportID := ctx.Param("reportId")
	if reportID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Report ID is required",
		})
		return
	}

	// Check if report exists and belongs to the repository
	reports, err := c.dataProcessor.GetReports()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get reports: " + err.Error(),
		})
		return
	}

	var reportFound bool
	for _, report := range reports {
		if report.ID == reportID {
			// Verify that the report belongs to the specified repository
			if report.RepositoryID != "" && report.RepositoryID != repoID {
				ctx.JSON(http.StatusNotFound, gin.H{
					"error": "Report not found in the specified repository",
				})
				return
			}
			reportFound = true
			break
		}
	}

	if !reportFound {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Report not found",
		})
		return
	}

	err = c.dataProcessor.DeleteReport(reportID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete report: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Report deleted successfully",
	})
}

// batchDeleteReports deletes multiple reports in a single operation
func (c *DataController) batchDeleteReports(ctx *gin.Context) {
	// Get repository ID from path parameter
	repoID := ctx.Param("id")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	var params struct {
		ReportIDs []string `json:"reportIds"`
	}

	if err := ctx.BindJSON(&params); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request: " + err.Error(),
		})
		return
	}

	if len(params.ReportIDs) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "No report IDs provided",
		})
		return
	}

	// Check if reports exist and belong to the repository
	reports, err := c.dataProcessor.GetReports()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get reports: " + err.Error(),
		})
		return
	}

	// Filter report IDs to only include those that belong to the repository
	var validReportIDs []string
	for _, reportID := range params.ReportIDs {
		for _, report := range reports {
			if report.ID == reportID {
				// Include reports that match the repository ID or have no repository ID (for backward compatibility)
				if report.RepositoryID == repoID || report.RepositoryID == "" {
					validReportIDs = append(validReportIDs, reportID)
				}
				break
			}
		}
	}

	if len(validReportIDs) == 0 {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "No valid reports found in the specified repository",
		})
		return
	}

	result, err := c.dataProcessor.BatchDeleteReports(validReportIDs)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete reports: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, result)
}

// downloadReport serves a report file for download
func (c *DataController) downloadReport(ctx *gin.Context) {
	filename := ctx.Param("filename")

	filePath := filepath.Join(c.dataProcessor.GetReportsFolder(), filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "File not found",
		})
		return
	}

	// Set appropriate headers for download
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.Header("Content-Type", "application/json")
	ctx.File(filePath)
}

// clearCache handles clearing the cache
func (c *DataController) clearCache(ctx *gin.Context) {
	// Optional parameter to clear specific cache pattern
	keyPattern := ctx.Query("pattern")

	// Clear cache in the data processor
	c.dataProcessor.ClearCache(keyPattern)

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Cache cleared successfully",
		"pattern": keyPattern,
	})
}

// Helper functions for search

// ParseSearch parses a search query into a structured search format
// Handles:
// - Column-specific searches with different matching modes:
//   - Exact match (default): column:value or column:"value"
//   - Contains match: column:~value
//   - Prefix match: column:value*
//
// - Exact word matching ("exact word")
// - Multiple search terms (term1 and term2)
func (c *DataController) ParseSearch(searchQuery string) map[string][]string {
	result, err := c.searchService.ParseQueryToMap(searchQuery)
	if err != nil {
		log.Printf("Error parsing search query: %v", err)
		// Return empty result on error
		result = make(map[string][]string)
		result["_any"] = []string{}
	}
	return result
}

// MatchesAdvancedSearch checks if a group matches the advanced search criteria
func (c *DataController) MatchesAdvancedSearch(group models.Group, searchCriteria map[string][]string) bool {
	return c.searchService.MatchesAdvancedSearch(group, searchCriteria)
}

// MatchesAdvancedUserSearch checks if a user matches the advanced search criteria
func (c *DataController) MatchesAdvancedUserSearch(user models.User, searchCriteria map[string][]string) bool {
	return c.searchService.MatchesAdvancedUserSearch(user, searchCriteria)
}

// containsIgnoreCase checks if a string contains a substring case-insensitively
func containsIgnoreCase(s string, substr string) bool {
	if s == "" || substr == "" {
		return false
	}
	s = strings.ToLower(s)
	substr = strings.ToLower(substr)
	return strings.Contains(s, substr)
}

// containsLOBIgnoreCase checks if the LOB field matches
func containsLOBIgnoreCase(group models.Group, substr string) bool {
	return containsIgnoreCase(group.Lob, substr)
}

// Note: The containsMemberIgnoreCase function was removed as it was not used anywhere in the codebase.

// getUniqueUsersForGroup returns unique users with their group memberships for a specific group
func (c *DataController) getUniqueUsersForGroup(ctx *gin.Context) {
	log.Printf("getUniqueUsersForGroup called")
	repoId := ctx.Param("id")
	if repoId == "" {
		log.Printf("Repository ID is missing")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	groupName := ctx.Param("groupName")
	if groupName == "" {
		log.Printf("Group name is missing")
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Group name is required",
		})
		return
	}
	log.Printf("Processing request for repo: %s, group: %s", repoId, groupName)

	// Get the repository
	repo, err := c.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get repository: " + err.Error(),
		})
		return
	}

	repoPath := repo.GetLocalRepoPath()

	groups, err := c.dataProcessor.ParseJSONFiles(repoPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse repository data: " + err.Error(),
		})
		return
	}

	// Resolve group references in members
	c.dataProcessor.ResolveMembers(groups)

	// Find the specific group
	var targetGroup *models.Group
	log.Printf("Looking for group: %s among %d groups", groupName, len(groups))
	for i, group := range groups {
		log.Printf("Checking group: %s", group.Groupname)
		if group.Groupname == groupName {
			targetGroup = &groups[i]
			log.Printf("Found target group: %s", group.Groupname)
			break
		}
	}

	if targetGroup == nil {
		log.Printf("Group not found: %s", groupName)
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Group not found",
		})
		return
	}

	// Enrich membership info to get resolved members
	c.dataProcessor.EnrichMembershipInfo([]models.Group{*targetGroup})
	log.Printf("After enrichment, target group has %d resolved members",
		func() int {
			if targetGroup.ResolvedMembers != nil {
				return len(targetGroup.ResolvedMembers.Users)
			}
			return 0
		}())

	// Extract unique users with their group memberships
	uniqueUsers := make(map[string]*models.User)

	if targetGroup.ResolvedMembers != nil {
		log.Printf("Processing %d resolved members", len(targetGroup.ResolvedMembers.Users))
		for _, resolvedUser := range targetGroup.ResolvedMembers.Users {
			userName := resolvedUser.Name
			log.Printf("Processing resolved user: %s", userName)

			// Skip if this is actually a group name
			isGroup := false
			for _, group := range groups {
				if strings.EqualFold(group.Groupname, userName) {
					isGroup = true
					log.Printf("Skipping %s as it's a group name", userName)
					break
				}
			}
			if isGroup {
				continue
			}

			// Get or create user entry
			if _, exists := uniqueUsers[userName]; !exists {
				// Find all groups this user belongs to
				userGroups := c.dataProcessor.FindGroupsForMember(groups, userName)
				log.Printf("User %s belongs to %d groups: %v", userName, len(userGroups), userGroups)

				uniqueUsers[userName] = &models.User{
					Name:   userName,
					Groups: userGroups,
				}
			}
		}
	} else {
		log.Printf("No resolved members found for group %s", targetGroup.Groupname)
	}

	// Convert map to slice and sort
	users := make([]models.User, 0, len(uniqueUsers))
	for _, user := range uniqueUsers {
		users = append(users, *user)
	}

	// Sort users by name
	sort.Slice(users, func(i, j int) bool {
		return users[i].Name < users[j].Name
	})

	ctx.JSON(http.StatusOK, gin.H{
		"users": users,
		"total": len(users),
	})
}
