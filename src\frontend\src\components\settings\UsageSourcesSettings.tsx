import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Loader2,
  Plus,
  Pencil,
  Trash,
  TestTube,
  MoreHorizontal,
  AlertCircle,
  CheckCircle,
  Clock,
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { apiClient } from '@/api/client';
import type {
  UsageSource,
  UsageSourcesResponse,
  UsageSourceStatisticsResponse,
  TestConnectionResponse
} from '@/types/usage';
import { getSourceTypeIcon, getSourceTypeColor, formatScanFrequency, formatTimeAgo } from '@/types/usage';
import UsageSourceForm from './UsageSourceForm';

const UsageSourcesSettings: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [sources, setSources] = useState<UsageSource[]>([]);
  const [statistics, setStatistics] = useState<UsageSourceStatisticsResponse | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingSource, setEditingSource] = useState<UsageSource | null>(null);
  const [deleteSource, setDeleteSource] = useState<UsageSource | null>(null);
  const [testingSource, setTestingSource] = useState<string | null>(null);

  // Load sources and statistics on component mount
  useEffect(() => {
    loadSources();
    loadStatistics();
  }, []);

  const loadSources = async () => {
    try {
      setIsLoading(true);
      const response: UsageSourcesResponse = await apiClient.usageSources.getAll();
      setSources(response.sources);
    } catch (error) {
      console.error('Failed to load usage sources:', error);
      toast({
        title: 'Error',
        description: 'Failed to load usage sources',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await apiClient.usageSources.getStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Failed to load usage source statistics:', error);
    }
  };

  const handleAddSource = () => {
    setEditingSource(null);
    setShowForm(true);
  };

  const handleEditSource = (source: UsageSource) => {
    setEditingSource(source);
    setShowForm(true);
  };

  const handleDeleteSource = async (source: UsageSource) => {
    try {
      await apiClient.usageSources.delete(source.id);

      // Immediately remove the source from the local sources array
      setSources(prevSources => prevSources.filter(s => s.id !== source.id));

      toast({
        title: 'Success',
        description: `Usage source "${source.name}" deleted successfully`,
      });

      // Still call loadSources and loadStatistics for consistency
      loadSources();
      loadStatistics();
    } catch (error) {
      console.error('Failed to delete usage source:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete usage source',
        variant: 'destructive',
      });
    } finally {
      setDeleteSource(null);
    }
  };

  const handleTestConnection = async (source: UsageSource) => {
    try {
      setTestingSource(source.id);
      const response: TestConnectionResponse = await apiClient.usageSources.test(source.id);

      if (response.success) {
        toast({
          title: 'Connection Test Successful',
          description: `Successfully connected to "${source.name}"`,
        });
      } else {
        toast({
          title: 'Connection Test Failed',
          description: response.status.error || 'Connection test failed',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Failed to test connection:', error);
      toast({
        title: 'Connection Test Failed',
        description: 'Failed to test connection',
        variant: 'destructive',
      });
    } finally {
      setTestingSource(null);
    }
  };

  const handleToggleActive = async (source: UsageSource, active: boolean) => {
    try {
      const updatedSource = { ...source, isActive: active };
      const apiResponse = await apiClient.usageSources.update(source.id, updatedSource);

      // Immediately update the local sources array with the response
      setSources(prevSources =>
        prevSources.map(s =>
          s.id === source.id ? apiResponse : s
        )
      );

      toast({
        title: 'Success',
        description: `Usage source "${source.name}" ${active ? 'activated' : 'deactivated'}`,
      });

      // Still call loadSources and loadStatistics for consistency
      loadSources();
      loadStatistics();
    } catch (error) {
      console.error('Failed to update usage source:', error);
      toast({
        title: 'Error',
        description: 'Failed to update usage source',
        variant: 'destructive',
      });
    }
  };

  const handleFormSubmit = async (sourceData: Omit<UsageSource, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      if (editingSource) {
        // Update existing source
        const updatedSource = { ...editingSource, ...sourceData };
        const apiResponse = await apiClient.usageSources.update(editingSource.id, updatedSource);

        // Immediately update the local sources array with the response
        setSources(prevSources =>
          prevSources.map(source =>
            source.id === editingSource.id ? apiResponse : source
          )
        );

        toast({
          title: 'Success',
          description: `Usage source "${sourceData.name}" updated successfully`,
        });
      } else {
        // Create new source
        const newSource = await apiClient.usageSources.create(sourceData);

        // Add the new source to the local sources array
        setSources(prevSources => [...prevSources, newSource]);

        toast({
          title: 'Success',
          description: `Usage source "${sourceData.name}" created successfully`,
        });
      }

      setShowForm(false);
      setEditingSource(null);

      // Still call loadSources and loadStatistics for consistency, but the UI will already be updated
      loadSources();
      loadStatistics();
    } catch (error) {
      console.error('Failed to save usage source:', error);
      toast({
        title: 'Error',
        description: 'Failed to save usage source',
        variant: 'destructive',
      });
    }
  };

  const renderSourceTypeBadge = (type: string) => {
    return (
      <Badge variant="outline" className={getSourceTypeColor(type as any)}>
        <span className="mr-1">{getSourceTypeIcon(type as any)}</span>
        {type.toUpperCase()}
      </Badge>
    );
  };

  const renderStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'outline'}>
        {isActive ? (
          <>
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </>
        ) : (
          <>
            <AlertCircle className="w-3 h-3 mr-1" />
            Inactive
          </>
        )}
      </Badge>
    );
  };

  if (showForm) {
    return (
      <UsageSourceForm
        source={editingSource}
        onSubmit={handleFormSubmit}
        onCancel={() => {
          setShowForm(false);
          setEditingSource(null);
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Usage Sources</CardTitle>
              <CardDescription>
                Configure external sources to scan for group usage references
              </CardDescription>
            </div>
            <Button onClick={handleAddSource} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              Add Source
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Statistics */}
          {statistics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{statistics.totalSources}</div>
                <div className="text-sm text-blue-600">Total Sources</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{statistics.activeSources}</div>
                <div className="text-sm text-green-600">Active Sources</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {Object.keys(statistics.sourcesByType).length}
                </div>
                <div className="text-sm text-purple-600">Source Types</div>
              </div>
            </div>
          )}

          {isLoading && (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          )}

          {!isLoading && sources.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No usage sources configured yet.</p>
              <Button onClick={handleAddSource} className="mt-4" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Source
              </Button>
            </div>
          )}

          {!isLoading && sources.length > 0 && (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Scan Frequency</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sources.map((source) => (
                    <TableRow key={source.id}>
                      <TableCell className="font-medium">{source.name}</TableCell>
                      <TableCell>{renderSourceTypeBadge(source.type)}</TableCell>
                      <TableCell>{renderStatusBadge(source.isActive)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1 text-muted-foreground" />
                          {formatScanFrequency(source.scanFrequency)}
                        </div>
                      </TableCell>
                      <TableCell>{formatTimeAgo(source.updatedAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Switch
                            checked={source.isActive}
                            onCheckedChange={(checked) => handleToggleActive(source, checked)}
                            disabled={isLoading}
                          />
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleTestConnection(source)}>
                                {testingSource === source.id ? (
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                  <TestTube className="h-4 w-4 mr-2" />
                                )}
                                Test Connection
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditSource(source)}>
                                <Pencil className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => setDeleteSource(source)}
                                className="text-red-600"
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteSource} onOpenChange={() => setDeleteSource(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Usage Source</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the usage source "{deleteSource?.name}"?
              This action cannot be undone and will remove all associated scan results.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteSource && handleDeleteSource(deleteSource)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default UsageSourcesSettings;
