package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
	"github.com/philippgille/chromem-go"
)

// VectorDatabaseService manages vector database operations for scan history and logs using chromem-go
type VectorDatabaseService struct {
	db          *chromem.DB
	mutex       sync.RWMutex
	initialized bool
	dataDir     string
	collections map[string]*chromem.Collection
}

// Collection names
const (
	ScanHistoryCollection   = "scan_history"
	ScanLogsCollection      = "scan_logs"
	GroupMetadataCollection = "group_metadata"
	PerformanceCollection   = "performance_metrics"
)

// ScanPerformanceMetrics represents performance metrics for scanning operations
type ScanPerformanceMetrics struct {
	FilesScanned    int     `json:"filesScanned"`
	LinesProcessed  int     `json:"linesProcessed"`
	AvgFileSize     int64   `json:"avgFileSize"`
	ProcessingSpeed float64 `json:"processingSpeed"`
	MemoryUsage     int64   `json:"memoryUsage"`
	AuthCacheHits   int     `json:"authCacheHits"`
	AuthCacheMisses int     `json:"authCacheMisses"`
}

// ScanHistoryEntry represents a scan history entry for vector storage
type ScanHistoryEntry struct {
	ID            string                 `json:"id"`
	GroupName     string                 `json:"groupName"`
	RepoID        string                 `json:"repoId"`
	ScanType      string                 `json:"scanType"` // "manual", "auto", "scheduled"
	Status        string                 `json:"status"`   // "completed", "failed", "cancelled"
	StartTime     time.Time              `json:"startTime"`
	EndTime       time.Time              `json:"endTime"`
	Duration      int64                  `json:"duration"` // milliseconds
	ResultsCount  int                    `json:"resultsCount"`
	ErrorMessage  string                 `json:"errorMessage,omitempty"`
	SourcesTotal  int                    `json:"sourcesTotal"`
	SourcesFailed int                    `json:"sourcesFailed"`
	Performance   ScanPerformanceMetrics `json:"performance"`
}

// NewVectorDatabaseService creates a new vector database service
func NewVectorDatabaseService(dataDir string) *VectorDatabaseService {
	return &VectorDatabaseService{
		dataDir:     dataDir,
		initialized: false,
		collections: make(map[string]*chromem.Collection),
	}
}

// Initialize initializes the vector database service and creates collections
func (v *VectorDatabaseService) Initialize(ctx context.Context) error {
	v.mutex.Lock()
	defer v.mutex.Unlock()

	if v.initialized {
		return nil
	}

	// Create data directory if it doesn't exist
	vectorDBDir := filepath.Join(v.dataDir, "vector_db")
	if err := os.MkdirAll(vectorDBDir, 0755); err != nil {
		return fmt.Errorf("failed to create vector database directory: %w", err)
	}

	// Initialize chromem database with persistence
	db, err := chromem.NewPersistentDB(vectorDBDir, false)
	if err != nil {
		return fmt.Errorf("failed to initialize chromem database: %w", err)
	}

	v.db = db

	// Create collections
	if err := v.createCollections(ctx); err != nil {
		return fmt.Errorf("failed to create collections: %w", err)
	}

	v.initialized = true
	log.Printf("Vector database service initialized with chromem-go at %s", vectorDBDir)
	return nil
}

// createCollections creates the necessary collections
func (v *VectorDatabaseService) createCollections(ctx context.Context) error {
	collectionNames := []string{
		ScanHistoryCollection,
		ScanLogsCollection,
		GroupMetadataCollection,
		PerformanceCollection,
	}

	for _, name := range collectionNames {
		// Check if collection already exists
		if collection := v.db.GetCollection(name, nil); collection != nil {
			v.collections[name] = collection
			log.Printf("Loaded existing collection: %s", name)
			continue
		}

		// Create new collection
		collection, err := v.db.CreateCollection(name, nil, nil)
		if err != nil {
			return fmt.Errorf("failed to create collection %s: %w", name, err)
		}

		v.collections[name] = collection
		log.Printf("Created new collection: %s", name)
	}

	return nil
}

// StoreScanHistory stores scan history in the vector database
func (v *VectorDatabaseService) StoreScanHistory(ctx context.Context, entry ScanHistoryEntry) error {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return fmt.Errorf("scan history collection not found")
	}

	// Create content for embedding
	content := fmt.Sprintf("Group: %s Repository: %s Type: %s Status: %s Duration: %dms Results: %d Sources: %d/%d",
		entry.GroupName, entry.RepoID, entry.ScanType, entry.Status, entry.Duration,
		entry.ResultsCount, entry.SourcesTotal-entry.SourcesFailed, entry.SourcesTotal)

	if entry.ErrorMessage != "" {
		content += fmt.Sprintf(" Error: %s", entry.ErrorMessage)
	}

	// Create metadata (chromem-go uses map[string]string)
	metadata := map[string]string{
		"groupName":     entry.GroupName,
		"repoId":        entry.RepoID,
		"scanType":      entry.ScanType,
		"status":        entry.Status,
		"startTime":     entry.StartTime.Format(time.RFC3339),
		"endTime":       entry.EndTime.Format(time.RFC3339),
		"duration":      fmt.Sprintf("%d", entry.Duration),
		"resultsCount":  fmt.Sprintf("%d", entry.ResultsCount),
		"sourcesTotal":  fmt.Sprintf("%d", entry.SourcesTotal),
		"sourcesFailed": fmt.Sprintf("%d", entry.SourcesFailed),
		"errorMessage":  entry.ErrorMessage,
	}

	// Add performance metrics to metadata
	perfData, _ := json.Marshal(entry.Performance)
	metadata["performance"] = string(perfData)

	// Create document
	document := chromem.Document{
		ID:       entry.ID,
		Content:  content,
		Metadata: metadata,
	}

	// Add document to collection
	err := collection.AddDocuments(ctx, []chromem.Document{document}, 0)
	if err != nil {
		return fmt.Errorf("failed to add scan history document: %w", err)
	}

	log.Printf("Stored scan history for group %s in repo %s", entry.GroupName, entry.RepoID)
	return nil
}

// GetNeverScannedGroups identifies groups that have never been scanned
func (v *VectorDatabaseService) GetNeverScannedGroups(ctx context.Context, allGroups []models.Group, repoID string) ([]models.Group, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return allGroups, nil // If no collection, all groups are never scanned
	}

	// Query for all documents in this repository
	query := fmt.Sprintf("repository %s", repoID)
	results, err := collection.Query(ctx, query, 1000, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query scan history: %w", err)
	}

	// Build set of scanned groups
	scannedGroups := make(map[string]bool)
	for _, result := range results {
		if groupName, exists := result.Metadata["groupName"]; exists {
			if docRepoID, exists := result.Metadata["repoId"]; exists && docRepoID == repoID {
				scannedGroups[groupName] = true
			}
		}
	}

	// Filter out scanned groups
	var neverScanned []models.Group
	for _, group := range allGroups {
		if !scannedGroups[group.Groupname] {
			neverScanned = append(neverScanned, group)
		}
	}

	return neverScanned, nil
}

// GetGroupsNotScannedSince identifies groups not scanned within a time threshold
func (v *VectorDatabaseService) GetGroupsNotScannedSince(ctx context.Context, allGroups []models.Group, repoID string, threshold time.Duration) ([]models.Group, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return allGroups, nil // If no collection, all groups need scanning
	}

	cutoffTime := time.Now().Add(-threshold)

	// Query for all successful scans in this repository
	query := fmt.Sprintf("repository %s status completed", repoID)
	results, err := collection.Query(ctx, query, 1000, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query scan history: %w", err)
	}

	// Find most recent successful scan for each group
	recentlyScanned := make(map[string]time.Time)
	for _, result := range results {
		if groupName, exists := result.Metadata["groupName"]; exists {
			if docRepoID, exists := result.Metadata["repoId"]; exists && docRepoID == repoID {
				if status, exists := result.Metadata["status"]; exists && status == "completed" {
					if startTimeStr, exists := result.Metadata["startTime"]; exists {
						if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
							if existing, exists := recentlyScanned[groupName]; !exists || startTime.After(existing) {
								recentlyScanned[groupName] = startTime
							}
						}
					}
				}
			}
		}
	}

	// Filter groups that need scanning
	var needsScanning []models.Group
	for _, group := range allGroups {
		if lastScan, exists := recentlyScanned[group.Groupname]; !exists || lastScan.Before(cutoffTime) {
			needsScanning = append(needsScanning, group)
		}
	}

	return needsScanning, nil
}

// GetLastScanTime retrieves the last scan time for a specific group
func (v *VectorDatabaseService) GetLastScanTime(ctx context.Context, groupName, repoID string) (time.Time, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return time.Time{}, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return time.Time{}, fmt.Errorf("scan history collection not found")
	}

	// Query for specific group and repository, get most recent entry
	query := fmt.Sprintf("Group: %s Repository: %s", groupName, repoID)
	results, err := collection.Query(ctx, query, 1, nil, nil) // Limit to 1 for most recent
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to query scan history: %w", err)
	}

	if len(results) == 0 {
		return time.Time{}, fmt.Errorf("no scan history found for group %s in repository %s", groupName, repoID)
	}

	// Parse the most recent result
	entries, err := v.parseResults(results, 1)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse scan history: %w", err)
	}

	if len(entries) == 0 {
		return time.Time{}, fmt.Errorf("no valid scan history entries found")
	}

	// Return the start time of the most recent scan
	return entries[0].StartTime, nil
}

// GetGroupScanHistory retrieves scan history for a specific group
func (v *VectorDatabaseService) GetGroupScanHistory(ctx context.Context, groupName, repoID string, limit int) ([]ScanHistoryEntry, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return []ScanHistoryEntry{}, nil
	}

	// Query for specific group and repository
	query := fmt.Sprintf("Group: %s Repository: %s", groupName, repoID)
	queryLimit := limit
	if queryLimit <= 0 {
		queryLimit = 100 // Default limit
	}

	results, err := collection.Query(ctx, query, queryLimit, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query group scan history: %w", err)
	}

	return v.parseResults(results, limit)
}

// GetScanHistory retrieves scan history with optional filters
func (v *VectorDatabaseService) GetScanHistory(ctx context.Context, filters map[string]interface{}, limit int) ([]ScanHistoryEntry, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return []ScanHistoryEntry{}, nil
	}

	// Build query from filters
	var queryParts []string
	if groupName, ok := filters["groupName"].(string); ok {
		queryParts = append(queryParts, fmt.Sprintf("Group: %s", groupName))
	}
	if repoID, ok := filters["repoId"].(string); ok {
		queryParts = append(queryParts, fmt.Sprintf("Repository: %s", repoID))
	}
	if scanType, ok := filters["scanType"].(string); ok {
		queryParts = append(queryParts, fmt.Sprintf("Type: %s", scanType))
	}
	if status, ok := filters["status"].(string); ok {
		queryParts = append(queryParts, fmt.Sprintf("Status: %s", status))
	}

	query := "scan"
	if len(queryParts) > 0 {
		query = fmt.Sprintf("%s", queryParts[0])
		for i := 1; i < len(queryParts); i++ {
			query += " " + queryParts[i]
		}
	}

	queryLimit := limit
	if queryLimit <= 0 {
		queryLimit = 100 // Default limit
	}

	results, err := collection.Query(ctx, query, queryLimit, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query scan history: %w", err)
	}

	return v.parseResults(results, limit)
}

// parseResults converts chromem query results to ScanHistoryEntry structs
func (v *VectorDatabaseService) parseResults(results []chromem.Result, limit int) ([]ScanHistoryEntry, error) {
	entries := make([]ScanHistoryEntry, 0, len(results))
	count := 0

	for _, result := range results {
		if limit > 0 && count >= limit {
			break
		}

		entry := ScanHistoryEntry{
			ID: result.ID,
		}

		// Extract fields from metadata (chromem-go uses map[string]string)
		if groupName, exists := result.Metadata["groupName"]; exists {
			entry.GroupName = groupName
		}
		if repoID, exists := result.Metadata["repoId"]; exists {
			entry.RepoID = repoID
		}
		if scanType, exists := result.Metadata["scanType"]; exists {
			entry.ScanType = scanType
		}
		if status, exists := result.Metadata["status"]; exists {
			entry.Status = status
		}
		if startTimeStr, exists := result.Metadata["startTime"]; exists {
			if startTimeInt, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
				entry.StartTime = startTimeInt
			}
		}
		if endTimeStr, exists := result.Metadata["endTime"]; exists {
			if endTimeInt, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
				entry.EndTime = endTimeInt
			}
		}
		if durationStr, exists := result.Metadata["duration"]; exists {
			if durationInt, err := time.ParseDuration(durationStr + "ms"); err == nil {
				entry.Duration = int64(durationInt.Milliseconds())
			}
		}
		if resultsCountStr, exists := result.Metadata["resultsCount"]; exists {
			fmt.Sscanf(resultsCountStr, "%d", &entry.ResultsCount)
		}
		if sourcesTotalStr, exists := result.Metadata["sourcesTotal"]; exists {
			fmt.Sscanf(sourcesTotalStr, "%d", &entry.SourcesTotal)
		}
		if sourcesFailedStr, exists := result.Metadata["sourcesFailed"]; exists {
			fmt.Sscanf(sourcesFailedStr, "%d", &entry.SourcesFailed)
		}
		if errorMessage, exists := result.Metadata["errorMessage"]; exists {
			entry.ErrorMessage = errorMessage
		}

		// Parse performance metrics
		if perfData, exists := result.Metadata["performance"]; exists {
			json.Unmarshal([]byte(perfData), &entry.Performance)
		}

		entries = append(entries, entry)
		count++
	}

	return entries, nil
}

// StoreScanLog stores a scan log entry in the vector database
func (v *VectorDatabaseService) StoreScanLog(ctx context.Context, scanID, groupName, repoID, level, message, step, sourceID string) error {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanLogsCollection]
	if collection == nil {
		return fmt.Errorf("scan logs collection not found")
	}

	// Create content for embedding
	content := fmt.Sprintf("Scan: %s Group: %s Level: %s Step: %s Message: %s",
		scanID, groupName, level, step, message)

	// Create metadata (chromem-go uses map[string]string)
	metadata := map[string]string{
		"scanId":    scanID,
		"groupName": groupName,
		"repoId":    repoID,
		"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		"level":     level,
		"message":   message,
		"step":      step,
		"sourceId":  sourceID,
	}

	// Create document
	document := chromem.Document{
		ID:       uuid.New().String(),
		Content:  content,
		Metadata: metadata,
	}

	// Add document to collection
	err := collection.AddDocuments(ctx, []chromem.Document{document}, 0)
	if err != nil {
		return fmt.Errorf("failed to add scan log document: %w", err)
	}

	return nil
}

// SearchSimilarScans finds scans similar to the given content using vector similarity
func (v *VectorDatabaseService) SearchSimilarScans(ctx context.Context, content string, limit int) ([]ScanHistoryEntry, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanHistoryCollection]
	if collection == nil {
		return []ScanHistoryEntry{}, nil
	}

	// Query for similar content
	results, err := collection.Query(ctx, content, limit, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to search similar scans: %w", err)
	}

	return v.parseResults(results, limit)
}

// GetScanLogs retrieves scan logs for a specific scan
func (v *VectorDatabaseService) GetScanLogs(ctx context.Context, scanID string, limit int) ([]map[string]interface{}, error) {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil, fmt.Errorf("vector database service not initialized")
	}

	collection := v.collections[ScanLogsCollection]
	if collection == nil {
		return []map[string]interface{}{}, nil
	}

	// Query for logs of specific scan
	query := fmt.Sprintf("Scan: %s", scanID)
	results, err := collection.Query(ctx, query, limit, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query scan logs: %w", err)
	}

	logs := make([]map[string]interface{}, 0, len(results))
	for _, result := range results {
		logEntry := make(map[string]interface{})
		logEntry["id"] = result.ID
		logEntry["content"] = result.Content

		// Copy metadata
		for key, value := range result.Metadata {
			logEntry[key] = value
		}

		logs = append(logs, logEntry)
	}

	return logs, nil
}

// Close closes the vector database
func (v *VectorDatabaseService) Close() error {
	v.mutex.Lock()
	defer v.mutex.Unlock()

	if !v.initialized {
		return nil
	}

	if v.db != nil {
		// chromem-go handles persistence automatically
		log.Println("Vector database service closed")
	}

	v.initialized = false
	return nil
}

// StoreConfigChange stores configuration change information
func (v *VectorDatabaseService) StoreConfigChange(ctx context.Context, configType, configID string, version int, changedBy, description string) error {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil
	}

	changeData := map[string]interface{}{
		"configType":  configType,
		"configId":    configID,
		"version":     version,
		"changedBy":   changedBy,
		"description": description,
		"timestamp":   time.Now().Format(time.RFC3339),
		"type":        "config_change",
	}

	// Convert to JSON for storage
	jsonData, err := json.Marshal(changeData)
	if err != nil {
		return fmt.Errorf("failed to marshal config change data: %w", err)
	}

	// Store in the vector database (placeholder implementation)
	log.Printf("Storing config change in vector database: %s/%s v%d by %s", configType, configID, version, changedBy)

	// In a real implementation, this would store the data in a vector database
	// For now, we'll just log it
	_ = jsonData

	return nil
}

// StorePerformanceMetrics stores performance metrics in the vector database
func (v *VectorDatabaseService) StorePerformanceMetrics(ctx context.Context, operation string, metrics ScanPerformanceMetrics) error {
	v.mutex.RLock()
	defer v.mutex.RUnlock()

	if !v.initialized {
		return nil
	}

	metricsData := map[string]interface{}{
		"operation":       operation,
		"authCacheHits":   metrics.AuthCacheHits,
		"authCacheMisses": metrics.AuthCacheMisses,
		"timestamp":       time.Now().Format(time.RFC3339),
		"type":            "performance_metrics",
	}

	// Convert to JSON for storage
	jsonData, err := json.Marshal(metricsData)
	if err != nil {
		return fmt.Errorf("failed to marshal performance metrics: %w", err)
	}

	// Store in the vector database (placeholder implementation)
	log.Printf("Storing performance metrics in vector database: %s", operation)

	// In a real implementation, this would store the data in a vector database
	// For now, we'll just log it
	_ = jsonData

	return nil
}
