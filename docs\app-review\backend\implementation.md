# Backend Implementation Details

## Service Layer Implementation

### Repository Manager (`services/repository_manager.go`)

#### Core Structure
```go
type RepositoryManager struct {
    repositories map[string]*models.Repository
    mutex        sync.RWMutex
    configFile   string
    reposDir     string
    syncLogger   *SyncLogger
}
```

#### Key Implementation Patterns

**Repository Configuration Management**:
```go
func (rm *RepositoryManager) LoadRepositories() error {
    rm.mutex.Lock()
    defer rm.mutex.Unlock()
    
    // Load from JSON configuration file
    data, err := os.ReadFile(rm.configFile)
    if err != nil {
        if os.IsNotExist(err) {
            rm.repositories = make(map[string]*models.Repository)
            return nil
        }
        return err
    }
    
    var repos []*models.Repository
    if err := json.Unmarshal(data, &repos); err != nil {
        return err
    }
    
    // Convert to map for efficient access
    rm.repositories = make(map[string]*models.Repository)
    for _, repo := range repos {
        rm.repositories[repo.ID] = repo
    }
    
    return nil
}
```

**Git Operations with Error Handling**:
```go
func (rm *RepositoryManager) SyncRepository(repoID string) error {
    repo, exists := rm.repositories[repoID]
    if !exists {
        return fmt.Errorf("repository not found: %s", repoID)
    }
    
    repoPath := repo.GetLocalRepoPath()
    
    // Check if repository exists locally
    if _, err := os.Stat(repoPath); os.IsNotExist(err) {
        // Clone repository
        return rm.cloneRepository(repo)
    } else {
        // Pull latest changes
        return rm.pullRepository(repo)
    }
}
```

### Data Processor (`services/data_processor.go`)

#### Caching Implementation
```go
type DataProcessor struct {
    reportsDir string
    cache      *cache.Cache
    mutex      sync.RWMutex
}

// Multi-level caching with TTL
func NewDataProcessor(reportsDir string) *DataProcessor {
    return &DataProcessor{
        reportsDir: reportsDir,
        cache:      cache.New(5*time.Minute, 10*time.Minute),
    }
}

func (dp *DataProcessor) GetGroupsWithCache(repoPath string) ([]*models.Group, error) {
    cacheKey := fmt.Sprintf(CacheKeyGroups, repoPath)
    
    // Try cache first
    if cached, found := dp.cache.Get(cacheKey); found {
        return cached.([]*models.Group), nil
    }
    
    // Load from disk
    groups, err := dp.ParseJSONFiles(repoPath)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    dp.cache.Set(cacheKey, groups, cache.DefaultExpiration)
    return groups, nil
}
```

#### Group Membership Resolution
```go
func (dp *DataProcessor) ResolveMembers(groups []*models.Group) {
    groupMap := make(map[string]*models.Group)
    for _, group := range groups {
        groupMap[group.Name] = group
    }
    
    // Resolve member groups recursively
    for _, group := range groups {
        visited := make(map[string]bool)
        group.ResolvedMembers = dp.resolveMembersRecursive(group, groupMap, visited)
    }
}

func (dp *DataProcessor) resolveMembersRecursive(
    group *models.Group, 
    groupMap map[string]*models.Group, 
    visited map[string]bool,
) []string {
    if visited[group.Name] {
        // Circular dependency detected
        return group.Members
    }
    
    visited[group.Name] = true
    resolvedMembers := make([]string, 0)
    
    // Add direct members
    resolvedMembers = append(resolvedMembers, group.Members...)
    
    // Resolve member groups
    for _, memberGroupName := range group.MemberGroups {
        if memberGroup, exists := groupMap[memberGroupName]; exists {
            nestedMembers := dp.resolveMembersRecursive(memberGroup, groupMap, visited)
            resolvedMembers = append(resolvedMembers, nestedMembers...)
        }
    }
    
    delete(visited, group.Name)
    return removeDuplicates(resolvedMembers)
}
```

### WebSocket Hub (`services/websocket_hub.go`)

#### Connection Management
```go
type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
    mutex      sync.RWMutex
}

type Client struct {
    hub    *Hub
    conn   *websocket.Conn
    send   chan []byte
    repoID string
}

func (h *Hub) Run() {
    for {
        select {
        case client := <-h.register:
            h.mutex.Lock()
            h.clients[client] = true
            h.mutex.Unlock()
            
        case client := <-h.unregister:
            h.mutex.Lock()
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.send)
            }
            h.mutex.Unlock()
            
        case message := <-h.broadcast:
            h.mutex.RLock()
            for client := range h.clients {
                select {
                case client.send <- message:
                default:
                    close(client.send)
                    delete(h.clients, client)
                }
            }
            h.mutex.RUnlock()
        }
    }
}
```

#### Message Broadcasting with Filtering
```go
func (h *Hub) BroadcastToRepo(repoID string, message []byte) {
    h.mutex.RLock()
    defer h.mutex.RUnlock()
    
    for client := range h.clients {
        if client.repoID == repoID {
            select {
            case client.send <- message:
            default:
                close(client.send)
                delete(h.clients, client)
            }
        }
    }
}
```

### Search Implementation (`controllers/bleve_search_controller.go`)

#### Index Management
```go
type BleveSearchController struct {
    indexPath string
    index     bleve.Index
    mutex     sync.RWMutex
}

func (c *BleveSearchController) InitializeIndex() error {
    c.mutex.Lock()
    defer c.mutex.Unlock()
    
    // Try to open existing index
    index, err := bleve.Open(c.indexPath)
    if err != nil {
        // Create new index if it doesn't exist
        mapping := bleve.NewIndexMapping()
        index, err = bleve.New(c.indexPath, mapping)
        if err != nil {
            return fmt.Errorf("failed to create search index: %v", err)
        }
    }
    
    c.index = index
    return nil
}
```

#### Query Processing
```go
func (c *BleveSearchController) SearchGroups(query, repoId string) ([]*models.Group, error) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    if c.index == nil {
        return nil, fmt.Errorf("search index not initialized")
    }
    
    // Build search query
    searchQuery := bleve.NewQueryStringQuery(query)
    searchRequest := bleve.NewSearchRequest(searchQuery)
    searchRequest.Size = 1000 // Configurable limit
    
    // Execute search
    searchResult, err := c.index.Search(searchRequest)
    if err != nil {
        return nil, fmt.Errorf("search failed: %v", err)
    }
    
    // Convert results to groups
    groups := make([]*models.Group, 0, len(searchResult.Hits))
    for _, hit := range searchResult.Hits {
        if group, err := c.getGroupFromHit(hit); err == nil {
            groups = append(groups, group)
        }
    }
    
    return groups, nil
}
```

## Controller Layer Implementation

### API Server Setup (`api/server.go`)

#### Server Initialization
```go
type Server struct {
    router      *gin.Engine
    port        string
    controllers []Controller
    wsHub       *services.WebSocketHub
    staticDir   string
}

func NewServer(port string) *Server {
    gin.SetMode(gin.ReleaseMode)
    router := gin.New()
    
    // Add middleware
    router.Use(gin.Logger())
    router.Use(gin.Recovery())
    router.Use(corsMiddleware())
    
    return &Server{
        router:      router,
        port:        port,
        controllers: make([]Controller, 0),
    }
}
```

#### Route Registration
```go
func (s *Server) AddController(controller Controller) {
    s.controllers = append(s.controllers, controller)
    controller.RegisterRoutes(s.router)
}

func (s *Server) Start() error {
    // Setup WebSocket routes
    if s.wsHub != nil {
        s.setupWebSocketRoutes()
    }
    
    // Setup static file serving
    if s.staticDir != "" {
        s.setupStaticRoutes()
    }
    
    // Start server
    return s.router.Run(":" + s.port)
}
```

### Error Handling Implementation

#### Structured Error Responses
```go
type ApiError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details any    `json:"details,omitempty"`
}

func (c *DataController) handleError(ctx *gin.Context, err error, defaultStatus int) {
    var apiError *ApiError
    var status int
    
    switch e := err.(type) {
    case *models.ValidationError:
        apiError = &ApiError{
            Code:    "VALIDATION_ERROR",
            Message: e.Message,
            Details: map[string]string{"field": e.Field},
        }
        status = http.StatusBadRequest
        
    case *os.PathError:
        apiError = &ApiError{
            Code:    "FILE_ERROR",
            Message: "File operation failed",
        }
        status = http.StatusInternalServerError
        
    default:
        apiError = &ApiError{
            Code:    "INTERNAL_ERROR",
            Message: err.Error(),
        }
        status = defaultStatus
    }
    
    ctx.JSON(status, gin.H{"error": apiError})
}
```

### Request Validation Implementation

#### Input Validation Patterns
```go
func (c *RepositoryController) CreateRepository(ctx *gin.Context) {
    var repo models.Repository
    
    // Bind JSON request
    if err := ctx.ShouldBindJSON(&repo); err != nil {
        c.handleError(ctx, err, http.StatusBadRequest)
        return
    }
    
    // Validate repository data
    if err := repo.Validate(); err != nil {
        c.handleError(ctx, err, http.StatusBadRequest)
        return
    }
    
    // Process request
    if err := c.repoManager.SaveRepository(&repo); err != nil {
        c.handleError(ctx, err, http.StatusInternalServerError)
        return
    }
    
    ctx.JSON(http.StatusCreated, repo)
}
```

## Model Layer Implementation

### Data Validation (`models/usage.go`)

#### Comprehensive Validation
```go
func (us *UsageSource) Validate() error {
    if us.Name == "" {
        return NewValidationError("name", "name is required")
    }
    if us.Type == "" {
        return NewValidationError("type", "type is required")
    }
    if us.ScanFrequency < 300 {
        return NewValidationError("scanFrequency", "scan frequency must be at least 300 seconds")
    }
    
    // Type-specific validation
    switch us.Type {
    case SourceTypeGit:
        if us.GitConfig == nil {
            return NewValidationError("gitConfig", "git configuration is required")
        }
        return us.GitConfig.Validate()
        
    case SourceTypeAPI:
        if us.ApiConfig == nil {
            return NewValidationError("apiConfig", "api configuration is required")
        }
        return us.ApiConfig.Validate()
        
    case SourceTypeFile:
        if us.FileConfig == nil {
            return NewValidationError("fileConfig", "file configuration is required")
        }
        return us.FileConfig.Validate()
        
    default:
        return NewValidationError("type", "invalid source type")
    }
}
```

### Data Serialization
```go
// Custom JSON marshaling for complex types
func (r *Repository) MarshalJSON() ([]byte, error) {
    type Alias Repository
    return json.Marshal(&struct {
        *Alias
        LastSyncFormatted string `json:"lastSyncFormatted,omitempty"`
    }{
        Alias:             (*Alias)(r),
        LastSyncFormatted: r.GetFormattedLastSync(),
    })
}
```

## Background Processing Implementation

### Scheduler Service (`services/scheduler_service.go`)

#### Task Management
```go
type SchedulerService struct {
    tasks       map[string]*ScheduledTask
    activeTasks map[string]*ActiveTask
    mutex       sync.RWMutex
    stopChan    chan struct{}
}

type ScheduledTask struct {
    ID           string
    Name         string
    CronExpr     string
    Handler      TaskHandler
    NextRun      time.Time
    LastRun      time.Time
    Enabled      bool
}

func (s *SchedulerService) ScheduleTask(task *ScheduledTask) error {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    // Parse cron expression
    schedule, err := cron.ParseStandard(task.CronExpr)
    if err != nil {
        return fmt.Errorf("invalid cron expression: %v", err)
    }
    
    // Calculate next run time
    task.NextRun = schedule.Next(time.Now())
    s.tasks[task.ID] = task
    
    return nil
}
```

#### Task Execution
```go
func (s *SchedulerService) Run() {
    ticker := time.NewTicker(1 * time.Minute)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            s.checkAndExecuteTasks()
        case <-s.stopChan:
            return
        }
    }
}

func (s *SchedulerService) checkAndExecuteTasks() {
    s.mutex.RLock()
    tasksToRun := make([]*ScheduledTask, 0)
    now := time.Now()
    
    for _, task := range s.tasks {
        if task.Enabled && task.NextRun.Before(now) {
            tasksToRun = append(tasksToRun, task)
        }
    }
    s.mutex.RUnlock()
    
    // Execute tasks
    for _, task := range tasksToRun {
        go s.executeTask(task)
    }
}
```

### Usage Scanning Implementation (`services/usage_source_manager.go`)

#### Source Handler Pattern
```go
type SourceHandler interface {
    ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error)
    ValidateConfiguration() error
}

type GitSourceHandler struct {
    source *models.UsageSource
}

func (g *GitSourceHandler) ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error) {
    results := make([]models.UsageResult, 0)
    
    // Clone or update repository
    repoPath, err := g.ensureRepository(ctx)
    if err != nil {
        return nil, err
    }
    
    // Scan files for group usage
    err = filepath.Walk(repoPath, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return err
        }
        
        // Check for cancellation
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
        }
        
        // Skip directories and binary files
        if info.IsDir() || isBinaryFile(path) {
            return nil
        }
        
        // Scan file content
        fileResults, err := g.scanFile(path, groupName)
        if err != nil {
            log.Printf("Error scanning file %s: %v", path, err)
            return nil // Continue with other files
        }
        
        results = append(results, fileResults...)
        return nil
    })
    
    return results, err
}
```

## Performance Optimization Implementation

### Concurrent Processing
```go
func (dp *DataProcessor) ProcessRepositoriesConcurrently(repoPaths []string) error {
    const maxWorkers = 5
    semaphore := make(chan struct{}, maxWorkers)
    var wg sync.WaitGroup
    var mu sync.Mutex
    var errors []error
    
    for _, repoPath := range repoPaths {
        wg.Add(1)
        go func(path string) {
            defer wg.Done()
            
            // Acquire semaphore
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            if err := dp.processRepository(path); err != nil {
                mu.Lock()
                errors = append(errors, err)
                mu.Unlock()
            }
        }(repoPath)
    }
    
    wg.Wait()
    
    if len(errors) > 0 {
        return fmt.Errorf("processing errors: %v", errors)
    }
    
    return nil
}
```

### Memory Management
```go
func (dp *DataProcessor) ProcessLargeDataset(filePath string) error {
    file, err := os.Open(filePath)
    if err != nil {
        return err
    }
    defer file.Close()
    
    // Use streaming JSON decoder for large files
    decoder := json.NewDecoder(file)
    
    // Process in chunks to manage memory
    for decoder.More() {
        var chunk json.RawMessage
        if err := decoder.Decode(&chunk); err != nil {
            return err
        }
        
        if err := dp.processChunk(chunk); err != nil {
            return err
        }
        
        // Force garbage collection periodically
        runtime.GC()
    }
    
    return nil
}
```

## Testing Implementation

### Unit Test Patterns
```go
func TestRepositoryManager_SyncRepository(t *testing.T) {
    // Setup
    tempDir := t.TempDir()
    configFile := filepath.Join(tempDir, "config.json")
    reposDir := filepath.Join(tempDir, "repos")
    
    rm := services.NewRepositoryManager(configFile, reposDir, nil)
    
    // Test data
    repo := &models.Repository{
        ID:     "test-repo",
        Name:   "Test Repository",
        URL:    "https://github.com/test/repo.git",
        Branch: "main",
    }
    
    // Add repository
    err := rm.SaveRepository(repo)
    require.NoError(t, err)
    
    // Test sync
    err = rm.SyncRepository("test-repo")
    assert.NoError(t, err)
    
    // Verify repository was cloned
    repoPath := filepath.Join(reposDir, "test-repo")
    assert.DirExists(t, repoPath)
}
```

### Integration Test Patterns
```go
func TestIntegration_FullWorkflow(t *testing.T) {
    suite := setupIntegrationTest(t)
    defer suite.teardownIntegrationTest()
    
    // Test repository creation
    repo := models.Repository{
        Name:   "Integration Test Repo",
        URL:    "https://github.com/test/repo.git",
        Branch: "main",
    }
    
    resp, body, err := suite.makeRequest("POST", "/api/repo/configurations", repo)
    require.NoError(t, err)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
    
    var createdRepo models.Repository
    err = json.Unmarshal(body, &createdRepo)
    require.NoError(t, err)
    
    // Test repository sync
    resp, _, err = suite.makeRequest("POST", fmt.Sprintf("/api/repo/%s/sync", createdRepo.ID), nil)
    require.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
    
    // Test search functionality
    resp, body, err = suite.makeRequest("GET", fmt.Sprintf("/api/repo/%s/search/groups?q=test", createdRepo.ID), nil)
    require.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
}
```
