# ADGitOps UI Helm Chart

This Helm chart deploys the ADGitOps UI application in a Kubernetes cluster.

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- PV provisioner support in the underlying infrastructure (if persistence is enabled)

## Installing the Chart

To install the chart with the release name `adgitops-ui`:

```bash
helm install adgitops-ui ./chart
```

The command deploys ADGitOps UI on the Kubernetes cluster with default configuration. The [Parameters](#parameters) section lists the parameters that can be configured during installation.

## Uninstalling the Chart

To uninstall/delete the `adgitops-ui` deployment:

```bash
helm uninstall adgitops-ui
```

## Horizontal Scaling Limitations

> **IMPORTANT**: This application is designed to run with exactly 1 replica and cannot be horizontally scaled.

The application is currently not designed to support multiple replicas due to the following limitations:

1. **File-based Storage**: The application uses file-based storage for configurations, data, and reports.
   - While these are stored on persistent volumes, the application lacks proper file locking mechanisms for concurrent access.

2. **In-memory State**: Several components maintain in-memory state that is not shared between instances:
   - Caching in the `DataProcessor`
   - Repository synchronization state
   - Search indices

3. **Local Concurrency Control**: The application uses local mutexes for critical sections, which only work within a single instance.

4. **ReadWriteOnce PVCs**: The persistent volumes are configured with `ReadWriteOnce` access mode, which means they can only be mounted by pods on a single node.

To make the application support multiple replicas would require significant architectural changes:
- Moving from file-based storage to a database
- Implementing distributed caching and locking
- Using a shared search index
- Changing PVC access modes to `ReadWriteMany`

For these reasons, the deployment is configured to always use exactly 1 replica, regardless of the value specified in `values.yaml`.

## Parameters

### Application Configuration

| Name                      | Description                                     | Value           |
| ------------------------- | ----------------------------------------------- | --------------- |
| `app.port`                | Application port                                | `8080`          |
| `app.replicaCount`        | Number of replicas (ignored, always set to 1)   | `1`             |

### Image Configuration

| Name                      | Description                                     | Value           |
| ------------------------- | ----------------------------------------------- | --------------- |
| `image.repository`        | Image repository                                | `adgitops-ui` |
| `image.tag`               | Image tag                                       | `latest`        |
| `image.pullPolicy`        | Image pull policy                               | `IfNotPresent`  |

### Service Configuration

| Name                      | Description                                     | Value           |
| ------------------------- | ----------------------------------------------- | --------------- |
| `service.type`            | Kubernetes service type                         | `ClusterIP`     |
| `service.port`            | Kubernetes service port                         | `8080`          |

### Persistence Configuration

| Name                                  | Description                                     | Value           |
| ------------------------------------- | ----------------------------------------------- | --------------- |
| `persistence.enabled`                 | Enable persistence                              | `true`          |
| `persistence.configs.storageClass`    | Storage class for configs                       | `""`            |
| `persistence.configs.accessMode`      | Access mode for configs                         | `ReadWriteOnce` |
| `persistence.configs.size`            | Size for configs                                | `1Gi`           |
| `persistence.data.storageClass`       | Storage class for data                          | `""`            |
| `persistence.data.accessMode`         | Access mode for data                            | `ReadWriteOnce` |
| `persistence.data.size`               | Size for data                                   | `5Gi`           |
| `persistence.reports.storageClass`    | Storage class for reports                       | `""`            |
| `persistence.reports.accessMode`      | Access mode for reports                         | `ReadWriteOnce` |
| `persistence.reports.size`            | Size for reports                                | `5Gi`           |
| `persistence.repos.storageClass`      | Storage class for repositories                  | `""`            |
| `persistence.repos.accessMode`        | Access mode for repositories                    | `ReadWriteOnce` |
| `persistence.repos.size`              | Size for repositories                           | `10Gi`          |

## Quick Start

1. Build the application and Docker image:
   ```bash
   # Build the application
   ./run.sh build  # or run.bat build on Windows

   # Build the Docker image
   docker build -t adgitops-ui:latest .

   # Push to your registry
   docker tag adgitops-ui:latest your-registry/adgitops-ui:latest
   docker push your-registry/adgitops-ui:latest
   ```

2. Deploy to Kubernetes:
   ```bash
   helm install adgitops-ui ./chart \
     --set image.repository=your-registry/adgitops-ui \
     --set image.tag=latest
   ```

## Custom Configuration

Create a custom values file for more advanced configuration:

```yaml
# custom-values.yaml
app:
  port: 8080
  # Note: replicaCount is ignored, application always runs with 1 replica

image:
  repository: your-registry/adgitops-ui
  tag: v1.0.0

persistence:
  enabled: true
  data:
    size: 10Gi
```

Then deploy with:

```bash
helm install adgitops-ui ./chart -f custom-values.yaml
```

Note: Repository configuration is not included in the Helm chart and should be provided at runtime.
