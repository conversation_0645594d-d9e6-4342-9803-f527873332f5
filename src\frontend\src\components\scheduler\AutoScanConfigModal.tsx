import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Clock, Settings, Bell, Zap, AlertTriangle, CheckCircle, Target, Trash2, Plus } from 'lucide-react';
import { apiClient as api } from '@/api/client';
import type { PriorityRule, PriorityRuleOperator } from '@/types/autoScan';

interface AutoScanConfig {
  id?: string;
  repoId: string;
  repositoryName?: string;
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  timeWindow: {
    startHour: number;
    endHour: number;
    timezone: string;
  };
  maxConcurrentScans: number;
  scanAllGroups: boolean;
  targetGroups: string[];
  loadBalancing: {
    spreadAcrossDay: boolean;
    minIntervalBetween: string;
    maxScansPerHour: number;
  };
  notificationConfig: {
    onCompletion: boolean;
    onFailure: boolean;
    onSummary: boolean;
  };
  priorityConfig: {
    enabled: boolean;
    defaultWeight: number;
    rules: PriorityRule[];
    scheduleByWeight: boolean;
  };
  createdAt?: string;
  updatedAt?: string;
  lastScanTime?: string;
  nextScanTime?: string;
}

interface AutoScanConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  repoId?: string;
  repositoryName?: string;
  onConfigSaved: () => void;
}

const DEFAULT_CONFIG: Partial<AutoScanConfig> = {
  enabled: true,
  frequency: 'daily',
  timeWindow: {
    startHour: 2,
    endHour: 6,
    timezone: 'UTC',
  },
  maxConcurrentScans: 3,
  scanAllGroups: true,
  targetGroups: [],
  loadBalancing: {
    spreadAcrossDay: true,
    minIntervalBetween: '30m',
    maxScansPerHour: 10,
  },
  notificationConfig: {
    onCompletion: false,
    onFailure: true,
    onSummary: false,
  },
  priorityConfig: {
    enabled: false,
    defaultWeight: 50,
    scheduleByWeight: true,
    rules: [
      {
        id: 'high-priority-admin',
        name: 'High Priority Admin Groups',
        pattern: '*Admin*',
        weight: 100,
        description: 'Administrative groups get highest priority',
        enabled: true,
        operator: 'include' as const,
      },
      {
        id: 'medium-priority-super',
        name: 'Medium Priority Super User Groups',
        pattern: '*Super*',
        weight: 75,
        description: 'Super user groups get medium-high priority',
        enabled: true,
        operator: 'include' as const,
      },
      {
        id: 'low-priority-standard',
        name: 'Low Priority Standard Groups',
        pattern: '*Standard*',
        weight: 25,
        description: 'Standard user groups get lower priority',
        enabled: true,
        operator: 'include' as const,
      },
    ],
  },
};

export const AutoScanConfigModal: React.FC<AutoScanConfigModalProps> = ({
  isOpen,
  onClose,
  repoId,
  repositoryName,
  onConfigSaved,
}) => {
  const [config, setConfig] = useState<AutoScanConfig>({
    ...DEFAULT_CONFIG,
    repoId: repoId || '',
    repositoryName: repositoryName || '',
  } as AutoScanConfig);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (isOpen && repoId) {
      loadConfig();
    } else if (isOpen && !repoId) {
      // Reset to defaults when opening without a specific repo
      setConfig({
        ...DEFAULT_CONFIG,
        repoId: '',
        repositoryName: '',
      } as AutoScanConfig);
      setIsEditing(false);
    }
  }, [isOpen, repoId]);

  const loadConfig = async () => {
    if (!repoId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.scheduler.getAutoScanConfig(repoId);
      if (response.config) {
        // Ensure priorityConfig exists with defaults if missing
        const loadedConfig = {
          ...response.config,
          priorityConfig: response.config.priorityConfig || DEFAULT_CONFIG.priorityConfig
        };
        setConfig(loadedConfig);
        setIsEditing(true);
      } else {
        // No existing config, use defaults
        setConfig({
          ...DEFAULT_CONFIG,
          repoId,
          repositoryName,
        } as AutoScanConfig);
        setIsEditing(false);
      }
    } catch (err: any) {
      console.error('Error loading auto-scan config:', err);
      if (err.status === 404 || err.message?.includes('404') || err.message?.includes('not found')) {
        // No existing config, use defaults
        setConfig({
          ...DEFAULT_CONFIG,
          repoId,
          repositoryName,
        } as AutoScanConfig);
        setIsEditing(false);
      } else if (err.message?.includes('not available')) {
        setError('Auto-scan configuration endpoints not available. Please ensure the backend is running with the latest version.');
      } else {
        setError(`Failed to load configuration: ${err.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (isEditing) {
        await api.scheduler.updateAutoScanConfig(repoId!, config);
        setSuccess('Configuration updated successfully');
      } else {
        await api.scheduler.createAutoScanConfig(config);
        setSuccess('Configuration created successfully');
        setIsEditing(true);
      }
      onConfigSaved();
    } catch (err: any) {
      setError(err.message || 'Failed to save configuration');
      console.error('Error saving auto-scan config:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!isEditing || !repoId) return;

    setLoading(true);
    setError(null);

    try {
      await api.scheduler.deleteAutoScanConfig(repoId);
      setSuccess('Configuration deleted successfully');
      setIsEditing(false);
      setConfig({
        ...DEFAULT_CONFIG,
        repoId,
        repositoryName,
      } as AutoScanConfig);
      onConfigSaved();
    } catch (err: any) {
      setError(err.message || 'Failed to delete configuration');
      console.error('Error deleting auto-scan config:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickEnable = async () => {
    setLoading(true);
    setError(null);

    try {
      await api.scheduler.enableAutoScan(repoId!);
      setSuccess('Auto-scan enabled with default settings');
      await loadConfig(); // Reload to show the created config
      onConfigSaved();
    } catch (err: any) {
      setError(err.message || 'Failed to enable auto-scan');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickDisable = async () => {
    setLoading(true);
    setError(null);

    try {
      await api.scheduler.disableAutoScan(repoId!);
      setSuccess('Auto-scan disabled');
      await loadConfig(); // Reload to show updated config
      onConfigSaved();
    } catch (err: any) {
      setError(err.message || 'Failed to disable auto-scan');
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current: any = newConfig;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!(keys[i] in current)) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const formatTime = (hour: number) => {
    return `${hour.toString().padStart(2, '0')}:00`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-blue-600" />
            <span>Auto-Scan Configuration</span>
            {repositoryName && (
              <Badge variant="outline" className="ml-2">{repositoryName}</Badge>
            )}
          </DialogTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Configure automatic scanning schedules, time windows, and load balancing settings for this repository.
          </p>
        </DialogHeader>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2">
                <Button
                  onClick={handleQuickEnable}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                >
                  <Zap className="h-4 w-4 mr-1" />
                  Enable with Defaults
                </Button>
                <Button
                  onClick={handleQuickDisable}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                >
                  Disable Auto-Scan
                </Button>
                {isEditing && (
                  <Button
                    onClick={handleDelete}
                    disabled={loading}
                    variant="destructive"
                    size="sm"
                  >
                    Delete Configuration
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Configuration Tabs */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="schedule">Schedule</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
              <TabsTrigger value="priority">Priority</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Settings className="h-4 w-4" />
                    <span>Basic Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.enabled}
                      onCheckedChange={(checked) => updateConfig('enabled', checked)}
                    />
                    <Label>Enable Auto-Scan</Label>
                  </div>

                  <div className="space-y-2">
                    <Label>Scan Frequency</Label>
                    <Select
                      value={config.frequency}
                      onValueChange={(value) => updateConfig('frequency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Maximum Concurrent Scans</Label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.maxConcurrentScans}
                      onChange={(e) => updateConfig('maxConcurrentScans', parseInt(e.target.value) || 1)}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.scanAllGroups}
                      onCheckedChange={(checked) => updateConfig('scanAllGroups', checked)}
                    />
                    <Label>Scan All Groups</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="schedule" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Schedule Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Start Hour (24h format)</Label>
                      <Input
                        type="number"
                        min="0"
                        max="23"
                        value={config.timeWindow.startHour}
                        onChange={(e) => updateConfig('timeWindow.startHour', parseInt(e.target.value) || 0)}
                      />
                      <p className="text-xs text-gray-500">
                        {formatTime(config.timeWindow.startHour)}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label>End Hour (24h format)</Label>
                      <Input
                        type="number"
                        min="0"
                        max="23"
                        value={config.timeWindow.endHour}
                        onChange={(e) => updateConfig('timeWindow.endHour', parseInt(e.target.value) || 0)}
                      />
                      <p className="text-xs text-gray-500">
                        {formatTime(config.timeWindow.endHour)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Timezone</Label>
                    <Select
                      value={config.timeWindow.timezone}
                      onValueChange={(value) => updateConfig('timeWindow.timezone', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="UTC">UTC</SelectItem>
                        <SelectItem value="America/New_York">Eastern Time</SelectItem>
                        <SelectItem value="America/Chicago">Central Time</SelectItem>
                        <SelectItem value="America/Denver">Mountain Time</SelectItem>
                        <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                        <SelectItem value="Europe/London">London</SelectItem>
                        <SelectItem value="Europe/Paris">Paris</SelectItem>
                        <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Load Balancing</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.loadBalancing.spreadAcrossDay}
                      onCheckedChange={(checked) => updateConfig('loadBalancing.spreadAcrossDay', checked)}
                    />
                    <Label>Spread Scans Across Day</Label>
                  </div>

                  <div className="space-y-2">
                    <Label>Minimum Interval Between Scans</Label>
                    <Input
                      value={config.loadBalancing.minIntervalBetween}
                      onChange={(e) => updateConfig('loadBalancing.minIntervalBetween', e.target.value)}
                      placeholder="30m"
                    />
                    <p className="text-xs text-gray-500">
                      Format: 30m, 1h, 2h30m
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Maximum Scans Per Hour</Label>
                    <Input
                      type="number"
                      min="1"
                      max="60"
                      value={config.loadBalancing.maxScansPerHour}
                      onChange={(e) => updateConfig('loadBalancing.maxScansPerHour', parseInt(e.target.value) || 1)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="priority" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Target className="h-4 w-4" />
                    <span>Priority-Based Scheduling</span>
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">
                    Configure priority rules to control the order in which groups are scanned
                  </p>
                  <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                    <strong>Operators:</strong> Include (default priority), Exclude (skip matching groups),
                    Exclusive (only scan matching groups), Required (always include regardless of other rules)
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.priorityConfig?.enabled || false}
                      onCheckedChange={(checked) =>
                        setConfig(prev => ({
                          ...prev,
                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), enabled: checked }
                        }))
                      }
                    />
                    <Label className="text-sm">Enable priority-based scheduling</Label>
                  </div>

                  {config.priorityConfig?.enabled && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-xs">Default Weight</Label>
                          <Input
                            type="number"
                            min="1"
                            max="100"
                            value={config.priorityConfig?.defaultWeight || 50}
                            onChange={(e) =>
                              setConfig(prev => ({
                                ...prev,
                                priorityConfig: {
                                  ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig),
                                  defaultWeight: parseInt(e.target.value) || 50
                                }
                              }))
                            }
                            className="text-xs"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Weight for groups that don't match any rule
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={config.priorityConfig?.scheduleByWeight || true}
                            onCheckedChange={(checked) =>
                              setConfig(prev => ({
                                ...prev,
                                priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), scheduleByWeight: checked }
                              }))
                            }
                          />
                          <Label className="text-xs">Higher weight = higher priority</Label>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-sm font-medium">Priority Rules</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newRule: PriorityRule = {
                                id: `rule-${Date.now()}`,
                                name: 'New Rule',
                                pattern: '*',
                                weight: 50,
                                description: '',
                                enabled: true,
                                operator: 'include' as PriorityRuleOperator,
                              };
                              setConfig(prev => ({
                                ...prev,
                                priorityConfig: {
                                  ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig),
                                  rules: [...(prev.priorityConfig?.rules || []), newRule]
                                }
                              }));
                            }}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Rule
                          </Button>
                        </div>

                        <div className="space-y-2 max-h-64 overflow-y-auto">
                          {(config.priorityConfig?.rules || []).map((rule, index) => (
                            <Card key={rule.id} className="p-3">
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-2">
                                    <Switch
                                      checked={rule.enabled}
                                      onCheckedChange={(checked) => {
                                        const newRules = [...(config.priorityConfig?.rules || [])];
                                        newRules[index] = { ...rule, enabled: checked };
                                        setConfig(prev => ({
                                          ...prev,
                                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                        }));
                                      }}
                                    />
                                    <Input
                                      value={rule.name}
                                      onChange={(e) => {
                                        const newRules = [...(config.priorityConfig?.rules || [])];
                                        newRules[index] = { ...rule, name: e.target.value };
                                        setConfig(prev => ({
                                          ...prev,
                                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                        }));
                                      }}
                                      className="text-xs font-medium"
                                      placeholder="Rule name"
                                    />
                                  </div>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const newRules = (config.priorityConfig?.rules || []).filter((_, i) => i !== index);
                                      setConfig(prev => ({
                                        ...prev,
                                        priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                      }));
                                    }}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>

                                <div className="grid grid-cols-3 gap-2">
                                  <div>
                                    <Label className="text-xs">Pattern (wildcards allowed)</Label>
                                    <Input
                                      value={rule.pattern}
                                      onChange={(e) => {
                                        const newRules = [...(config.priorityConfig?.rules || [])];
                                        newRules[index] = { ...rule, pattern: e.target.value };
                                        setConfig(prev => ({
                                          ...prev,
                                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                        }));
                                      }}
                                      className="text-xs"
                                      placeholder="e.g., *Admin*, FM-*"
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-xs">Weight</Label>
                                    <Input
                                      type="number"
                                      min="1"
                                      max="100"
                                      value={rule.weight}
                                      onChange={(e) => {
                                        const newRules = [...(config.priorityConfig?.rules || [])];
                                        newRules[index] = { ...rule, weight: parseInt(e.target.value) || 50 };
                                        setConfig(prev => ({
                                          ...prev,
                                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                        }));
                                      }}
                                      className="text-xs"
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-xs">Operator</Label>
                                    <Select
                                      value={rule.operator || 'include'}
                                      onValueChange={(value: PriorityRuleOperator) => {
                                        const newRules = [...(config.priorityConfig?.rules || [])];
                                        newRules[index] = { ...rule, operator: value };
                                        setConfig(prev => ({
                                          ...prev,
                                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                        }));
                                      }}
                                    >
                                      <SelectTrigger className="text-xs">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="include">Include</SelectItem>
                                        <SelectItem value="exclude">Exclude</SelectItem>
                                        <SelectItem value="exclusive">Exclusive</SelectItem>
                                        <SelectItem value="required">Required</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>

                                <Input
                                  value={rule.description}
                                  onChange={(e) => {
                                    const newRules = [...(config.priorityConfig?.rules || [])];
                                    newRules[index] = { ...rule, description: e.target.value };
                                    setConfig(prev => ({
                                      ...prev,
                                      priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig), rules: newRules }
                                    }));
                                  }}
                                  className="text-xs"
                                  placeholder="Optional description"
                                />
                              </div>
                            </Card>
                          ))}
                        </div>

                        {(config.priorityConfig?.rules || []).length === 0 && (
                          <div className="text-center py-4 text-muted-foreground">
                            <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p className="text-sm">No priority rules defined</p>
                            <p className="text-xs">Add rules to control scan scheduling order</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Bell className="h-4 w-4" />
                    <span>Notification Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.notificationConfig.onCompletion}
                      onCheckedChange={(checked) => updateConfig('notificationConfig.onCompletion', checked)}
                    />
                    <Label>Notify on Completion</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.notificationConfig.onFailure}
                      onCheckedChange={(checked) => updateConfig('notificationConfig.onFailure', checked)}
                    />
                    <Label>Notify on Failure</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.notificationConfig.onSummary}
                      onCheckedChange={(checked) => updateConfig('notificationConfig.onSummary', checked)}
                    />
                    <Label>Send Summary Reports</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Status Information */}
          {isEditing && (config.lastScanTime || config.nextScanTime) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Status Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {config.lastScanTime && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Last Scan:</span>
                    <span>{new Date(config.lastScanTime).toLocaleString()}</span>
                  </div>
                )}
                {config.nextScanTime && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Next Scan:</span>
                    <span>{new Date(config.nextScanTime).toLocaleString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? 'Saving...' : isEditing ? 'Update Configuration' : 'Create Configuration'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
