# Frontend Implementation Details

## Component Architecture

### UI Component System

#### Base UI Components (`src/components/ui/`)
The application uses a consistent design system built on Radix UI primitives:

```typescript
// Example: Button component with variants
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}
```

**Key Components**:
- `Button` - Primary action component with multiple variants
- `Input` - Form input with validation states
- `Dialog` - Modal dialogs with accessibility
- `Select` - Dropdown selection with search
- `Tabs` - Navigation tabs with keyboard support
- `Toast` - Notification system
- `Progress` - Progress bars with enhanced metadata

#### Layout Components (`src/components/layout/`)

**Layout.tsx** - Main application layout:
```typescript
interface LayoutProps {
  children: React.ReactNode;
  filters?: React.ReactNode; // Slot for page-specific filters
}
```

Features:
- Responsive sidebar navigation
- Repository selector integration
- Page-specific filter slots
- Mobile-friendly collapsible menu

### State Management Implementation

#### Repository Context (`src/context/RepositoryContext.tsx`)

**Global State Structure**:
```typescript
interface RepositoryContextType {
  selectedRepoId: string;
  setSelectedRepoId: (id: string) => void;
  isLoading: boolean;
  repositories: Repository[];
  duplicateRepository: (id: string) => Promise<void>;
  refreshRepositories: (forceRefresh?: boolean) => Promise<boolean>;
  onRepositoryChange: (callback: RepositoryChangeCallback) => () => void;
}
```

**Implementation Patterns**:
- Centralized repository state management
- Change notification system for components
- Automatic refresh with caching
- Repository duplication with validation

#### Custom Hooks

**useWebSocketProgress.ts** - WebSocket connection management:
```typescript
interface UseWebSocketProgressReturn {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastMessage: any;
  error: string | null;
}
```

Features:
- Automatic reconnection with exponential backoff
- Connection state tracking
- Message subscription management
- Error handling and recovery

**useAdminWebSocket.ts** - Admin-level WebSocket subscriptions:
- System-wide monitoring
- Task management updates
- Global scan progress
- Service health monitoring

### API Integration Implementation

#### API Client (`src/api/client.ts`)

**Core Features**:
```typescript
class ApiClient {
  private cache = new Map<string, CacheEntry>();
  private activeRequests = new Map<string, AbortController>();
  
  async request<T>(endpoint: string, options?: RequestOptions): Promise<T>;
  cancelRequestsByPattern(pattern: string): number;
  clearCache(pattern?: string): void;
}
```

**Request Management**:
- Request deduplication to prevent duplicate API calls
- Automatic request cancellation on component unmount
- Response caching with TTL for GET requests
- Error handling with custom `ApiError` class

**Error Handling Strategy**:
```typescript
export class ApiError extends Error {
  status: number;
  data: any;
  
  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}
```

### Form Implementation

#### React Hook Form Integration

**Form Validation Patterns**:
```typescript
interface CreatePresetFormData {
  name: string;
  description: string;
  reportType: 'groups' | 'users';
  query: string;
  schedule?: ScheduleConfig;
}

const form = useForm<CreatePresetFormData>({
  resolver: zodResolver(createPresetSchema),
  defaultValues: {
    name: '',
    description: '',
    reportType: 'groups',
    query: '',
  },
});
```

**Form Components**:
- Consistent validation patterns
- Error state management
- Loading state handling
- Optimistic updates

### Search Implementation

#### Advanced Search Components

**SearchInputWithSuggestions.tsx**:
```typescript
interface SearchInputWithSuggestionsProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => void;
  suggestions: string[];
  placeholder?: string;
  helpContent?: React.ReactNode;
}
```

**Features**:
- Auto-suggestions with keyboard navigation
- Query syntax highlighting
- Search help integration
- Debounced search execution

**Query Syntax Support**:
- Field-specific filters: `lob:engineering`, `type:group`
- Boolean operators: `AND`, `OR`, `NOT`
- Parenthetical grouping: `(lob:eng OR lob:ops) AND type:group`
- Wildcard matching: `name:*admin*`

### Real-time Features Implementation

#### WebSocket Integration

**Connection Management**:
```typescript
const useWebSocketProgress = (endpoint: string) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  
  // Automatic reconnection logic
  const reconnect = useCallback(() => {
    if (reconnectAttempts.current < maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
      setTimeout(connect, delay);
      reconnectAttempts.current++;
    }
  }, [connect]);
};
```

**Progress Broadcasting**:
- Real-time scan progress updates
- Task status monitoring
- System health indicators
- Connection status feedback

### Performance Optimizations

#### Component Optimization

**Memoization Patterns**:
```typescript
const GroupListItem = React.memo(({ group, onUsageClick }: GroupListItemProps) => {
  const handleUsageClick = useCallback(() => {
    onUsageClick(group.name);
  }, [group.name, onUsageClick]);
  
  return (
    <div className="group-item">
      {/* Component content */}
    </div>
  );
});
```

**Virtual Scrolling** (for large lists):
- Implemented for group and user lists
- Reduces DOM nodes for better performance
- Maintains scroll position during updates

#### Request Optimization

**Caching Strategy**:
```typescript
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

const cache = new Map<string, CacheEntry>();

const getCachedData = (key: string): any | null => {
  const entry = cache.get(key);
  if (entry && Date.now() - entry.timestamp < entry.ttl) {
    return entry.data;
  }
  cache.delete(key);
  return null;
};
```

### Error Handling Implementation

#### Error Boundaries

**Global Error Boundary**:
```typescript
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }
}
```

**API Error Handling**:
- Consistent error message display
- Toast notifications for user feedback
- Retry mechanisms for failed requests
- Graceful degradation for non-critical features

### Accessibility Implementation

#### ARIA Support

**Screen Reader Support**:
```typescript
<button
  aria-label={`Search for groups in ${repository.name}`}
  aria-describedby="search-help"
  aria-expanded={showSuggestions}
  role="combobox"
>
  Search
</button>
```

**Keyboard Navigation**:
- Tab order management
- Keyboard shortcuts for common actions
- Focus indicators
- Skip links for navigation

### Testing Implementation

#### Component Testing Strategy

**Test Structure**:
```typescript
describe('GroupListItem', () => {
  it('should render group information correctly', () => {
    const mockGroup = createMockGroup();
    render(<GroupListItem group={mockGroup} onUsageClick={jest.fn()} />);
    
    expect(screen.getByText(mockGroup.name)).toBeInTheDocument();
    expect(screen.getByText(mockGroup.description)).toBeInTheDocument();
  });
  
  it('should handle usage click events', () => {
    const mockOnUsageClick = jest.fn();
    const mockGroup = createMockGroup();
    
    render(<GroupListItem group={mockGroup} onUsageClick={mockOnUsageClick} />);
    
    fireEvent.click(screen.getByText('Track Usage'));
    expect(mockOnUsageClick).toHaveBeenCalledWith(mockGroup.name);
  });
});
```

### Build Configuration

#### Vite Configuration (`vite.config.ts`)

**Key Features**:
```typescript
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    proxy: {
      '/api': 'http://localhost:8080',
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
      },
    },
  },
});
```

**Optimizations**:
- Path aliases for clean imports
- API and WebSocket proxying
- Hot module replacement
- Production build optimizations

### TypeScript Implementation

#### Type Safety Patterns

**API Types**:
```typescript
interface Repository {
  id: string;
  name: string;
  url: string;
  branch: string;
  status: 'active' | 'inactive' | 'error';
  lastSync?: string;
  lastCommit?: string;
}

interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}
```

**Component Props**:
```typescript
interface GroupListProps {
  groups: Group[];
  loading: boolean;
  error: string | null;
  onGroupSelect: (group: Group) => void;
  onUsageTrack: (groupName: string) => void;
}
```

**Utility Types**:
```typescript
type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;
```
