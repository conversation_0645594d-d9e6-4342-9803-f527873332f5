import { useCallback, useRef } from "react"
import { useToast } from "@/components/ui/use-toast"
import api from "@/api/client"
import type { FailedTask } from "@/types/scheduler"

interface UseTaskActionsProps {
  recentlyRetriedTasks: Set<string>
  setRecentlyRetriedTasks: (fn: (prev: Set<string>) => Set<string>) => void
  loadSchedulerData: (showLoading?: boolean) => Promise<void>
  setFailedTasksFiltered: (filterFn: (tasks: FailedTask[]) => FailedTask[]) => void
  setPlannedTasksFiltered: (filterFn: (tasks: any[]) => any[]) => void
  setActiveTasksFiltered: (filterFn: (tasks: any[]) => any[]) => void
  failedTasks: FailedTask[]
  onRefresh?: () => void
}

interface UseTaskActionsReturn {
  handleRetryTask: (taskId: string) => Promise<void>
  handleDeleteTask: (taskId: string) => Promise<void>
  handleCancelTask: (taskId: string, taskType?: string) => Promise<void>
  handleTriggerTask: (taskId: string) => Promise<void>
  handleDelayTask: (taskId: string, delayMinutes: number) => Promise<void>
  handlePauseTask: (taskId: string, taskType?: string) => Promise<void>
  handleResumeTask: (taskId: string, taskType?: string) => Promise<void>
  handleReorderTasks: (taskIds: string[]) => Promise<void>
  handleEditTask: (taskId: string, updates: any) => Promise<void>
  handleCleanupStaleTasks: () => Promise<void>
  handleDeleteAllScheduledTasks: () => Promise<void>
  handleDeleteAllFailedTasks: () => Promise<void>
  handleTaskRestarted: (taskId: string, taskType: string) => void
  removeStaleActiveTask: (taskId: string) => void
}

export const useTaskActions = ({
  recentlyRetriedTasks,
  setRecentlyRetriedTasks,
  loadSchedulerData,
  setFailedTasksFiltered,
  setPlannedTasksFiltered,
  setActiveTasksFiltered,
  failedTasks,
  onRefresh
}: UseTaskActionsProps): UseTaskActionsReturn => {
  const { toast } = useToast()

  // Map task type to service for API calls
  const getServiceForTaskType = useCallback((taskType: string): string => {
    switch (taskType) {
      case 'report':
        return 'report'
      case 'scan':
        return 'usage'
      case 'auto-scan':
        return 'auto-scan'
      default:
        return 'all'
    }
  }, [])

  // Retry failed task
  const handleRetryTask = useCallback(async (taskId: string) => {
    // Prevent concurrent retry operations on the same task
    if (recentlyRetriedTasks.has(taskId)) {
      toast({
        title: 'Retry In Progress',
        description: 'This task is already being retried. Please wait for the operation to complete.',
        variant: 'destructive',
      })
      return
    }

    try {
      // Find the task to get its type
      const task = failedTasks.find(t => t.id === taskId)
      if (!task) {
        toast({
          title: 'Task Not Found',
          description: 'The task you are trying to retry was not found. It may have been already processed or removed.',
          variant: 'destructive',
        })
        return
      }

      // Mark task as being retried to prevent concurrent operations
      setRecentlyRetriedTasks(prev => new Set(prev).add(taskId))

      // Immediately remove from failed tasks for instant UI feedback
      setFailedTasksFiltered(tasks => tasks.filter(t => t.id !== taskId))

      const service = getServiceForTaskType(task.type)

      // Use the working restart-scan endpoint instead of the unimplemented retry endpoint
      const response = await api.scheduler.restartSpecificScan({
        action: 'retry',
        service: service as any,
        taskId: taskId,
        taskType: task.type,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || `Task ${task.name} retry initiated`,
        })

        // Immediately remove from failed tasks to prevent showing in both lists
        setFailedTasksFiltered(prev => prev.filter(t => t.id !== taskId))

        // The backend will broadcast task_restarted event which will add it to active tasks
        // Clean up the recently retried tasks set after a delay to allow backend processing
        setTimeout(() => {
          setRecentlyRetriedTasks(prev => {
            const newSet = new Set(prev)
            newSet.delete(taskId)
            return newSet
          })
        }, 5000) // 5 seconds should be enough for the retry to be processed
      } else {
        // Handle specific error cases with more detailed feedback
        if (response.message?.includes('not found')) {
          // Task was already processed or removed
          toast({
            title: 'Task No Longer Available',
            description: 'This failed task may have already been processed or removed. Refreshing the task list...',
            variant: 'destructive',
          })

          // Let backend handle state - refresh data to get current state
          setTimeout(() => loadSchedulerData(false), 500)
        } else if (response.message?.includes('authentication') || response.message?.includes('auth')) {
          toast({
            title: 'Authentication Error',
            description: 'Failed to retry task due to authentication issues. Please check repository credentials.',
            variant: 'destructive',
          })
        } else if (response.message?.includes('permission') || response.message?.includes('forbidden')) {
          toast({
            title: 'Permission Error',
            description: 'Failed to retry task due to insufficient permissions. Please check access rights.',
            variant: 'destructive',
          })
        } else if (response.message?.includes('network') || response.message?.includes('connection')) {
          toast({
            title: 'Connection Error',
            description: 'Failed to retry task due to network issues. Please check connectivity and try again.',
            variant: 'destructive',
          })
        } else if (response.message?.includes('timeout')) {
          toast({
            title: 'Timeout Error',
            description: 'Task retry timed out. The repository may be large or temporarily unavailable.',
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Retry Failed',
            description: response.message || 'Failed to retry task. Please try again or contact support if the issue persists.',
            variant: 'destructive',
          })
        }
      }
    } catch (err) {
      console.error('Error retrying task:', err)

      // Provide specific error messages based on error type
      let errorMessage = 'An unexpected error occurred while retrying the task';

      if (err instanceof TypeError && err.message.includes('fetch')) {
        errorMessage = 'Network error: Unable to connect to the server. Please check your connection and try again.';
      } else if (err instanceof Error) {
        if (err.message.includes('401') || err.message.includes('unauthorized')) {
          errorMessage = 'Authentication error: Please log in again to retry tasks.';
        } else if (err.message.includes('403') || err.message.includes('forbidden')) {
          errorMessage = 'Permission error: You do not have permission to retry this task.';
        } else if (err.message.includes('500')) {
          errorMessage = 'Server error: The scheduler service is temporarily unavailable. Please try again later.';
        }
      }

      toast({
        title: 'Retry Failed',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      // Always clean up the recently retried tasks set on error
      setTimeout(() => {
        setRecentlyRetriedTasks(prev => {
          const newSet = new Set(prev)
          newSet.delete(taskId)
          return newSet
        })
      }, 1000) // Shorter delay for error cases
    }
  }, [recentlyRetriedTasks, setRecentlyRetriedTasks, failedTasks, getServiceForTaskType, setFailedTasksFiltered, loadSchedulerData, toast])

  // Delete task (works for both failed and scheduled tasks)
  const handleDeleteTask = useCallback(async (taskId: string) => {
    try {
      // Find the task in failed tasks first
      let task = failedTasks.find(t => t.id === taskId)
      let isScheduledTask = false

      // If not found in failed tasks, it might be a scheduled task
      // For scheduled tasks, we'll try to cancel them instead of delete
      if (!task) {
        isScheduledTask = true
      }

      // Call the appropriate API endpoint based on task type and location
      let response;

      if (isScheduledTask) {
        // For scheduled tasks, use cancellation which removes them from the schedule
        response = await api.scheduler.cancelTask({
          action: 'cancel',
          service: 'auto-scan', // Most scheduled tasks are auto-scan
          taskId: taskId,
          taskType: 'auto-scan',
        })
      } else if (task && task.type === 'scan') {
        // For failed usage scans, use removeSpecificScan
        response = await api.scheduler.removeSpecificScan({
          action: 'remove',
          service: 'usage',
          taskId: taskId,
          taskType: task.type,
        })
      } else {
        // For other task types, we might need different endpoints
        // For now, show an error as we don't have a generic delete endpoint
        toast({
          title: 'Error',
          description: `Deletion not supported for task type: ${task?.type || 'unknown'}`,
          variant: 'destructive',
        })
        return
      }

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Task deleted successfully',
        })

        // Let WebSocket events handle the UI updates - refresh data to get current state
        setTimeout(() => loadSchedulerData(false), 500)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete task',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Error deleting task:', err)

      // Handle specific error cases
      if (err instanceof Error && err.message.includes('404')) {
        // Task not found - likely already completed or cancelled
        toast({
          title: 'Task Not Found',
          description: 'This task is no longer available. It may have already been completed or cancelled.',
          variant: 'destructive',
        })

        // Trigger a refresh to update the UI
        if (onRefresh) {
          onRefresh()
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete task',
          variant: 'destructive',
        })
      }
    }
  }, [failedTasks, setFailedTasksFiltered, loadSchedulerData, toast, onRefresh])

  // Handle task cancellation
  const handleCancelTask = useCallback(async (taskId: string, taskType?: string) => {
    try {
      const response = await api.scheduler.cancelTask({
        action: 'cancel',
        service: 'all', // Default to all services for cancel operations
        taskId: taskId,
        taskType: taskType,
      })

      if (response.success) {

        toast({
          title: 'Success',
          description: response.message || 'Task cancellation requested - backend will update task state',
        })

        // Let WebSocket events handle the UI updates - no optimistic updates
        // The backend will broadcast task_cancelled event which will trigger UI refresh
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to cancel task',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to cancel task:', err)

      // Handle specific error cases
      if (err instanceof Error && err.message.includes('404')) {
        // Task not found - likely already completed
        toast({
          title: 'Task Not Found',
          description: 'This task is no longer running. It may have already completed.',
          variant: 'destructive',
        })

        // Remove the stale task from active tasks list immediately
        setActiveTasksFiltered(tasks => tasks.filter(task => task.id !== taskId))

        // Trigger a refresh to update the UI
        if (onRefresh) {
          onRefresh()
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to cancel task',
          variant: 'destructive',
        })
      }
    }
  }, [setPlannedTasksFiltered, setActiveTasksFiltered, setFailedTasksFiltered, loadSchedulerData, toast, onRefresh])

  // Enhanced planned task management handlers
  const handleTriggerTask = useCallback(async (taskId: string) => {
    try {
      // Use the proper trigger task API for scheduled tasks
      const response = await api.scheduler.triggerTask({
        action: 'trigger',
        service: 'auto-scan', // Scheduled tasks are auto-scan tasks
        taskId: taskId,
      })

      if (response.success) {

        toast({
          title: 'Success',
          description: response.message || 'Task triggered successfully',
        })

        // Let WebSocket events handle the UI updates - no optimistic updates
        // The backend will broadcast task_triggered event and handle state transitions
        // Refresh data to get updated task states with a delay to allow backend processing
        setTimeout(() => {
          loadSchedulerData(false)
        }, 1500)
      } else {
        // Handle specific error cases
        if (response.message?.includes('not found')) {
          // Task was already processed or removed
          toast({
            title: 'Task No Longer Available',
            description: 'This task may have already been processed or removed. Refreshing the task list...',
            variant: 'destructive',
          })

          // Let backend handle state - refresh data to get current state
          setTimeout(() => loadSchedulerData(false), 500)
        } else {
          toast({
            title: 'Error',
            description: response.message || 'Failed to trigger task',
            variant: 'destructive',
          })
        }
      }
    } catch (err: any) {
      // Handle 404 errors specifically (task not found)
      if (err?.status === 404 || err?.message?.includes('404') || err?.message?.includes('not found')) {
        toast({
          title: 'Task No Longer Available',
          description: 'This task may have already been processed or removed. Refreshing the task list...',
          variant: 'destructive',
        })

        // Refresh data to get current state
        setTimeout(() => loadSchedulerData(false), 500)
      } else {
        toast({
          title: 'Error',
          description: err?.message || 'Failed to trigger task',
          variant: 'destructive',
        })
      }
    }
  }, [setPlannedTasksFiltered, loadSchedulerData, toast])

  // Delay task (placeholder implementation)
  const handleDelayTask = useCallback(async (taskId: string, delayMinutes: number) => {
    try {
      // For now, show a placeholder message since delay API needs to be implemented
      toast({
        title: 'Feature Coming Soon',
        description: `Task delay functionality is being developed. Would delay task by ${delayMinutes} minutes.`,
      })
    } catch (err) {
      console.error('Failed to delay task:', err)
      toast({
        title: 'Error',
        description: 'Failed to delay task',
        variant: 'destructive',
      })
    }
  }, [toast])

  // Pause task
  const handlePauseTask = useCallback(async (taskId: string, taskType?: string) => {
    try {
      const response = await api.scheduler.pauseScheduler({
        action: 'pause',
        service: 'all', // Default to all services for pause operations
        taskId: taskId,
        taskType: taskType,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Task pause requested',
        })
      } else {
        toast({
          title: 'Info',
          description: response.message || 'Task pause functionality is being developed.',
        })
      }
    } catch (err) {
      console.error('Failed to pause task:', err)

      // Handle specific error cases
      if (err instanceof Error && err.message.includes('404')) {
        // Task not found - likely already completed
        toast({
          title: 'Task Not Found',
          description: 'This task is no longer running. It may have already completed.',
          variant: 'destructive',
        })

        // Remove the stale task from active tasks list immediately
        setActiveTasksFiltered(tasks => tasks.filter(task => task.id !== taskId))

        // Trigger a refresh to update the UI
        if (onRefresh) {
          onRefresh()
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to pause task',
          variant: 'destructive',
        })
      }
    }
  }, [toast, setActiveTasksFiltered, onRefresh])

  // Resume task
  const handleResumeTask = useCallback(async (taskId: string, taskType?: string) => {
    try {
      const response = await api.scheduler.resumeScheduler({
        action: 'resume',
        service: 'all', // Default to all services for resume operations
        taskId: taskId,
        taskType: taskType,
      })

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Task resume requested',
        })
      } else {
        toast({
          title: 'Info',
          description: response.message || 'Task resume functionality is being developed.',
        })
      }
    } catch (err) {
      console.error('Failed to resume task:', err)

      // Handle specific error cases
      if (err instanceof Error && err.message.includes('404')) {
        // Task not found - likely already completed
        toast({
          title: 'Task Not Found',
          description: 'This task is no longer running. It may have already completed.',
          variant: 'destructive',
        })

        // Remove the stale task from active tasks list immediately
        setActiveTasksFiltered(tasks => tasks.filter(task => task.id !== taskId))

        // Trigger a refresh to update the UI
        if (onRefresh) {
          onRefresh()
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to resume task',
          variant: 'destructive',
        })
      }
    }
  }, [toast, setActiveTasksFiltered, onRefresh])

  // Reorder tasks (placeholder implementation)
  const handleReorderTasks = useCallback(async (taskIds: string[]) => {
    try {
      // For now, show a placeholder message since reorder task API needs to be implemented
      toast({
        title: 'Feature Coming Soon',
        description: 'Task reordering functionality is being developed.',
      })
    } catch (err) {
      console.error('Failed to reorder tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to reorder tasks',
        variant: 'destructive',
      })
    }
  }, [toast])

  // Edit task (placeholder implementation)
  const handleEditTask = useCallback(async (taskId: string, updates: any) => {
    try {
      // For now, show a placeholder message since update task API needs to be implemented
      toast({
        title: 'Feature Coming Soon',
        description: 'Task editing functionality is being developed.',
      })
    } catch (err) {
      console.error('Failed to edit task:', err)
      toast({
        title: 'Error',
        description: 'Failed to edit task',
        variant: 'destructive',
      })
    }
  }, [toast])

  // Cleanup stale tasks
  const handleCleanupStaleTasks = useCallback(async () => {
    try {
      const response = await api.scheduler.cleanupStaleTasks()

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Stale tasks cleaned up successfully',
        })

        // Refresh data to show updated task lists
        setTimeout(() => loadSchedulerData(false), 500)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to cleanup stale tasks',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to cleanup stale tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to cleanup stale tasks',
        variant: 'destructive',
      })
    }
  }, [loadSchedulerData, toast])

  // Helper function to remove stale task from UI when 404 errors occur
  const removeStaleActiveTask = useCallback((taskId: string) => {
    setActiveTasksFiltered(tasks => tasks.filter(task => task.id !== taskId))

    // Also trigger a refresh to ensure UI is in sync
    if (onRefresh) {
      setTimeout(() => onRefresh(), 100)
    }
  }, [setActiveTasksFiltered, onRefresh])

  // Delete all scheduled tasks
  const handleDeleteAllScheduledTasks = useCallback(async () => {
    try {
      const response = await api.scheduler.deleteAllScheduledTasks()

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'All scheduled tasks deleted successfully',
        })

        // Refresh data to show updated task lists
        setTimeout(() => loadSchedulerData(false), 500)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete all scheduled tasks',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to delete all scheduled tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to delete all scheduled tasks',
        variant: 'destructive',
      })
    }
  }, [loadSchedulerData, toast])

  // Delete all failed tasks
  const handleDeleteAllFailedTasks = useCallback(async () => {
    try {
      const response = await api.scheduler.clearFailedScans()

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'All failed tasks cleared successfully',
        })

        // Refresh data to show updated task lists
        setTimeout(() => loadSchedulerData(false), 500)
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to clear all failed tasks',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Failed to clear all failed tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to clear all failed tasks',
        variant: 'destructive',
      })
    }
  }, [loadSchedulerData, toast])

  // Use refs to avoid circular dependencies
  const setRecentlyRetriedTasksRef = useRef(setRecentlyRetriedTasks)
  const loadSchedulerDataRef = useRef(loadSchedulerData)

  // Update refs when values change
  setRecentlyRetriedTasksRef.current = setRecentlyRetriedTasks
  loadSchedulerDataRef.current = loadSchedulerData

  // Handle task restarted event
  const handleTaskRestarted = useCallback((taskId: string, taskType: string) => {
    // Add to recently retried tasks to prevent it from being moved back to failed immediately
    setRecentlyRetriedTasksRef.current((prev: Set<string>) => {
      const newSet = new Set(prev).add(taskId)
      return newSet
    })

    // Immediately remove the task from failed tasks for instant UI feedback
    setFailedTasksFiltered(tasks => tasks.filter(task => task.id !== taskId))

    // Refresh data immediately to get the updated active tasks list
    loadSchedulerDataRef.current(false)

    // Clean up the recently retried tasks set after a delay
    setTimeout(() => {
      setRecentlyRetriedTasksRef.current((prev: Set<string>) => {
        const newSet = new Set(prev)
        newSet.delete(taskId)
        return newSet
      })
    }, 8000) // Slightly shorter cleanup time
  }, [])

  return {
    handleRetryTask,
    handleDeleteTask,
    handleCancelTask,
    handleTriggerTask,
    handleDelayTask,
    handlePauseTask,
    handleResumeTask,
    handleReorderTasks,
    handleEditTask,
    handleCleanupStaleTasks,
    handleDeleteAllScheduledTasks,
    handleDeleteAllFailedTasks,
    handleTaskRestarted,
    removeStaleActiveTask
  }
}
