import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  RefreshCw,
  Search,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader,
  X,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';

import { scanLogsApi } from '@/api/client';
import type {
  ScanLogEntry,
  ScanLogSummary,
  ScanLogLevel
} from '@/types/scanLogs';
import {
  LogLevelColors,
  formatDuration,
  formatTimestamp,
  formatRelativeTime,
  getLogLevelText
} from '@/types/scanLogs';

interface GroupScanLogsProps {
  repoId: string;
  groupName: string;
  className?: string;
}

const GroupScanLogs: React.FC<GroupScanLogsProps> = ({ repoId, groupName, className }) => {
  const [logs, setLogs] = useState<ScanLogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<ScanLogLevel | ''>('');
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Load group-specific scan logs
  const loadLogs = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) {
        setLoading(true);
      }
      setError(null);

      console.log('Loading group scan logs for:', { repoId, groupName, level: selectedLevel });
      console.log('API call starting...');

      const startTime = Date.now();
      const response = await scanLogsApi.getGroupLogs(repoId, groupName, {
        level: selectedLevel || undefined,
        page: 1,
        pageSize: 20 // Show recent logs only
      });
      const duration = Date.now() - startTime;

      console.log(`Group scan logs response received in ${duration}ms:`, response);
      console.log('Setting logs to:', response.logs || []);
      setLogs(response.logs || []);
    } catch (err) {
      console.error('Failed to load group scan logs:', err);
      console.error('Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined
      });
      setError('Failed to load scan logs');
      setLogs([]);
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [repoId, groupName, selectedLevel]);

  // Initial load
  useEffect(() => {
    if (expanded) {
      loadLogs(true);
    }
  }, [expanded, loadLogs]);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh || !expanded) {
      console.log('Auto-refresh disabled or component collapsed:', { autoRefresh, expanded });
      return;
    }

    console.log('Starting auto-refresh for group scan logs');
    const interval = setInterval(() => {
      console.log('Auto-refreshing group scan logs...');
      loadLogs(false); // Don't show loading spinner for auto-refresh
    }, 30000); // Refresh every 30 seconds

    return () => {
      console.log('Stopping auto-refresh for group scan logs');
      clearInterval(interval);
    };
  }, [autoRefresh, expanded, loadLogs]);

  // Manual refresh
  const handleRefresh = useCallback(async () => {
    console.log('Manual refresh triggered for group scan logs:', { repoId, groupName });
    console.log('Refresh button clicked - starting manual refresh');
    setRefreshing(true);
    try {
      await loadLogs(true);
      console.log('Manual refresh completed successfully');
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setRefreshing(false);
    }
  }, [loadLogs, repoId, groupName]);

  // Reload when level filter changes
  useEffect(() => {
    if (expanded) {
      console.log('Level filter changed, reloading logs:', selectedLevel);
      loadLogs(true);
    }
  }, [selectedLevel, expanded, loadLogs]);

  // Get log level icon
  const getLogLevelIcon = (level: ScanLogLevel) => {
    switch (level) {
      case 'info':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'error':
        return <XCircle className="h-4 w-4" />;
      case 'debug':
        return <Search className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="py-3 px-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-base font-medium">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
              className="p-0 h-auto mr-3 hover:bg-transparent"
            >
              {expanded ? (
                <ChevronUp className="h-4 w-4 text-muted-foreground" />
              ) : (
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
            Scan Logs
            {logs && logs.length > 0 && (
              <Badge variant="outline" className="ml-2 text-xs">
                {logs.length} recent
              </Badge>
            )}
          </CardTitle>

          <div className="flex items-center space-x-2">
            {expanded && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-xs text-muted-foreground">Auto-refresh:</label>
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => {
                      console.log('Auto-refresh toggled:', e.target.checked);
                      setAutoRefresh(e.target.checked);
                    }}
                    className="rounded w-3 h-3"
                  />
                  {autoRefresh && (
                    <span className="text-xs text-green-600 flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                      30s
                    </span>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading || refreshing}
                  className="h-7 px-2 text-xs"
                >
                  <RefreshCw className={`h-3 w-3 mr-1 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
                  {refreshing ? 'Refreshing...' : 'Refresh'}
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      {expanded && (
        <CardContent className="px-4 pb-4">
          {/* Filters */}
          <div className="flex items-center space-x-4 mb-3">
            <div>
              <label className="block text-xs font-medium mb-1 text-muted-foreground">Level</label>
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value as ScanLogLevel | '')}
                className="border rounded px-2 py-1 text-xs h-7"
              >
                <option value="">All Levels</option>
                <option value="info">Info</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="debug">Debug</option>
              </select>
            </div>

            <div className="flex-1" />

            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/settings?tab=scan-logs&repoId=${repoId}&groupName=${groupName}`, '_blank')}
              className="h-7 px-2 text-xs"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View All Logs
            </Button>
          </div>

          {/* Error display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-2 mb-3">
              <div className="flex items-center text-red-700">
                <XCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* Loading state */}
          {loading && (
            <div className="flex items-center justify-center py-4">
              <Loader className="h-5 w-5 animate-spin mr-2" />
              <span className="text-sm">Loading scan logs...</span>
            </div>
          )}

          {/* Logs list */}
          {!loading && (
            <>
              {!logs || logs.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  <AlertCircle className="h-6 w-6 mx-auto mb-2" />
                  <p className="text-sm">No scan logs found for this group.</p>
                  <p className="text-xs text-muted-foreground">Logs will appear here after running a usage scan.</p>
                </div>
              ) : (
                <div className="max-h-64 overflow-y-auto space-y-2 pr-2 scan-logs-scroll">
                  {logs.map((log) => (
                    <div
                      key={log.id}
                      className="border rounded-lg p-2 hover:bg-gray-50 transition-colors bg-white"
                    >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge className={`${LogLevelColors[log.level]} bg-transparent border text-xs`}>
                            <span className="flex items-center">
                              {getLogLevelIcon(log.level)}
                              <span className="ml-1">{getLogLevelText(log.level)}</span>
                            </span>
                          </Badge>

                          {log.step && (
                            <Badge variant="outline" className="text-xs">
                              {log.step}
                            </Badge>
                          )}

                          {log.sourceName && (
                            <Badge variant="secondary" className="text-xs">
                              {log.sourceName}
                            </Badge>
                          )}
                        </div>

                        <p className="text-xs font-medium mb-1">{log.message}</p>

                        {log.details && (
                          <p className="text-xs text-gray-600 mb-1">{log.details}</p>
                        )}

                        {log.error && (
                          <p className="text-xs text-red-600 bg-red-50 p-1 rounded mb-1">
                            {log.error}
                          </p>
                        )}

                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span>{formatTimestamp(log.timestamp)}</span>
                          {log.duration && (
                            <span>Duration: {formatDuration(log.duration)}</span>
                          )}
                        </div>
                      </div>

                      {log.progress && (
                        <div className="ml-2 text-right flex-shrink-0">
                          <div className="text-xs font-medium">
                            {log.progress.current} / {log.progress.total}
                          </div>
                          <div className="w-12 bg-gray-200 rounded-full h-1 mt-1">
                            <div
                              className="bg-blue-600 h-1 rounded-full transition-all"
                              style={{ width: `${log.progress.percentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  ))}
                  {logs.length > 3 && (
                    <div className="mt-2 text-xs text-muted-foreground text-center">
                      Scroll to see all {logs.length} logs
                    </div>
                  )}
                </div>
              )}
            </>
          )}

          {/* Show more link */}
          {logs && logs.length >= 10 && (
            <div className="text-center mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`/settings?tab=scan-logs&repoId=${repoId}&groupName=${groupName}`, '_blank')}
                className="h-7 px-3 text-xs"
              >
                View All Logs ({logs.length}+ available)
              </Button>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default GroupScanLogs;
