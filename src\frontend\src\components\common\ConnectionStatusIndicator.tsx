import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Wifi,
  WifiOff,
  Loader2,
  RefreshCw,
  AlertCircle,
  Eye,
  EyeOff,
  Activity
} from 'lucide-react';

export type ConnectionState = 'connected' | 'connecting' | 'disconnected' | 'error';

export interface ConnectionStatusIndicatorProps {
  isConnected: boolean;
  isConnecting: boolean;
  error?: string | null;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onRefresh?: () => void;
  showToggle?: boolean;
  showRefresh?: boolean;
  compact?: boolean;
  className?: string;
}

export function ConnectionStatusIndicator({
  isConnected,
  isConnecting,
  error,
  onConnect,
  onDisconnect,
  onRefresh,
  showToggle = false,
  showRefresh = true,
  compact = false,
  className = ""
}: ConnectionStatusIndicatorProps) {
  
  const getConnectionState = (): ConnectionState => {
    if (error) return 'error';
    if (isConnecting) return 'connecting';
    if (isConnected) return 'connected';
    return 'disconnected';
  };

  const connectionState = getConnectionState();

  const getStatusConfig = () => {
    switch (connectionState) {
      case 'connected':
        return {
          icon: <Wifi className="h-4 w-4 text-green-500" />,
          text: 'Connected',
          className: 'text-green-600',
          badgeClassName: 'bg-green-100 text-green-800 border-green-200'
        };
      case 'connecting':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin text-blue-500" />,
          text: 'Connecting...',
          className: 'text-blue-600',
          badgeClassName: 'bg-blue-100 text-blue-800 border-blue-200'
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-4 w-4 text-red-500" />,
          text: 'Connection Error',
          className: 'text-red-600',
          badgeClassName: 'bg-red-100 text-red-800 border-red-200'
        };
      default:
        return {
          icon: <WifiOff className="h-4 w-4 text-red-500" />,
          text: 'Disconnected',
          className: 'text-red-600',
          badgeClassName: 'bg-red-100 text-red-800 border-red-200'
        };
    }
  };

  const config = getStatusConfig();

  // Compact version - just icon and status
  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {config.icon}
        <span className={`text-sm ${config.className}`}>
          {config.text}
        </span>
        
        {showRefresh && !isConnected && !isConnecting && onRefresh && (
          <Button
            size="sm"
            variant="outline"
            onClick={onRefresh}
            className="h-6 px-2"
            title="Retry Connection"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  // Full version with controls and error display
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Status Display */}
      <div className="flex items-center gap-3">
        <Badge variant="outline" className={`gap-1 ${config.badgeClassName}`}>
          {config.icon}
          {config.text}
        </Badge>

        {/* Connection Controls */}
        <div className="flex items-center gap-2">
          {showToggle && (
            <Button
              size="sm"
              variant={isConnected ? "default" : "outline"}
              onClick={isConnected ? onDisconnect : onConnect}
              className="gap-1"
              disabled={isConnecting}
            >
              {isConnected ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              {isConnected ? 'Monitoring' : 'Start Monitor'}
            </Button>
          )}

          {showRefresh && onRefresh && (
            <Button
              size="sm"
              variant="outline"
              onClick={onRefresh}
              disabled={isConnecting}
              title="Refresh Connection"
            >
              <RefreshCw className={`h-3 w-3 ${isConnecting ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Connection Details */}
      {isConnected && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Activity className="h-3 w-3 text-green-500" />
          <span>Real-time updates active</span>
        </div>
      )}
    </div>
  );
}

// Specialized variants for common use cases
export function WebSocketConnectionStatus({
  isConnected,
  isConnecting,
  error,
  onConnect,
  onDisconnect,
  className = ""
}: Omit<ConnectionStatusIndicatorProps, 'showToggle' | 'showRefresh'>) {
  return (
    <ConnectionStatusIndicator
      isConnected={isConnected}
      isConnecting={isConnecting}
      error={error}
      onConnect={onConnect}
      onDisconnect={onDisconnect}
      showToggle={true}
      showRefresh={true}
      className={className}
    />
  );
}

export function CompactConnectionStatus({
  isConnected,
  isConnecting,
  error,
  onRefresh,
  className = ""
}: Pick<ConnectionStatusIndicatorProps, 'isConnected' | 'isConnecting' | 'error' | 'onRefresh' | 'className'>) {
  return (
    <ConnectionStatusIndicator
      isConnected={isConnected}
      isConnecting={isConnecting}
      error={error}
      onRefresh={onRefresh}
      compact={true}
      showRefresh={true}
      className={className}
    />
  );
}

// Connection status dot indicator for minimal spaces
export function ConnectionStatusDot({
  isConnected,
  isConnecting,
  error,
  className = ""
}: Pick<ConnectionStatusIndicatorProps, 'isConnected' | 'isConnecting' | 'error' | 'className'>) {
  const getStatusColor = () => {
    if (error) return 'bg-red-500';
    if (isConnecting) return 'bg-yellow-500 animate-pulse';
    if (isConnected) return 'bg-green-500';
    return 'bg-red-500';
  };

  const getStatusText = () => {
    if (error) return 'Connection Error';
    if (isConnecting) return 'Connecting...';
    if (isConnected) return 'Connected';
    return 'Disconnected';
  };

  return (
    <div className={`flex items-center gap-2 ${className}`} title={getStatusText()}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
      <span className="text-xs text-muted-foreground">{getStatusText()}</span>
    </div>
  );
}
