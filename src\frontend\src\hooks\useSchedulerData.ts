import { useState, useEffect, useCallback, useRef } from "react"
import { useToast } from "@/components/ui/use-toast"
import api from "@/api/client"
import type {
  SchedulerStatus,
  SchedulerOverview,
  PlannedTask,
  ActiveTask,
  FailedTask,
  CompletedTask
} from "@/types/scheduler"

interface UseSchedulerDataReturn {
  // State
  status: SchedulerStatus | null
  overview: SchedulerOverview | null
  plannedTasks: PlannedTask[]
  activeTasks: ActiveTask[]
  failedTasks: FailedTask[]
  completedTasks: CompletedTask[]
  loading: boolean
  refreshing: boolean
  error: string | null
  lastRefreshTime: Date | null

  // Actions
  loadSchedulerData: (showLoading?: boolean) => Promise<void>
  debouncedRefresh: () => void
  handleRefresh: () => Promise<void>
  handleActiveTasksRefresh: () => Promise<void>

  // Utility functions for task filtering
  setFailedTasksFiltered: (filterFn: (tasks: FailedTask[]) => FailedTask[]) => void
  setPlannedTasksFiltered: (filterFn: (tasks: PlannedTask[]) => PlannedTask[]) => void
  setActiveTasksFiltered: (filterFn: (tasks: ActiveTask[]) => ActiveTask[]) => void
}

export const useSchedulerData = (recentlyRetriedTasks: Set<string> = new Set()): UseSchedulerDataReturn => {
  const { toast } = useToast()

  // Use ref to store recently retried tasks to avoid dependency issues
  const recentlyRetriedTasksRef = useRef(recentlyRetriedTasks)

  // Add ref to track if a request is in progress to prevent race conditions
  const loadingRef = useRef(false)

  // Add ref for debouncing WebSocket-triggered refreshes
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Update ref when prop changes
  useEffect(() => {
    recentlyRetriedTasksRef.current = recentlyRetriedTasks
  }, [recentlyRetriedTasks])

  // State management
  const [status, setStatus] = useState<SchedulerStatus | null>(null)
  const [overview, setOverview] = useState<SchedulerOverview | null>(null)
  const [plannedTasks, setPlannedTasks] = useState<PlannedTask[]>([])
  const [activeTasks, setActiveTasks] = useState<ActiveTask[]>([])
  const [failedTasks, setFailedTasks] = useState<FailedTask[]>([])
  const [completedTasks, setCompletedTasks] = useState<CompletedTask[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null)

  // Load all scheduler data with request deduplication
  const loadSchedulerData = useCallback(async (showLoading = false) => {
    // Prevent multiple simultaneous requests
    if (loadingRef.current) {
      return;
    }

    loadingRef.current = true;

    try {
      if (showLoading) {
        setLoading(true)
      } else {
        setRefreshing(true)
      }
      setError(null)

      // Load all data in parallel
      const [
        statusResponse,
        overviewResponse,
        plannedWorkResponse,
        activeTasksResponse,
        failedTasksResponse,
        completedTasksResponse
      ] = await Promise.all([
        api.scheduler.getStatus(),
        api.scheduler.getOverview(),
        api.scheduler.getPlannedWork(),
        api.scheduler.getActiveTasks(),
        api.scheduler.getFailedTasks(),
        api.scheduler.getCompletedTasks()
      ])

      setStatus(statusResponse)
      setOverview(overviewResponse)
      setPlannedTasks(plannedWorkResponse.tasks)
      setActiveTasks(activeTasksResponse.tasks)
      // Filter out recently retried tasks from failed tasks to prevent them from reappearing
      const filteredFailedTasks = failedTasksResponse.tasks.filter(task => !recentlyRetriedTasksRef.current.has(task.id));
      setFailedTasks(filteredFailedTasks)
      setCompletedTasks(completedTasksResponse.tasks)
      setLastRefreshTime(new Date())
    } catch (err) {
      console.error('Failed to load scheduler data:', err)

      // Show user-friendly error message based on error type
      let errorMessage = 'Failed to load scheduler data. Please try refreshing the page.';

      if (err instanceof TypeError && err.message.includes('fetch')) {
        errorMessage = 'Network error: Unable to connect to the server. Please check your connection and try again.';
      } else if (err instanceof Error) {
        if (err.message.includes('401') || err.message.includes('unauthorized')) {
          errorMessage = 'Authentication error: Please log in again.';
        } else if (err.message.includes('403') || err.message.includes('forbidden')) {
          errorMessage = 'Permission error: You do not have access to view scheduler data.';
        } else if (err.message.includes('500')) {
          errorMessage = 'Server error: The scheduler service is temporarily unavailable. Please try again later.';
        }
      }

      toast({
        title: 'Data Loading Failed',
        description: errorMessage,
        variant: 'destructive',
      })
      setError('Failed to load scheduler data')
    } finally {
      setLoading(false)
      setRefreshing(false)
      loadingRef.current = false;
    }
  }, [toast])

  // Debounced refresh function for WebSocket events
  const debouncedRefresh = useCallback(() => {
    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      loadSchedulerData(false);
    }, 1000); // 1 second debounce
  }, []) // Remove loadSchedulerData dependency to prevent recreation

  // Manual refresh with cache bypass
  const handleRefresh = useCallback(async () => {
    try {
      setRefreshing(true)
      setError(null)

      // Load all data in parallel with cache bypass
      const [
        statusResponse,
        overviewResponse,
        plannedWorkResponse,
        activeTasksResponse,
        failedTasksResponse
      ] = await Promise.all([
        api.scheduler.refreshStatus(),
        api.scheduler.refreshOverview(),
        api.scheduler.refreshPlannedWork(),
        api.scheduler.refreshActiveTasks(),
        api.scheduler.refreshFailedTasks()
      ])

      setStatus(statusResponse)
      setOverview(overviewResponse)
      setPlannedTasks(plannedWorkResponse.tasks)
      setActiveTasks(activeTasksResponse.tasks)
      // Filter out recently retried tasks from failed tasks to prevent them from reappearing
      setFailedTasks(failedTasksResponse.tasks.filter(task => !recentlyRetriedTasksRef.current.has(task.id)))
      setLastRefreshTime(new Date())

      toast({
        title: 'Success',
        description: 'Dashboard data refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh dashboard data:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh dashboard data',
        variant: 'destructive',
      })
    } finally {
      setRefreshing(false)
    }
  }, [toast])

  // Refresh only active tasks
  const handleActiveTasksRefresh = useCallback(async () => {
    try {
      const activeTasksResponse = await api.scheduler.refreshActiveTasks()
      setActiveTasks(activeTasksResponse.tasks)
      setLastRefreshTime(new Date())
      toast({
        title: 'Success',
        description: 'Active tasks refreshed successfully',
      })
    } catch (err) {
      console.error('Failed to refresh active tasks:', err)
      toast({
        title: 'Error',
        description: 'Failed to refresh active tasks',
        variant: 'destructive',
      })
    }
  }, [toast])

  // Utility functions for task filtering
  const setFailedTasksFiltered = useCallback((filterFn: (tasks: FailedTask[]) => FailedTask[]) => {
    setFailedTasks(prev => filterFn(prev))
  }, [])

  const setPlannedTasksFiltered = useCallback((filterFn: (tasks: PlannedTask[]) => PlannedTask[]) => {
    setPlannedTasks(prev => filterFn(prev))
  }, [])

  const setActiveTasksFiltered = useCallback((filterFn: (tasks: ActiveTask[]) => ActiveTask[]) => {
    setActiveTasks(prev => filterFn(prev))
  }, [])

  // Initial load - only run once on mount
  useEffect(() => {
    loadSchedulerData(true)
  }, []) // Empty dependency array to run only once

  // Auto-refresh every 20 seconds for more responsive UI
  useEffect(() => {
    const interval = setInterval(() => {
      loadSchedulerData(false)
    }, 20000)

    return () => clearInterval(interval)
  }, []) // Remove loadSchedulerData dependency to prevent recreation

  // Additional faster refresh when there are active tasks to keep UI in sync
  useEffect(() => {
    if ((activeTasks?.length || 0) > 0) {
      const fastInterval = setInterval(() => {
        loadSchedulerData(false)
      }, 15000) // Refresh every 15 seconds when there are active tasks (reduced from 10s to prevent overlap)

      return () => clearInterval(fastInterval)
    }
  }, [activeTasks?.length]) // Remove loadSchedulerData dependency to prevent recreation

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    status,
    overview,
    plannedTasks,
    activeTasks,
    failedTasks,
    completedTasks,
    loading,
    refreshing,
    error,
    lastRefreshTime,

    // Actions
    loadSchedulerData,
    debouncedRefresh,
    handleRefresh,
    handleActiveTasksRefresh,

    // Utility functions
    setFailedTasksFiltered,
    setPlannedTasksFiltered,
    setActiveTasksFiltered
  }
}
