import React from "react"
import { Card, CardContent } from "@/components/ui/card"

interface StatisticsCardProps {
  title: string
  value: number
  icon: React.ReactNode
  color: string
}

export const StatisticsCard: React.FC<StatisticsCardProps> = ({ 
  title, 
  value, 
  icon, 
  color 
}) => (
  <Card className="h-16">
    <CardContent className="p-3">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600">{title}</p>
          <p className={`text-lg font-bold ${color}`}>{value}</p>
        </div>
        <div className={color}>
          {icon}
        </div>
      </div>
    </CardContent>
  </Card>
)

export default StatisticsCard
