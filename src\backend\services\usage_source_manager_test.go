package services

import (
	"os"
	"testing"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestUsageSourceManager(t *testing.T) (*UsageSourceManagerImpl, string, func()) {
	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "usage_source_manager_test")
	require.NoError(t, err)

	// Create manager
	manager := NewUsageSourceManager(tempDir)

	// Return cleanup function
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return manager, tempDir, cleanup
}

func createTestGitSource() models.UsageSource {
	return models.UsageSource{
		Name:          "Test Git Source",
		Type:          models.SourceTypeGit,
		IsActive:      true,
		ScanFrequency: 300,
		GitConfig: &models.GitSourceConfig{
			RepoURL:  "https://github.com/test/repo.git",
			Branch:   "main",
			AuthType: "token",
			Token:    "test-token",
		},
	}
}

func createTestApiSource() models.UsageSource {
	return models.UsageSource{
		Name:          "Test API Source",
		Type:          models.SourceTypeAPI,
		IsActive:      true,
		ScanFrequency: 600,
		ApiConfig: &models.ApiSourceConfig{
			Endpoint: "https://api.example.com/data",
			Method:   "GET",
			AuthType: "bearer",
			Token:    "test-token",
		},
	}
}

func TestUsageSourceManager_CreateSource(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source := createTestGitSource()

	// Create source
	created, err := manager.CreateSource(source)
	assert.NoError(t, err)

	// Verify ID and timestamps were set
	assert.NotEmpty(t, created.ID)
	assert.False(t, created.CreatedAt.IsZero())
	assert.False(t, created.UpdatedAt.IsZero())
	assert.Equal(t, created.CreatedAt, created.UpdatedAt)

	// Verify other fields
	assert.Equal(t, source.Name, created.Name)
	assert.Equal(t, source.Type, created.Type)
	assert.Equal(t, source.IsActive, created.IsActive)
	assert.Equal(t, source.ScanFrequency, created.ScanFrequency)
}

func TestUsageSourceManager_CreateSource_ValidationError(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	// Create invalid source (missing name)
	source := models.UsageSource{
		Type:          models.SourceTypeGit,
		ScanFrequency: 300,
		GitConfig: &models.GitSourceConfig{
			RepoURL: "https://github.com/test/repo.git",
		},
	}

	_, err := manager.CreateSource(source)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "validation failed")
}

func TestUsageSourceManager_GetSource(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source := createTestGitSource()

	// Create source
	created, err := manager.CreateSource(source)
	require.NoError(t, err)

	// Get source
	retrieved, err := manager.GetSource(created.ID)
	assert.NoError(t, err)
	assert.Equal(t, created.ID, retrieved.ID)
	assert.Equal(t, created.Name, retrieved.Name)
}

func TestUsageSourceManager_GetSource_NotFound(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	_, err := manager.GetSource("non-existent-id")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "source not found")
}

func TestUsageSourceManager_GetAllSources(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	// Initially empty
	sources, err := manager.GetAllSources()
	assert.NoError(t, err)
	assert.Empty(t, sources)

	// Create sources
	gitSource := createTestGitSource()
	apiSource := createTestApiSource()

	_, err = manager.CreateSource(gitSource)
	require.NoError(t, err)

	_, err = manager.CreateSource(apiSource)
	require.NoError(t, err)

	// Get all sources
	sources, err = manager.GetAllSources()
	assert.NoError(t, err)
	assert.Len(t, sources, 2)
}

func TestUsageSourceManager_GetActiveSourcesByType(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	// Create active git source
	gitSource := createTestGitSource()
	gitSource.IsActive = true
	_, err := manager.CreateSource(gitSource)
	require.NoError(t, err)

	// Create inactive git source
	inactiveGitSource := createTestGitSource()
	inactiveGitSource.Name = "Inactive Git Source"
	inactiveGitSource.IsActive = false
	_, err = manager.CreateSource(inactiveGitSource)
	require.NoError(t, err)

	// Create active API source
	apiSource := createTestApiSource()
	apiSource.IsActive = true
	_, err = manager.CreateSource(apiSource)
	require.NoError(t, err)

	// Get active git sources
	gitSources, err := manager.GetActiveSourcesByType(models.SourceTypeGit)
	assert.NoError(t, err)
	assert.Len(t, gitSources, 1)
	assert.Equal(t, "Test Git Source", gitSources[0].Name)

	// Get active API sources
	apiSources, err := manager.GetActiveSourcesByType(models.SourceTypeAPI)
	assert.NoError(t, err)
	assert.Len(t, apiSources, 1)
	assert.Equal(t, "Test API Source", apiSources[0].Name)

	// Get active file sources (should be empty)
	fileSources, err := manager.GetActiveSourcesByType(models.SourceTypeFile)
	assert.NoError(t, err)
	assert.Empty(t, fileSources)
}

func TestUsageSourceManager_UpdateSource(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source := createTestGitSource()

	// Create source
	created, err := manager.CreateSource(source)
	require.NoError(t, err)

	// Wait a bit to ensure different timestamp
	time.Sleep(time.Millisecond * 10)

	// Update source
	created.Name = "Updated Git Source"
	created.ScanFrequency = 900

	updated, err := manager.UpdateSource(created)
	assert.NoError(t, err)

	// Verify updates
	assert.Equal(t, "Updated Git Source", updated.Name)
	assert.Equal(t, 900, updated.ScanFrequency)
	assert.Equal(t, created.CreatedAt, updated.CreatedAt)      // CreatedAt should be preserved
	assert.True(t, updated.UpdatedAt.After(updated.CreatedAt)) // UpdatedAt should be newer
}

func TestUsageSourceManager_UpdateSource_NotFound(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source := createTestGitSource()
	source.ID = "non-existent-id"

	_, err := manager.UpdateSource(source)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "source not found")
}

func TestUsageSourceManager_DeleteSource(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source := createTestGitSource()

	// Create source
	created, err := manager.CreateSource(source)
	require.NoError(t, err)

	// Delete source
	err = manager.DeleteSource(created.ID)
	assert.NoError(t, err)

	// Verify source is deleted
	_, err = manager.GetSource(created.ID)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "source not found")
}

func TestUsageSourceManager_DeleteSource_NotFound(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	err := manager.DeleteSource("non-existent-id")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "source not found")
}

func TestUsageSourceManager_ValidateSource_DuplicateName(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source1 := createTestGitSource()
	source2 := createTestApiSource()
	source2.Name = source1.Name // Same name

	// Create first source
	_, err := manager.CreateSource(source1)
	require.NoError(t, err)

	// Try to create second source with same name
	_, err = manager.CreateSource(source2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "source name already exists")
}

func TestUsageSourceManager_LoadSources_FileNotExists(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	// LoadSources should not error when file doesn't exist
	err := manager.LoadSources()
	assert.NoError(t, err)

	sources, err := manager.GetAllSources()
	assert.NoError(t, err)
	assert.Empty(t, sources)
}

func TestUsageSourceManager_SaveAndLoadSources(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "usage_source_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create manager and add sources
	manager1 := NewUsageSourceManager(tempDir)

	gitSource := createTestGitSource()
	apiSource := createTestApiSource()

	created1, err := manager1.CreateSource(gitSource)
	require.NoError(t, err)

	created2, err := manager1.CreateSource(apiSource)
	require.NoError(t, err)

	// Create new manager instance (simulating restart)
	manager2 := NewUsageSourceManager(tempDir)

	// Verify sources were loaded
	sources, err := manager2.GetAllSources()
	assert.NoError(t, err)
	assert.Len(t, sources, 2)

	// Verify source details
	source1, err := manager2.GetSource(created1.ID)
	assert.NoError(t, err)
	assert.Equal(t, created1.Name, source1.Name)
	assert.Equal(t, created1.Type, source1.Type)

	source2, err := manager2.GetSource(created2.ID)
	assert.NoError(t, err)
	assert.Equal(t, created2.Name, source2.Name)
	assert.Equal(t, created2.Type, source2.Type)
}

func TestUsageSourceManager_TestSourceConnection(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	source := createTestGitSource()

	// Create source
	created, err := manager.CreateSource(source)
	require.NoError(t, err)

	// Test connection (currently just validates configuration)
	err = manager.TestSourceConnection(created.ID)
	assert.NoError(t, err)
}

func TestUsageSourceManager_TestSourceConnection_NotFound(t *testing.T) {
	manager, _, cleanup := setupTestUsageSourceManager(t)
	defer cleanup()

	err := manager.TestSourceConnection("non-existent-id")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "source not found")
}
