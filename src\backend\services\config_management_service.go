package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	"github.com/google/uuid"
)

// ConfigManagementService provides comprehensive configuration management
type ConfigManagementService struct {
	configsDir      string
	backupsDir      string
	versionsDir     string
	mutex           sync.RWMutex
	vectorDB        *VectorDatabaseService
	maxBackups      int
	maxVersions     int
	validationRules map[string]ConfigValidationRule
}

// ConfigVersion represents a versioned configuration
type ConfigVersion struct {
	ID          string                 `json:"id"`
	ConfigType  string                 `json:"configType"`
	ConfigID    string                 `json:"configId"`
	Version     int                    `json:"version"`
	Data        map[string]interface{} `json:"data"`
	Hash        string                 `json:"hash"`
	CreatedAt   time.Time              `json:"createdAt"`
	CreatedBy   string                 `json:"createdBy"`
	Description string                 `json:"description"`
	Tags        []string               `json:"tags,omitempty"`
	IsActive    bool                   `json:"isActive"`
}

// ConfigBackup represents a configuration backup
type ConfigBackup struct {
	ID          string                            `json:"id"`
	BackupType  string                            `json:"backupType"` // "manual", "automatic", "scheduled"
	Timestamp   time.Time                         `json:"timestamp"`
	Description string                            `json:"description"`
	Configs     map[string]map[string]interface{} `json:"configs"` // configType -> configId -> data
	Hash        string                            `json:"hash"`
	Size        int64                             `json:"size"`
	CreatedBy   string                            `json:"createdBy"`
}

// ConfigValidationRule defines validation rules for configuration types
type ConfigValidationRule struct {
	ConfigType     string                                                `json:"configType"`
	RequiredFields []string                                              `json:"requiredFields"`
	Validator      func(config map[string]interface{}) []ValidationError `json:"-"`
	Schema         map[string]interface{}                                `json:"schema,omitempty"`
}

// ValidationError represents a configuration validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// ConfigStats represents configuration management statistics
type ConfigStats struct {
	TotalConfigs     int                          `json:"totalConfigs"`
	ConfigsByType    map[string]int               `json:"configsByType"`
	TotalVersions    int                          `json:"totalVersions"`
	TotalBackups     int                          `json:"totalBackups"`
	LastBackup       *time.Time                   `json:"lastBackup,omitempty"`
	BackupSize       int64                        `json:"backupSize"`
	ValidationErrors map[string][]ValidationError `json:"validationErrors,omitempty"`
}

// NewConfigManagementService creates a new configuration management service
func NewConfigManagementService(dataDir string) *ConfigManagementService {
	configsDir := filepath.Join(dataDir, "configs")
	backupsDir := filepath.Join(dataDir, "config-backups")
	versionsDir := filepath.Join(dataDir, "config-versions")

	// Ensure directories exist
	for _, dir := range []string{configsDir, backupsDir, versionsDir} {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Printf("Warning: Failed to create directory %s: %v", dir, err)
		}
	}

	service := &ConfigManagementService{
		configsDir:      configsDir,
		backupsDir:      backupsDir,
		versionsDir:     versionsDir,
		maxBackups:      50,  // Keep up to 50 backups
		maxVersions:     100, // Keep up to 100 versions per config
		validationRules: make(map[string]ConfigValidationRule),
	}

	// Initialize default validation rules
	service.initializeValidationRules()

	log.Printf("Configuration management service initialized with directories: configs=%s, backups=%s, versions=%s",
		configsDir, backupsDir, versionsDir)

	return service
}

// SetVectorDatabase sets the vector database for storing config metrics
func (c *ConfigManagementService) SetVectorDatabase(vectorDB *VectorDatabaseService) {
	c.vectorDB = vectorDB
}

// SaveConfig saves a configuration with versioning and validation
func (c *ConfigManagementService) SaveConfig(configType, configID string, data map[string]interface{}, createdBy, description string) (*ConfigVersion, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate configuration
	if errors := c.validateConfig(configType, data); len(errors) > 0 {
		return nil, fmt.Errorf("validation failed: %v", errors)
	}

	// Calculate hash for change detection
	hash, err := c.calculateHash(data)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate config hash: %w", err)
	}

	// Check if this is a new version (different hash)
	currentVersion, _ := c.getLatestVersion(configType, configID)
	if currentVersion != nil && currentVersion.Hash == hash {
		log.Printf("Configuration %s/%s unchanged, skipping version creation", configType, configID)
		return currentVersion, nil
	}

	// Create new version
	version := &ConfigVersion{
		ID:          uuid.New().String(),
		ConfigType:  configType,
		ConfigID:    configID,
		Version:     c.getNextVersionNumber(configType, configID),
		Data:        data,
		Hash:        hash,
		CreatedAt:   time.Now(),
		CreatedBy:   createdBy,
		Description: description,
		IsActive:    true,
	}

	// Deactivate previous version
	if currentVersion != nil {
		currentVersion.IsActive = false
		c.saveVersion(currentVersion)
	}

	// Save new version
	if err := c.saveVersion(version); err != nil {
		return nil, fmt.Errorf("failed to save version: %w", err)
	}

	// Save current config
	if err := c.saveCurrentConfig(configType, configID, data); err != nil {
		return nil, fmt.Errorf("failed to save current config: %w", err)
	}

	// Clean up old versions
	c.cleanupOldVersions(configType, configID)

	// Store metrics in vector database
	if c.vectorDB != nil {
		ctx := context.Background()
		err := c.vectorDB.StoreConfigChange(ctx, configType, configID, version.Version, createdBy, description)
		if err != nil {
			log.Printf("Warning: Failed to store config change in vector database: %v", err)
		}
	}

	log.Printf("Saved configuration %s/%s version %d", configType, configID, version.Version)
	return version, nil
}

// GetConfig retrieves the current configuration
func (c *ConfigManagementService) GetConfig(configType, configID string) (map[string]interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	configPath := filepath.Join(c.configsDir, configType, fmt.Sprintf("%s.json", configID))

	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("configuration %s/%s not found", configType, configID)
		}
		return nil, fmt.Errorf("failed to read config: %w", err)
	}

	var config map[string]interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config: %w", err)
	}

	return config, nil
}

// GetConfigVersion retrieves a specific version of a configuration
func (c *ConfigManagementService) GetConfigVersion(configType, configID string, version int) (*ConfigVersion, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	versionPath := filepath.Join(c.versionsDir, configType, configID, fmt.Sprintf("v%d.json", version))

	data, err := ioutil.ReadFile(versionPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("version %d of configuration %s/%s not found", version, configType, configID)
		}
		return nil, fmt.Errorf("failed to read version: %w", err)
	}

	var configVersion ConfigVersion
	if err := json.Unmarshal(data, &configVersion); err != nil {
		return nil, fmt.Errorf("failed to parse version: %w", err)
	}

	return &configVersion, nil
}

// ListConfigVersions lists all versions of a configuration
func (c *ConfigManagementService) ListConfigVersions(configType, configID string) ([]*ConfigVersion, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	versionsPath := filepath.Join(c.versionsDir, configType, configID)

	files, err := ioutil.ReadDir(versionsPath)
	if err != nil {
		if os.IsNotExist(err) {
			return []*ConfigVersion{}, nil
		}
		return nil, fmt.Errorf("failed to read versions directory: %w", err)
	}

	var versions []*ConfigVersion
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			versionPath := filepath.Join(versionsPath, file.Name())
			data, err := ioutil.ReadFile(versionPath)
			if err != nil {
				log.Printf("Warning: Failed to read version file %s: %v", versionPath, err)
				continue
			}

			var version ConfigVersion
			if err := json.Unmarshal(data, &version); err != nil {
				log.Printf("Warning: Failed to parse version file %s: %v", versionPath, err)
				continue
			}

			versions = append(versions, &version)
		}
	}

	// Sort versions by version number (descending)
	sort.Slice(versions, func(i, j int) bool {
		return versions[i].Version > versions[j].Version
	})

	return versions, nil
}

// CreateBackup creates a backup of all configurations
func (c *ConfigManagementService) CreateBackup(backupType, description, createdBy string) (*ConfigBackup, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	backup := &ConfigBackup{
		ID:          uuid.New().String(),
		BackupType:  backupType,
		Timestamp:   time.Now(),
		Description: description,
		CreatedBy:   createdBy,
		Configs:     make(map[string]map[string]interface{}),
	}

	// Collect all configurations
	configTypes, err := c.getConfigTypes()
	if err != nil {
		return nil, fmt.Errorf("failed to get config types: %w", err)
	}

	for _, configType := range configTypes {
		backup.Configs[configType] = make(map[string]interface{})

		configIDs, err := c.getConfigIDs(configType)
		if err != nil {
			log.Printf("Warning: Failed to get config IDs for type %s: %v", configType, err)
			continue
		}

		for _, configID := range configIDs {
			config, err := c.GetConfig(configType, configID)
			if err != nil {
				log.Printf("Warning: Failed to get config %s/%s: %v", configType, configID, err)
				continue
			}
			backup.Configs[configType][configID] = config
		}
	}

	// Calculate backup hash and size
	backupData, err := json.Marshal(backup.Configs)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal backup data: %w", err)
	}

	backup.Hash = c.calculateDataHash(backupData)
	backup.Size = int64(len(backupData))

	// Save backup
	if err := c.saveBackup(backup); err != nil {
		return nil, fmt.Errorf("failed to save backup: %w", err)
	}

	// Clean up old backups
	c.cleanupOldBackups()

	log.Printf("Created backup %s (%s) with %d config types", backup.ID, backup.BackupType, len(backup.Configs))
	return backup, nil
}

// RestoreFromBackup restores configurations from a backup
func (c *ConfigManagementService) RestoreFromBackup(backupID, restoredBy string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	backup, err := c.getBackup(backupID)
	if err != nil {
		return fmt.Errorf("failed to get backup: %w", err)
	}

	// Create a backup of current state before restore
	_, err = c.CreateBackup("pre-restore", fmt.Sprintf("Backup before restoring from %s", backupID), restoredBy)
	if err != nil {
		log.Printf("Warning: Failed to create pre-restore backup: %v", err)
	}

	// Restore configurations
	for configType, configs := range backup.Configs {
		for configID, configData := range configs {
			if configMap, ok := configData.(map[string]interface{}); ok {
				_, err := c.SaveConfig(configType, configID, configMap, restoredBy, fmt.Sprintf("Restored from backup %s", backupID))
				if err != nil {
					log.Printf("Warning: Failed to restore config %s/%s: %v", configType, configID, err)
				}
			}
		}
	}

	log.Printf("Restored configurations from backup %s", backupID)
	return nil
}

// ValidateAllConfigs validates all current configurations
func (c *ConfigManagementService) ValidateAllConfigs() (map[string][]ValidationError, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	allErrors := make(map[string][]ValidationError)

	configTypes, err := c.getConfigTypes()
	if err != nil {
		return nil, fmt.Errorf("failed to get config types: %w", err)
	}

	for _, configType := range configTypes {
		configIDs, err := c.getConfigIDs(configType)
		if err != nil {
			log.Printf("Warning: Failed to get config IDs for type %s: %v", configType, err)
			continue
		}

		for _, configID := range configIDs {
			config, err := c.GetConfig(configType, configID)
			if err != nil {
				log.Printf("Warning: Failed to get config %s/%s: %v", configType, configID, err)
				continue
			}

			if errors := c.validateConfig(configType, config); len(errors) > 0 {
				key := fmt.Sprintf("%s/%s", configType, configID)
				allErrors[key] = errors
			}
		}
	}

	return allErrors, nil
}

// GetStats returns configuration management statistics
func (c *ConfigManagementService) GetStats() (*ConfigStats, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	stats := &ConfigStats{
		ConfigsByType: make(map[string]int),
	}

	// Count configurations by type
	configTypes, err := c.getConfigTypes()
	if err != nil {
		return nil, fmt.Errorf("failed to get config types: %w", err)
	}

	for _, configType := range configTypes {
		configIDs, err := c.getConfigIDs(configType)
		if err != nil {
			log.Printf("Warning: Failed to get config IDs for type %s: %v", configType, err)
			continue
		}
		stats.ConfigsByType[configType] = len(configIDs)
		stats.TotalConfigs += len(configIDs)
	}

	// Count versions
	stats.TotalVersions = c.countAllVersions()

	// Count backups and get backup info
	backups, err := c.listBackups()
	if err != nil {
		log.Printf("Warning: Failed to list backups: %v", err)
	} else {
		stats.TotalBackups = len(backups)
		if len(backups) > 0 {
			// Get latest backup info
			latest := backups[0]
			stats.LastBackup = &latest.Timestamp

			// Calculate total backup size
			for _, backup := range backups {
				stats.BackupSize += backup.Size
			}
		}
	}

	// Get validation errors
	validationErrors, err := c.ValidateAllConfigs()
	if err != nil {
		log.Printf("Warning: Failed to validate configs: %v", err)
	} else {
		stats.ValidationErrors = validationErrors
	}

	return stats, nil
}

// Helper methods

// initializeValidationRules sets up default validation rules
func (c *ConfigManagementService) initializeValidationRules() {
	// Repository configuration validation
	c.validationRules["repository"] = ConfigValidationRule{
		ConfigType:     "repository",
		RequiredFields: []string{"id", "name", "type", "url"},
		Validator: func(config map[string]interface{}) []ValidationError {
			var errors []ValidationError

			if id, ok := config["id"].(string); !ok || id == "" {
				errors = append(errors, ValidationError{Field: "id", Message: "ID is required", Code: "REQUIRED"})
			}

			if name, ok := config["name"].(string); !ok || name == "" {
				errors = append(errors, ValidationError{Field: "name", Message: "Name is required", Code: "REQUIRED"})
			}

			if repoType, ok := config["type"].(string); !ok || (repoType != "gitlab" && repoType != "bitbucket") {
				errors = append(errors, ValidationError{Field: "type", Message: "Type must be 'gitlab' or 'bitbucket'", Code: "INVALID_VALUE"})
			}

			if url, ok := config["url"].(string); !ok || url == "" {
				errors = append(errors, ValidationError{Field: "url", Message: "URL is required", Code: "REQUIRED"})
			}

			return errors
		},
	}

	// Usage source configuration validation
	c.validationRules["usage_source"] = ConfigValidationRule{
		ConfigType:     "usage_source",
		RequiredFields: []string{"id", "name", "type"},
		Validator: func(config map[string]interface{}) []ValidationError {
			var errors []ValidationError

			if id, ok := config["id"].(string); !ok || id == "" {
				errors = append(errors, ValidationError{Field: "id", Message: "ID is required", Code: "REQUIRED"})
			}

			if name, ok := config["name"].(string); !ok || name == "" {
				errors = append(errors, ValidationError{Field: "name", Message: "Name is required", Code: "REQUIRED"})
			}

			if sourceType, ok := config["type"].(string); !ok || (sourceType != "git" && sourceType != "api" && sourceType != "file") {
				errors = append(errors, ValidationError{Field: "type", Message: "Type must be 'git', 'api', or 'file'", Code: "INVALID_VALUE"})
			}

			return errors
		},
	}

	// Auto-scan configuration validation
	c.validationRules["auto_scan"] = ConfigValidationRule{
		ConfigType:     "auto_scan",
		RequiredFields: []string{"repoId", "frequency"},
		Validator: func(config map[string]interface{}) []ValidationError {
			var errors []ValidationError

			if repoId, ok := config["repoId"].(string); !ok || repoId == "" {
				errors = append(errors, ValidationError{Field: "repoId", Message: "Repository ID is required", Code: "REQUIRED"})
			}

			if frequency, ok := config["frequency"].(string); !ok || (frequency != "daily" && frequency != "weekly" && frequency != "monthly") {
				errors = append(errors, ValidationError{Field: "frequency", Message: "Frequency must be 'daily', 'weekly', or 'monthly'", Code: "INVALID_VALUE"})
			}

			return errors
		},
	}
}

// validateConfig validates a configuration against its rules
func (c *ConfigManagementService) validateConfig(configType string, config map[string]interface{}) []ValidationError {
	rule, exists := c.validationRules[configType]
	if !exists {
		return []ValidationError{} // No validation rules defined
	}

	if rule.Validator != nil {
		return rule.Validator(config)
	}

	return []ValidationError{}
}

// calculateHash calculates SHA256 hash of configuration data
func (c *ConfigManagementService) calculateHash(data map[string]interface{}) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return c.calculateDataHash(jsonData), nil
}

// calculateDataHash calculates SHA256 hash of byte data
func (c *ConfigManagementService) calculateDataHash(data []byte) string {
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// getLatestVersion gets the latest version of a configuration
func (c *ConfigManagementService) getLatestVersion(configType, configID string) (*ConfigVersion, error) {
	versions, err := c.ListConfigVersions(configType, configID)
	if err != nil {
		return nil, err
	}

	if len(versions) == 0 {
		return nil, nil
	}

	return versions[0], nil // Already sorted by version number descending
}

// getNextVersionNumber gets the next version number for a configuration
func (c *ConfigManagementService) getNextVersionNumber(configType, configID string) int {
	versions, err := c.ListConfigVersions(configType, configID)
	if err != nil || len(versions) == 0 {
		return 1
	}

	return versions[0].Version + 1
}

// saveVersion saves a configuration version to disk
func (c *ConfigManagementService) saveVersion(version *ConfigVersion) error {
	versionDir := filepath.Join(c.versionsDir, version.ConfigType, version.ConfigID)
	if err := os.MkdirAll(versionDir, 0755); err != nil {
		return fmt.Errorf("failed to create version directory: %w", err)
	}

	versionPath := filepath.Join(versionDir, fmt.Sprintf("v%d.json", version.Version))

	data, err := json.MarshalIndent(version, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal version: %w", err)
	}

	return ioutil.WriteFile(versionPath, data, 0644)
}

// saveCurrentConfig saves the current configuration to disk
func (c *ConfigManagementService) saveCurrentConfig(configType, configID string, data map[string]interface{}) error {
	configDir := filepath.Join(c.configsDir, configType)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	configPath := filepath.Join(configDir, fmt.Sprintf("%s.json", configID))

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	return ioutil.WriteFile(configPath, jsonData, 0644)
}

// cleanupOldVersions removes old versions beyond the limit
func (c *ConfigManagementService) cleanupOldVersions(configType, configID string) {
	versions, err := c.ListConfigVersions(configType, configID)
	if err != nil || len(versions) <= c.maxVersions {
		return
	}

	// Remove versions beyond the limit
	for i := c.maxVersions; i < len(versions); i++ {
		versionPath := filepath.Join(c.versionsDir, configType, configID, fmt.Sprintf("v%d.json", versions[i].Version))
		if err := os.Remove(versionPath); err != nil {
			log.Printf("Warning: Failed to remove old version %s: %v", versionPath, err)
		}
	}
}

// saveBackup saves a backup to disk
func (c *ConfigManagementService) saveBackup(backup *ConfigBackup) error {
	backupPath := filepath.Join(c.backupsDir, fmt.Sprintf("%s.json", backup.ID))

	data, err := json.MarshalIndent(backup, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal backup: %w", err)
	}

	return ioutil.WriteFile(backupPath, data, 0644)
}

// getBackup retrieves a backup by ID
func (c *ConfigManagementService) getBackup(backupID string) (*ConfigBackup, error) {
	backupPath := filepath.Join(c.backupsDir, fmt.Sprintf("%s.json", backupID))

	data, err := ioutil.ReadFile(backupPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read backup: %w", err)
	}

	var backup ConfigBackup
	if err := json.Unmarshal(data, &backup); err != nil {
		return nil, fmt.Errorf("failed to parse backup: %w", err)
	}

	return &backup, nil
}

// listBackups lists all backups sorted by timestamp (newest first)
func (c *ConfigManagementService) listBackups() ([]*ConfigBackup, error) {
	files, err := ioutil.ReadDir(c.backupsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read backups directory: %w", err)
	}

	var backups []*ConfigBackup
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			backupPath := filepath.Join(c.backupsDir, file.Name())
			data, err := ioutil.ReadFile(backupPath)
			if err != nil {
				log.Printf("Warning: Failed to read backup file %s: %v", backupPath, err)
				continue
			}

			var backup ConfigBackup
			if err := json.Unmarshal(data, &backup); err != nil {
				log.Printf("Warning: Failed to parse backup file %s: %v", backupPath, err)
				continue
			}

			backups = append(backups, &backup)
		}
	}

	// Sort by timestamp (newest first)
	sort.Slice(backups, func(i, j int) bool {
		return backups[i].Timestamp.After(backups[j].Timestamp)
	})

	return backups, nil
}

// cleanupOldBackups removes old backups beyond the limit
func (c *ConfigManagementService) cleanupOldBackups() {
	backups, err := c.listBackups()
	if err != nil || len(backups) <= c.maxBackups {
		return
	}

	// Remove backups beyond the limit
	for i := c.maxBackups; i < len(backups); i++ {
		backupPath := filepath.Join(c.backupsDir, fmt.Sprintf("%s.json", backups[i].ID))
		if err := os.Remove(backupPath); err != nil {
			log.Printf("Warning: Failed to remove old backup %s: %v", backupPath, err)
		}
	}
}

// getConfigTypes returns all configuration types
func (c *ConfigManagementService) getConfigTypes() ([]string, error) {
	files, err := ioutil.ReadDir(c.configsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read configs directory: %w", err)
	}

	var types []string
	for _, file := range files {
		if file.IsDir() {
			types = append(types, file.Name())
		}
	}

	return types, nil
}

// getConfigIDs returns all configuration IDs for a type
func (c *ConfigManagementService) getConfigIDs(configType string) ([]string, error) {
	configTypeDir := filepath.Join(c.configsDir, configType)
	files, err := ioutil.ReadDir(configTypeDir)
	if err != nil {
		if os.IsNotExist(err) {
			return []string{}, nil
		}
		return nil, fmt.Errorf("failed to read config type directory: %w", err)
	}

	var ids []string
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			// Remove .json extension to get config ID
			id := file.Name()[:len(file.Name())-5]
			ids = append(ids, id)
		}
	}

	return ids, nil
}

// countAllVersions counts total versions across all configurations
func (c *ConfigManagementService) countAllVersions() int {
	count := 0

	configTypes, err := c.getConfigTypes()
	if err != nil {
		return 0
	}

	for _, configType := range configTypes {
		configIDs, err := c.getConfigIDs(configType)
		if err != nil {
			continue
		}

		for _, configID := range configIDs {
			versions, err := c.ListConfigVersions(configType, configID)
			if err != nil {
				continue
			}
			count += len(versions)
		}
	}

	return count
}
