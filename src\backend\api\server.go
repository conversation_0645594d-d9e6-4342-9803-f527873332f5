package api

import (
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Server represents the API server
type Server struct {
	router      *gin.Engine
	controllers []Controller
	port        string
	staticDir   string
	wsHub       WebSocketHub // WebSocket hub for real-time updates
}

// WebSocketHub interface for WebSocket functionality
type WebSocketHub interface {
	HandleWebSocket(w http.ResponseWriter, r *http.Request)
}

// Controller interface for registering routes
type Controller interface {
	RegisterRoutes(router *gin.RouterGroup)
}

// NewServer creates a new API server
func NewServer(port string) *Server {
	router := gin.Default()

	// Configure CORS
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Add client disconnect middleware to detect cancelled requests
	router.Use(CancellationMiddleware())

	return &Server{
		router:      router,
		controllers: make([]Controller, 0),
		port:        port,
		staticDir:   "static", // Default static directory
	}
}

// AddController adds a controller to the server
func (s *Server) AddController(controller Controller) {
	s.controllers = append(s.controllers, controller)
}

// SetStaticDir sets the directory for static files
func (s *Server) SetStaticDir(dir string) {
	s.staticDir = dir
}

// SetWebSocketHub sets the WebSocket hub for real-time updates
func (s *Server) SetWebSocketHub(hub WebSocketHub) {
	s.wsHub = hub
}

// Start starts the API server
func (s *Server) Start() error {
	// Register API routes
	apiGroup := s.router.Group("/api")

	log.Printf("Registering %d controllers", len(s.controllers))
	for i, controller := range s.controllers {
		log.Printf("Registering controller %d: %T", i, controller)
		controller.RegisterRoutes(apiGroup)
		log.Printf("Controller %d registered: %T", i, controller)
	}

	// Add a health check endpoint
	s.router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "healthy"})
	})

	// Add WebSocket endpoint for real-time progress updates
	if s.wsHub != nil {
		s.router.GET("/ws/progress", func(c *gin.Context) {
			s.wsHub.HandleWebSocket(c.Writer, c.Request)
		})
		log.Printf("WebSocket endpoint registered at /ws/progress")
	}

	// Serve static files if the directory exists
	if _, err := os.Stat(s.staticDir); !os.IsNotExist(err) {
		// Serve static files for the frontend
		s.router.StaticFS("/assets", http.Dir(filepath.Join(s.staticDir, "assets")))

		// Serve index.html for all other routes (SPA fallback)
		s.router.NoRoute(func(c *gin.Context) {
			// API routes should 404 normally
			if len(c.Request.URL.Path) >= 4 && c.Request.URL.Path[:4] == "/api" {
				c.AbortWithStatus(http.StatusNotFound)
				return
			}

			// Serve the index.html for all other routes to support SPA routing
			indexPath := filepath.Join(s.staticDir, "index.html")
			if _, err := os.Stat(indexPath); !os.IsNotExist(err) {
				c.File(indexPath)
			} else {
				c.String(http.StatusNotFound, "Frontend not found. Did you build the UI?")
			}
		})

		log.Printf("Serving frontend from %s", s.staticDir)
	} else {
		log.Printf("Static directory %s not found. API-only mode.", s.staticDir)
	}

	// Start the server
	log.Printf("Starting API server on port %s", s.port)
	return s.router.Run(":" + s.port)
}
