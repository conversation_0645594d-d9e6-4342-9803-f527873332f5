package services

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
)

// UsageScanner defines the interface for scanning sources for group usage
type UsageScanner interface {
	// Scan for group usage across all active sources
	ScanGroupUsage(ctx context.Context, request models.UsageScanRequest) (string, error) // Returns scan ID

	// Get scan status for a group
	GetScanStatus(groupName, repoID string) (models.UsageScanStatus, error)

	// Get usage results for a group
	GetUsageResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error)

	// Cancel ongoing scan
	CancelScan(groupName, repoID string) error

	// Clear usage results for a group
	ClearUsageResults(groupName, repoID string) error

	// Clear failed status for a group (removes from failed tasks list)
	ClearFailedStatus(groupName, repoID string) error

	// Get usage statistics
	GetUsageStatistics(repoID string) (models.UsageStatistics, error)

	// Get all active scans for scheduler dashboard
	GetActiveScans() ([]models.ActiveTask, error)

	// Get failed scans for scheduler dashboard
	GetFailedScans() ([]models.UsageScanStatus, error)

	// Get interrupted scans for scheduler dashboard
	GetInterruptedScans() ([]models.UsageScanStatus, error)

	// Clean up stale tasks (for manual cleanup)
	CleanupStaleTasks() (int, error)

	// Get completed scans for scheduler dashboard
	GetCompletedScans() ([]models.UsageScanStatus, error)
}

// UsageScannerService manages usage scanning
type UsageScannerService struct {
	sourceManager       UsageSourceManager
	handlerRegistry     *SourceHandlerRegistry
	statusManager       *ScanStatusManager
	workerPool          *ScanWorkerPool
	scanLogger          *ScanLogger
	cancellationManager *TaskCancellationManager
	scheduledScans      map[string]time.Time // Map of groupName:repoID to next scan time
	scheduleMutex       sync.RWMutex
	isRunning           bool
	ticker              *time.Ticker
	done                chan struct{}
	scanInterval        time.Duration // Default scan interval
	maxConcurrentScans  int           // Maximum concurrent scans
	dataDir             string
	orphanCheckTicker   *time.Ticker
	stopOrphanCheck     chan struct{}

	// Task synchronization
	scanToTaskMap map[string]string // Map of scanID to taskID for completion tracking
	taskMapMutex  sync.RWMutex

	// Vector database integration
	vectorDB *VectorDatabaseService

	// Authentication cache integration
	authCache *AuthCacheService

	// Progress broadcasting for real-time updates
	progressBroadcaster *ProgressBroadcaster
}

// NewUsageScannerService creates a new usage scanner service
func NewUsageScannerService(sourceManager UsageSourceManager, dataDir string, scanLogger *ScanLogger) *UsageScannerService {
	registry := NewSourceHandlerRegistry()
	statusManager := NewScanStatusManager(dataDir)
	workerPool := NewScanWorkerPool(5) // 5 concurrent workers
	cancellationManager := NewTaskCancellationManager()

	// Set the status manager and scan logger on the worker pool
	workerPool.SetStatusManager(statusManager)
	workerPool.SetScanLogger(scanLogger)

	service := &UsageScannerService{
		sourceManager:       sourceManager,
		handlerRegistry:     registry,
		statusManager:       statusManager,
		workerPool:          workerPool,
		scanLogger:          scanLogger,
		cancellationManager: cancellationManager,
		scheduledScans:      make(map[string]time.Time),
		done:                make(chan struct{}),
		scanInterval:        time.Hour * 6, // Default: scan every 6 hours
		maxConcurrentScans:  10,
		dataDir:             dataDir,
		stopOrphanCheck:     make(chan struct{}),
		scanToTaskMap:       make(map[string]string),
	}

	// Set up scan completion callback
	workerPool.SetOnScanComplete(service.handleScanCompletion)

	return service
}

// Start begins the scanner service
func (s *UsageScannerService) Start() error {
	if s.isRunning {
		log.Println("Usage scanner service is already running")
		return nil
	}

	// Start worker pool
	s.workerPool.Start()

	// Check for scheduled scans every 5 minutes
	s.ticker = time.NewTicker(5 * time.Minute)

	// Start orphan scan checker every 10 minutes
	s.orphanCheckTicker = time.NewTicker(10 * time.Minute)

	s.isRunning = true

	go s.run()
	go s.orphanCheckLoop()
	log.Println("Usage scanner service started")
	return nil
}

// Stop stops the scanner service
func (s *UsageScannerService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false

	// Cancel all active tasks before stopping
	if s.cancellationManager != nil {
		log.Println("Cancelling all active usage scan tasks...")
		s.cancellationManager.CancelAllTasks()
	}

	close(s.done)
	close(s.stopOrphanCheck)
	s.ticker.Stop()
	if s.orphanCheckTicker != nil {
		s.orphanCheckTicker.Stop()
	}
	s.workerPool.Stop()
	log.Println("Usage scanner service stopped")
}

// run is the main loop for the scanner
func (s *UsageScannerService) run() {
	// Run once immediately to catch up on any missed schedules
	s.checkSchedules()

	for {
		select {
		case <-s.done:
			return
		case <-s.ticker.C:
			if !s.isRunning {
				return
			}
			s.checkSchedules()
		}
	}
}

// checkSchedules checks for scheduled scans
func (s *UsageScannerService) checkSchedules() {
	// Get all active sources
	sources, err := s.sourceManager.GetAllSources()
	if err != nil {
		log.Printf("Error getting usage sources: %v", err)
		return
	}

	now := time.Now()

	for _, source := range sources {
		if !source.IsActive {
			continue
		}

		// Check if it's time to scan this source
		scanKey := fmt.Sprintf("source:%s", source.ID)
		s.scheduleMutex.RLock()
		nextScan, exists := s.scheduledScans[scanKey]
		s.scheduleMutex.RUnlock()

		if !exists {
			// First time scheduling this source
			nextScan = now.Add(time.Duration(source.ScanFrequency) * time.Second)
			s.scheduleMutex.Lock()
			s.scheduledScans[scanKey] = nextScan
			s.scheduleMutex.Unlock()
			log.Printf("Scheduled first scan for source %s at %s", source.Name, nextScan.Format(time.RFC3339))
			continue
		}

		if now.After(nextScan) || now.Equal(nextScan) {
			log.Printf("Triggering scheduled scan for source %s", source.Name)

			// TODO: Get all groups from all repositories and scan them
			// For now, we'll implement manual scanning only

			// Schedule next scan
			nextScan = now.Add(time.Duration(source.ScanFrequency) * time.Second)
			s.scheduleMutex.Lock()
			s.scheduledScans[scanKey] = nextScan
			s.scheduleMutex.Unlock()
		}
	}
}

// SetVectorDatabase sets the vector database service for scan history tracking
func (s *UsageScannerService) SetVectorDatabase(vectorDB *VectorDatabaseService) {
	s.vectorDB = vectorDB
}

// SetAuthCache sets the authentication cache service
func (s *UsageScannerService) SetAuthCache(authCache *AuthCacheService) {
	s.authCache = authCache
	// Also set it on the worker pool so handlers can use it
	if s.workerPool != nil {
		s.workerPool.SetAuthCache(authCache)
	}
}

// SetProgressBroadcaster sets the progress broadcaster for real-time updates
func (s *UsageScannerService) SetProgressBroadcaster(broadcaster *ProgressBroadcaster) {
	s.progressBroadcaster = broadcaster
}

// ScanGroupUsage initiates a scan for a group
func (s *UsageScannerService) ScanGroupUsage(ctx context.Context, request models.UsageScanRequest) (string, error) {
	log.Printf("Starting usage scan for group %s in repo %s", request.GroupName, request.RepoID)

	// Generate unique scan ID and task ID to prevent race conditions
	// Format: GroupName:RepoID:timestamp:active (timestamp ensures uniqueness)
	timestamp := time.Now().Unix()
	scanID := fmt.Sprintf("%s:%s:%d:active", request.GroupName, request.RepoID, timestamp)
	taskID := fmt.Sprintf("%s:%s:%d", request.GroupName, request.RepoID, timestamp)

	// Create cancellable context for this scan
	scanCtx, cancel := context.WithCancel(ctx)

	// Register the task with the cancellation manager
	taskName := "Usage Scan: " + request.GroupName
	if err := s.cancellationManager.RegisterTask(taskID, "usage_scan", taskName, request.GroupName, request.RepoID, cancel); err != nil {
		cancel() // Clean up the context
		return "", fmt.Errorf("failed to register task for cancellation: %w", err)
	}

	// Track scanID to taskID mapping for completion synchronization
	s.taskMapMutex.Lock()
	s.scanToTaskMap[scanID] = taskID
	s.taskMapMutex.Unlock()

	// Ensure cleanup when function exits
	defer func() {
		// Check if the task was cancelled before cleanup
		var wasCancelled bool
		if taskInfo, err := s.cancellationManager.GetTaskInfo(taskID); err == nil && taskInfo.Status == "cancelled" {
			wasCancelled = true
		}

		s.cancellationManager.CleanupTask(taskID)

		// Clean up scanID to taskID mapping
		s.taskMapMutex.Lock()
		delete(s.scanToTaskMap, scanID)
		s.taskMapMutex.Unlock()

		// Update scan logger if the task was cancelled
		if wasCancelled && s.scanLogger != nil {
			s.scanLogger.CompleteScan(scanID, models.ScanStatusCancelled)
		}
	}()

	if s.scanLogger != nil {
		s.scanLogger.StartScan(scanID, request.RepoID, request.GroupName, 0) // Will update total sources later
	}

	// Get sources to scan
	var sources []models.UsageSource
	var err error

	if len(request.SourceIDs) > 0 {
		// Scan specific sources
		for _, sourceID := range request.SourceIDs {
			source, err := s.sourceManager.GetSource(sourceID)
			if err != nil {
				log.Printf("Warning: Could not get source %s: %v", sourceID, err)
				continue
			}
			if source.IsActive {
				sources = append(sources, source)
			}
		}
	} else {
		// Scan all active sources
		sources, err = s.sourceManager.GetAllSources()
		if err != nil {
			return "", fmt.Errorf("failed to get sources: %w", err)
		}

		// Filter to active sources only
		var activeSources []models.UsageSource
		for _, source := range sources {
			if source.IsActive {
				activeSources = append(activeSources, source)
			}
		}
		sources = activeSources
	}

	if len(sources) == 0 {
		if s.scanLogger != nil {
			s.scanLogger.LogError(scanID, "source_discovery", "No active sources found to scan", "No active sources are configured for scanning")
			s.scanLogger.CompleteScan(scanID, models.ScanStatusFailed)
		}
		return "", fmt.Errorf("no active sources found to scan")
	}

	// Update scan logger with total sources
	if s.scanLogger != nil {
		s.scanLogger.LogStep(scanID, "source_discovery", fmt.Sprintf("Found %d active sources to scan", len(sources)), &models.Progress{
			Current:     0,
			Total:       len(sources),
			Percentage:  0,
			Description: "Preparing to scan sources",
		})
	}

	// Initialize scan status
	status := models.UsageScanStatus{
		GroupName:        request.GroupName,
		SourcesTotal:     len(sources),
		SourcesScanned:   0,
		InProgress:       true,
		LastScanTime:     time.Now(),
		CompletedSources: []string{},
		PendingSources:   make([]string, len(sources)),
		FailedSources:    []models.SourceScanFailure{},
		RepoID:           request.RepoID,
		TotalUsages:      0,
		ScanDuration:     0,
	}

	for i, source := range sources {
		status.PendingSources[i] = source.ID
	}

	// Save initial status
	if err := s.statusManager.UpdateStatus(status); err != nil {
		log.Printf("Warning: Failed to save initial scan status: %v", err)
	}

	// Start heartbeat for this scan
	s.statusManager.UpdateHeartbeat(request.GroupName, request.RepoID)

	// Register scan sources with worker pool for completion tracking
	s.workerPool.RegisterScanSources(scanID, len(sources))

	// Submit scan jobs to worker pool
	for i, source := range sources {
		if s.scanLogger != nil {
			s.scanLogger.LogSourceStart(scanID, source.ID, source.Name, string(source.Type))
		}

		handler, err := s.handlerRegistry.GetHandler(source.Type)
		if err != nil {
			log.Printf("Warning: No handler for source type %s: %v", source.Type, err)
			if s.scanLogger != nil {
				s.scanLogger.LogError(scanID, "handler_initialization", fmt.Sprintf("No handler for source type %s", source.Type), err.Error(), source.ID)
			}
			continue
		}

		if err := handler.Initialize(source); err != nil {
			log.Printf("Warning: Failed to initialize handler for source %s: %v", source.Name, err)
			if s.scanLogger != nil {
				s.scanLogger.LogError(scanID, "handler_initialization", fmt.Sprintf("Failed to initialize handler for source %s", source.Name), err.Error(), source.ID)
			}
			continue
		}

		job := ScanJob{
			GroupName: request.GroupName,
			RepoID:    request.RepoID,
			Source:    source,
			Handler:   handler,
			Context:   scanCtx,
			ScanID:    scanID,       // Add scan ID to job
			Logger:    s.scanLogger, // Add scan logger for UI integration
		}

		if err := s.workerPool.SubmitJob(job); err != nil {
			log.Printf("Warning: Failed to submit scan job for source %s: %v", source.Name, err)
			if s.scanLogger != nil {
				s.scanLogger.LogError(scanID, "job_submission", fmt.Sprintf("Failed to submit scan job for source %s", source.Name), err.Error(), source.ID)
			}
		} else {
			if s.scanLogger != nil {
				s.scanLogger.LogStep(scanID, "job_submission", fmt.Sprintf("Submitted scan job for source %s", source.Name), &models.Progress{
					Current:     i + 1,
					Total:       len(sources),
					Percentage:  int(float64(i+1) / float64(len(sources)) * 100),
					Description: fmt.Sprintf("Submitting scan jobs (%d/%d)", i+1, len(sources)),
				})
			}
		}
	}

	// Log scan completion
	if s.scanLogger != nil {
		s.scanLogger.LogStep(scanID, "scan_initiated", "All scan jobs submitted to worker pool", &models.Progress{
			Current:     len(sources),
			Total:       len(sources),
			Percentage:  100,
			Description: "Scan jobs submitted, processing in background",
		})
	}

	return scanID, nil
}

// GetScanStatus gets the status of a scan
func (s *UsageScannerService) GetScanStatus(groupName, repoID string) (models.UsageScanStatus, error) {
	return s.statusManager.GetStatus(groupName, repoID)
}

// GetUsageResults gets the results of a scan
func (s *UsageScannerService) GetUsageResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error) {
	return s.statusManager.GetResults(groupName, repoID, page, pageSize)
}

// CancelScan cancels an ongoing scan
func (s *UsageScannerService) CancelScan(groupName, repoID string) error {
	taskID := groupName + ":" + repoID
	log.Printf("Canceling scan for group %s in repo %s (task ID: %s)", groupName, repoID, taskID)

	// Check if scan is actually running
	status, err := s.statusManager.GetStatus(groupName, repoID)
	if err != nil {
		return fmt.Errorf("scan status not found for group %s in repo %s: %w", groupName, repoID, err)
	}

	if !status.InProgress {
		return fmt.Errorf("no scan in progress for group %s in repo %s", groupName, repoID)
	}

	// Check if the scan is in a critical state where cancellation might be unsafe
	// For now, we allow cancellation at any time, but this could be extended
	// to check for specific conditions like data consistency requirements
	if status.SourcesScanned > 0 && status.SourcesScanned == status.SourcesTotal-1 {
		log.Printf("Warning: Cancelling scan for group %s in repo %s that is nearly complete (%d/%d sources)",
			groupName, repoID, status.SourcesScanned, status.SourcesTotal)
	}

	// Cancel the task using the cancellation manager
	if err := s.cancellationManager.CancelTask(taskID); err != nil {
		return fmt.Errorf("failed to cancel task %s: %w", taskID, err)
	}

	// Update the scan status to cancelled
	status.InProgress = false
	// Note: UsageScanStatus doesn't have a Status field, but InProgress=false indicates completion
	// The cancellation is tracked by the TaskCancellationManager

	if err := s.statusManager.UpdateStatus(status); err != nil {
		log.Printf("Warning: Failed to update scan status after cancellation: %v", err)
	}

	log.Printf("Successfully cancelled scan for group %s in repo %s", groupName, repoID)
	return nil
}

// ClearUsageResults clears the results of a scan
func (s *UsageScannerService) ClearUsageResults(groupName, repoID string) error {
	return s.statusManager.ClearResults(groupName, repoID)
}

// ClearFailedStatus clears the failed status of a scan, removing it from failed tasks list
func (s *UsageScannerService) ClearFailedStatus(groupName, repoID string) error {
	if s.statusManager == nil {
		return nil
	}
	return s.statusManager.ClearFailedStatus(groupName, repoID)
}

// GetUsageStatistics gets usage statistics
func (s *UsageScannerService) GetUsageStatistics(repoID string) (models.UsageStatistics, error) {
	// TODO: Implement usage statistics calculation
	// This would involve aggregating data from all scan results
	sources, err := s.sourceManager.GetAllSources()
	if err != nil {
		return models.UsageStatistics{}, err
	}

	activeSources := 0
	for _, source := range sources {
		if source.IsActive {
			activeSources++
		}
	}

	stats := models.UsageStatistics{
		TotalGroups:       0, // TODO: Get from repository
		GroupsWithUsage:   0, // TODO: Calculate from scan results
		TotalUsages:       0, // TODO: Calculate from scan results
		SourcesConfigured: len(sources),
		SourcesActive:     activeSources,
		LastScanTime:      time.Now(), // TODO: Get actual last scan time
	}

	return stats, nil
}

// IsRunning returns whether the usage scanner service is running
func (s *UsageScannerService) IsRunning() bool {
	return s.isRunning
}

// GetWorkerPoolQueueSize returns the current size of the worker pool job queue
func (s *UsageScannerService) GetWorkerPoolQueueSize() int {
	if s.workerPool != nil {
		return s.workerPool.GetQueueSize()
	}
	return 0
}

// GetWorkerPoolStatus returns whether the worker pool is running
func (s *UsageScannerService) GetWorkerPoolStatus() bool {
	if s.workerPool != nil {
		return s.workerPool.IsRunning()
	}
	return false
}

// processScan processes a scan request (called by worker pool)
func (s *UsageScannerService) processScan(ctx context.Context, request models.UsageScanRequest) {
	// This method would be called by the worker pool to process individual scan jobs
	// Implementation would be similar to ScanGroupUsage but for a single source
}

// ScanJob represents a scan job
type ScanJob struct {
	GroupName string
	RepoID    string
	Source    models.UsageSource
	Handler   SourceHandler
	Context   context.Context
	ScanID    string
	Logger    *ScanLogger // Add scan logger for UI integration
}

// ScanResult represents a scan result
type ScanResult struct {
	GroupName   string
	RepoID      string
	SourceID    string
	ScanID      string
	Results     []models.UsageResult
	Error       error
	CompletedAt time.Time
}

// GetActiveScans returns all currently active scans for the scheduler dashboard
func (s *UsageScannerService) GetActiveScans() ([]models.ActiveTask, error) {
	var activeTasks []models.ActiveTask

	if s.statusManager == nil {
		return activeTasks, nil
	}

	// Get all active scan statuses
	activeStatuses, err := s.statusManager.GetAllActiveScans()
	if err != nil {
		log.Printf("Error getting active scans: %v", err)
		return activeTasks, err
	}

	// Convert scan statuses to active tasks
	for _, status := range activeStatuses {
		if !status.InProgress {
			continue
		}

		// Calculate progress percentage
		var progressPercentage int
		if status.SourcesTotal > 0 {
			progressPercentage = int(float64(status.SourcesScanned) / float64(status.SourcesTotal) * 100)
		}

		// Create progress object
		progress := &models.Progress{
			Current:     status.SourcesScanned,
			Total:       status.SourcesTotal,
			Percentage:  progressPercentage,
			Description: fmt.Sprintf("Scanning %d of %d sources", status.SourcesScanned, status.SourcesTotal),
		}

		// Generate unique task ID using the same timestamp pattern as scan creation
		// This ensures each retry gets a unique task ID to prevent UI overlap
		taskID := status.GroupName + ":" + status.RepoID

		// Check if we have a registered task with timestamp in the cancellation manager
		if s.cancellationManager != nil {
			// Look for active tasks that match this group/repo pattern
			activeTasks := s.cancellationManager.GetActiveTasks()
			for _, activeTask := range activeTasks {
				if activeTask.GroupName == status.GroupName && activeTask.RepoID == status.RepoID {
					taskID = activeTask.ID // Use the unique task ID from cancellation manager
					break
				}
			}
		}

		task := models.ActiveTask{
			ID:         taskID,
			Type:       "usage_scan",
			Name:       "Usage Scan: " + status.GroupName,
			StartedAt:  status.LastScanTime.Format("2006-01-02T15:04:05Z07:00"),
			Progress:   progress,
			Repository: status.RepoID,
			GroupName:  status.GroupName,
			Status:     "running",
		}

		activeTasks = append(activeTasks, task)
	}

	return activeTasks, nil
}

// GetFailedScans returns all failed scans for the scheduler dashboard
func (s *UsageScannerService) GetFailedScans() ([]models.UsageScanStatus, error) {
	if s.statusManager == nil {
		return []models.UsageScanStatus{}, nil
	}

	return s.statusManager.GetFailedScans()
}

// GetInterruptedScans returns all interrupted scans for the scheduler dashboard
func (s *UsageScannerService) GetInterruptedScans() ([]models.UsageScanStatus, error) {
	if s.statusManager == nil {
		return []models.UsageScanStatus{}, nil
	}

	return s.statusManager.GetInterruptedScans()
}

// CleanupStaleTasks manually cleans up stale tasks
func (s *UsageScannerService) CleanupStaleTasks() (int, error) {
	if s.statusManager == nil {
		return 0, fmt.Errorf("status manager not available")
	}

	return s.statusManager.CleanupStaleTasks()
}

// GetCompletedScans returns all completed scans for the scheduler dashboard
func (s *UsageScannerService) GetCompletedScans() ([]models.UsageScanStatus, error) {
	if s.statusManager == nil {
		return []models.UsageScanStatus{}, nil
	}

	// Get all scans
	allScans, err := s.statusManager.GetAllScans()
	if err != nil {
		log.Printf("Error getting all scans: %v", err)
		return []models.UsageScanStatus{}, err
	}

	// Filter for completed scans (not in progress and have been scanned)
	var completedScans []models.UsageScanStatus
	for _, scan := range allScans {
		if !scan.InProgress && scan.SourcesScanned > 0 {
			completedScans = append(completedScans, scan)
		}
	}

	return completedScans, nil
}

// orphanCheckLoop periodically checks for orphaned scans
func (s *UsageScannerService) orphanCheckLoop() {
	log.Println("Orphan scan checker started")

	for {
		select {
		case <-s.stopOrphanCheck:
			log.Println("Orphan scan checker stopped")
			return
		case <-s.orphanCheckTicker.C:
			log.Println("Checking for orphaned scans...")
			s.statusManager.CheckForOrphanedScans()
		}
	}
}

// handleScanCompletion handles scan completion and updates the corresponding scheduler task
func (s *UsageScannerService) handleScanCompletion(scanID string) {
	log.Printf("Handling scan completion for scanID: %s", scanID)

	// Extract group name and repo ID from scan ID (format: GroupName:RepoID:timestamp:active)
	parts := strings.Split(scanID, ":")
	if len(parts) >= 2 {
		groupName := parts[0]
		repoID := parts[1]

		// Ensure the scan status is marked as not in progress and capture file count information
		if s.statusManager != nil {
			if status, err := s.statusManager.GetStatus(groupName, repoID); err == nil {
				// Always try to capture file count information, regardless of scan status
				needsUpdate := false

				if status.InProgress {
					log.Printf("Scan %s is still marked as InProgress, forcing completion", scanID)
					status.InProgress = false
					needsUpdate = true
				} else {
					log.Printf("Scan %s is already marked as completed", scanID)
				}

				// Capture file count information from scan logger if available (always try)
				if s.scanLogger != nil {
					if summary := s.scanLogger.GetScanSummary(scanID); summary != nil {
						// Only update if we don't already have file count information
						if status.TotalFiles == 0 && status.ProcessedFiles == 0 {
							status.TotalFiles = summary.TotalFiles
							status.ProcessedFiles = summary.ProcessedFiles
							needsUpdate = true
							log.Printf("Captured file count for scan %s: %d total files, %d processed",
								scanID, status.TotalFiles, status.ProcessedFiles)
						} else {
							log.Printf("Scan %s already has file count: %d total files, %d processed",
								scanID, status.TotalFiles, status.ProcessedFiles)
						}
					} else {
						log.Printf("Warning: No scan summary found for scanID %s", scanID)
					}
				} else {
					log.Printf("Warning: No scan logger available for file count capture")
				}

				if needsUpdate {
					if err := s.statusManager.UpdateStatus(status); err != nil {
						log.Printf("Warning: Failed to update scan status: %v", err)
					} else {
						log.Printf("Successfully updated scan %s with file counts", scanID)
					}
				}

				// Store scan history in vector database
				s.storeScanHistoryInVectorDB(scanID, groupName, repoID, status)
			} else {
				log.Printf("Warning: Could not get scan status for %s:%s: %v", groupName, repoID, err)
			}
		}
	}

	// Report scan completion via scan logger for real-time updates
	if s.scanLogger != nil {
		s.scanLogger.LogStep(scanID, "completion", "Scan completed successfully", &models.Progress{
			Current:     100,
			Total:       100,
			Percentage:  100,
			Description: "Scan completed successfully",
		})
	}

	// Get the corresponding taskID
	s.taskMapMutex.RLock()
	taskID, exists := s.scanToTaskMap[scanID]
	s.taskMapMutex.RUnlock()

	if !exists {
		log.Printf("Warning: No taskID found for completed scanID: %s", scanID)
		return
	}

	// Get task info to check if it's still active
	taskInfo, err := s.cancellationManager.GetTaskInfo(taskID)
	if err != nil {
		log.Printf("Warning: Could not get task info for taskID %s: %v", taskID, err)
		return
	}

	// Only complete the task if it's still running (not cancelled)
	if taskInfo.Status == "running" {
		log.Printf("Completing scheduler task %s for completed scan %s", taskID, scanID)

		// Mark the task as completed by cleaning it up
		// This will remove it from the active tasks list
		s.cancellationManager.CleanupTask(taskID)

		// Broadcast task completion event to notify frontend
		if s.progressBroadcaster != nil {
			update := models.RealTimeProgressUpdate{
				ScanID:    scanID,
				RepoID:    taskInfo.RepoID,
				GroupName: taskInfo.GroupName,
				Timestamp: time.Now(),
				EventType: "task_completed",
				Message:   "Task completed successfully",
				TaskID:    taskID,
				TaskType:  taskInfo.Type,
				Success:   true,
			}
			// Send to the update channel for processing and broadcasting
			updateChannel := s.progressBroadcaster.GetUpdateChannel()
			select {
			case updateChannel <- update:
				// Successfully sent
			default:
				log.Printf("Warning: Failed to send task completion event for task %s", taskID)
			}
		}

		log.Printf("Successfully completed scheduler task %s", taskID)
	} else {
		log.Printf("Task %s is already in status %s, not completing", taskID, taskInfo.Status)
	}

	// Clean up the mapping
	s.taskMapMutex.Lock()
	delete(s.scanToTaskMap, scanID)
	s.taskMapMutex.Unlock()
}

// storeScanHistoryInVectorDB stores scan history in the vector database
func (s *UsageScannerService) storeScanHistoryInVectorDB(scanID, groupName, repoID string, status models.UsageScanStatus) {
	if s.vectorDB == nil {
		log.Printf("Vector database not available for storing scan history")
		return
	}

	// Get scan summary for detailed information
	var summary *models.ScanLogSummary
	if s.scanLogger != nil {
		summary = s.scanLogger.GetScanSummary(scanID)
	}

	// Determine scan type (manual, auto, or scheduled)
	scanType := "manual" // Default assumption
	// Note: ScanLogSummary doesn't have Metadata field, so we'll use manual as default
	// In the future, this could be enhanced by passing scan type through the scan request

	// Determine scan status
	scanStatus := "completed"
	errorMessage := ""
	if len(status.FailedSources) > 0 {
		if status.SourcesScanned == 0 {
			scanStatus = "failed"
		} else {
			scanStatus = "completed" // Partial success
		}
		// Collect error messages
		for i, failure := range status.FailedSources {
			if i > 0 {
				errorMessage += "; "
			}
			errorMessage += failure.Error
			if i >= 2 { // Limit to first 3 errors
				errorMessage += "..."
				break
			}
		}
	}

	// Notify scan logger about completion/failure
	if s.scanLogger != nil {
		var scanLogStatus models.ScanStatus
		switch scanStatus {
		case "completed":
			scanLogStatus = models.ScanStatusCompleted
		case "failed":
			scanLogStatus = models.ScanStatusFailed
		default:
			scanLogStatus = models.ScanStatusCompleted // Default to completed
		}
		s.scanLogger.CompleteScan(scanID, scanLogStatus)
	}

	// Calculate duration
	var startTime, endTime time.Time
	var duration int64

	if summary != nil {
		startTime = summary.StartTime
		if summary.EndTime != nil {
			endTime = *summary.EndTime
		} else {
			endTime = time.Now() // Fallback if end time not set
		}
		if summary.Duration != nil {
			duration = *summary.Duration
		} else {
			duration = endTime.Sub(startTime).Milliseconds()
		}
	} else {
		// Fallback to status information
		startTime = status.LastScanTime
		endTime = time.Now()
		duration = endTime.Sub(startTime).Milliseconds()
	}

	// Create performance metrics
	performance := ScanPerformanceMetrics{
		FilesScanned:    status.ProcessedFiles,
		LinesProcessed:  0, // Not tracked in current system
		AvgFileSize:     0, // Not tracked in current system
		ProcessingSpeed: 0, // Will calculate if duration > 0
		MemoryUsage:     0, // Not tracked in current system
		AuthCacheHits:   0, // Not tracked in current system
		AuthCacheMisses: 0, // Not tracked in current system
	}

	if duration > 0 {
		performance.ProcessingSpeed = float64(status.ProcessedFiles) / (float64(duration) / 1000.0) // files per second
	}

	// Create scan history entry
	entry := ScanHistoryEntry{
		ID:            scanID,
		GroupName:     groupName,
		RepoID:        repoID,
		ScanType:      scanType,
		Status:        scanStatus,
		StartTime:     startTime,
		EndTime:       endTime,
		Duration:      duration,
		ResultsCount:  status.TotalUsages,
		ErrorMessage:  errorMessage,
		SourcesTotal:  status.SourcesTotal,
		SourcesFailed: len(status.FailedSources),
		Performance:   performance,
	}

	// Store in vector database
	ctx := context.Background()
	if err := s.vectorDB.StoreScanHistory(ctx, entry); err != nil {
		log.Printf("Warning: Failed to store scan history in vector database: %v", err)
	} else {
		log.Printf("Successfully stored scan history for %s:%s in vector database", groupName, repoID)
	}

	// Also store scan logs if available
	if s.scanLogger != nil {
		s.storeScanLogsInVectorDB(scanID, groupName, repoID)
	}
}

// storeScanLogsInVectorDB stores scan logs in the vector database
func (s *UsageScannerService) storeScanLogsInVectorDB(scanID, groupName, repoID string) {
	if s.vectorDB == nil {
		return
	}

	// Get scan logs
	logsResponse := s.scanLogger.GetScanLogs(models.ScanLogFilter{
		ScanID: scanID,
	})

	ctx := context.Background()
	logCount := 0

	// Store important log entries (errors, warnings, and key steps)
	for _, logEntry := range logsResponse.Logs {
		if logEntry.Level == models.LogLevelError ||
			logEntry.Level == models.LogLevelWarning ||
			logEntry.Step == "initialization" ||
			logEntry.Step == "completion" {

			err := s.vectorDB.StoreScanLog(ctx, scanID, groupName, repoID,
				string(logEntry.Level), logEntry.Message, logEntry.Step, logEntry.SourceID)
			if err != nil {
				log.Printf("Warning: Failed to store scan log in vector database: %v", err)
			} else {
				logCount++
			}
		}
	}

	if logCount > 0 {
		log.Printf("Stored %d scan log entries for %s:%s in vector database", logCount, groupName, repoID)
	}
}
