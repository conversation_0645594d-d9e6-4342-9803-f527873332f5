package models

import (
	"encoding/json"
	"time"
)

// SchedulerLogLevel represents the severity level of a scheduler log entry
type SchedulerLogLevel string

const (
	SchedulerLogLevelInfo    SchedulerLogLevel = "info"
	SchedulerLogLevelWarning SchedulerLogLevel = "warning"
	SchedulerLogLevelError   SchedulerLogLevel = "error"
	SchedulerLogLevelDebug   SchedulerLogLevel = "debug"
)

// SchedulerLogAction represents the type of action being logged
type SchedulerLogAction string

const (
	ActionScheduled     SchedulerLogAction = "scheduled"
	ActionTriggered     SchedulerLogAction = "triggered"
	ActionSkipped       SchedulerLogAction = "skipped"
	ActionCompleted     SchedulerLogAction = "completed"
	ActionFailed        SchedulerLogAction = "failed"
	ActionConfigChanged SchedulerLogAction = "config_changed"
	ActionPrioritized   SchedulerLogAction = "prioritized"
	ActionQueued        SchedulerLogAction = "queued"
	ActionDequeued      SchedulerLogAction = "dequeued"
	ActionCancelled     SchedulerLogAction = "cancelled"
)

// SchedulerLog represents a log entry for scheduler decisions and actions
type SchedulerLog struct {
	ID        string                 `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	Level     SchedulerLogLevel      `json:"level"`
	Action    SchedulerLogAction     `json:"action"`
	RepoID    string                 `json:"repoId"`
	GroupName string                 `json:"groupName,omitempty"`
	ConfigID  string                 `json:"configId,omitempty"`
	JobID     string                 `json:"jobId,omitempty"`
	Message   string                 `json:"message"`
	Reason    string                 `json:"reason,omitempty"`
	Priority  int                    `json:"priority,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Duration  *time.Duration         `json:"duration,omitempty"`
	Error     string                 `json:"error,omitempty"`
}

// SchedulerLogFilter represents filters for querying scheduler logs
type SchedulerLogFilter struct {
	StartTime  *time.Time          `json:"startTime,omitempty"`
	EndTime    *time.Time          `json:"endTime,omitempty"`
	Level      *SchedulerLogLevel  `json:"level,omitempty"`
	Action     *SchedulerLogAction `json:"action,omitempty"`
	RepoID     string              `json:"repoId,omitempty"`
	GroupName  string              `json:"groupName,omitempty"`
	ConfigID   string              `json:"configId,omitempty"`
	JobID      string              `json:"jobId,omitempty"`
	SearchText string              `json:"searchText,omitempty"`
	Limit      int                 `json:"limit,omitempty"`
	Offset     int                 `json:"offset,omitempty"`
}

// SchedulerLogSummary represents a summary of scheduler activity
type SchedulerLogSummary struct {
	Period              string                     `json:"period"`
	StartTime           time.Time                  `json:"startTime"`
	EndTime             time.Time                  `json:"endTime"`
	TotalLogs           int                        `json:"totalLogs"`
	ActionCounts        map[SchedulerLogAction]int `json:"actionCounts"`
	LevelCounts         map[SchedulerLogLevel]int  `json:"levelCounts"`
	RepositoryCounts    map[string]int             `json:"repositoryCounts"`
	TopGroups           []GroupActivity            `json:"topGroups"`
	ErrorRate           float64                    `json:"errorRate"`
	AverageDecisionTime *time.Duration             `json:"averageDecisionTime,omitempty"`
}

// GroupActivity represents activity for a specific group
type GroupActivity struct {
	RepoID      string    `json:"repoId"`
	GroupName   string    `json:"groupName"`
	ActionCount int       `json:"actionCount"`
	LastAction  time.Time `json:"lastAction"`
}

// Helper methods
func (log *SchedulerLog) ToJSON() ([]byte, error) {
	return json.Marshal(log)
}

func (log *SchedulerLog) FromJSON(data []byte) error {
	return json.Unmarshal(data, log)
}

// Matches checks if the log entry matches the given filter
func (log *SchedulerLog) Matches(filter *SchedulerLogFilter) bool {
	if filter == nil {
		return true
	}

	// Time range filter
	if filter.StartTime != nil && log.Timestamp.Before(*filter.StartTime) {
		return false
	}
	if filter.EndTime != nil && log.Timestamp.After(*filter.EndTime) {
		return false
	}

	// Level filter
	if filter.Level != nil && log.Level != *filter.Level {
		return false
	}

	// Action filter
	if filter.Action != nil && log.Action != *filter.Action {
		return false
	}

	// Repository filter
	if filter.RepoID != "" && log.RepoID != filter.RepoID {
		return false
	}

	// Group filter
	if filter.GroupName != "" && log.GroupName != filter.GroupName {
		return false
	}

	// Config filter
	if filter.ConfigID != "" && log.ConfigID != filter.ConfigID {
		return false
	}

	// Job filter
	if filter.JobID != "" && log.JobID != filter.JobID {
		return false
	}

	// Search text filter (searches in message and reason)
	if filter.SearchText != "" {
		searchText := filter.SearchText
		if !contains(log.Message, searchText) && !contains(log.Reason, searchText) {
			return false
		}
	}

	return true
}

// Helper function for case-insensitive string contains
func contains(str, substr string) bool {
	if str == "" || substr == "" {
		return substr == ""
	}
	// Simple case-insensitive contains check
	// In production, you might want to use strings.ToLower() or a more sophisticated search
	return len(str) >= len(substr) &&
		(str == substr ||
			(len(str) > len(substr) &&
				(str[:len(substr)] == substr ||
					str[len(str)-len(substr):] == substr ||
					containsSubstring(str, substr))))
}

func containsSubstring(str, substr string) bool {
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
