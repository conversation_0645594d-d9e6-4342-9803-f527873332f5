# Auto-Schedule and Auto-Scan System Architecture

## Overview

The Auto-Schedule and Auto-Scan system provides automated repository scanning capabilities with intelligent scheduling, load balancing, and continuous queue management. This system enables organizations to maintain up-to-date usage analytics across multiple repositories without manual intervention.

## Core Components

### 1. Auto-Scan Service (`AutoScanService`)
**Location**: `src/backend/services/auto_scan_service.go`

The central orchestrator that manages all automated scanning operations.

**Key Responsibilities**:
- Repository configuration management
- Job scheduling and execution
- Load balancing and concurrency control
- Continuous queue management
- Scheduler logging and monitoring

**Core Data Structures**:
```go
type AutoScanService struct {
    configsDir           string                              // Configuration storage directory
    statusDir            string                              // Job status tracking directory
    jobsDir              string                              // Active job storage directory
    logsDir              string                              // Scheduler logs directory
    usageScanner         *UsageScanner                       // Scanner instance
    configs              map[string]*models.AutoScanConfig   // Repository configurations
    activeJobs           map[string]*models.AutoScanJob      // Currently running jobs
    scanQueue            chan *models.AutoScanJob            // Job execution queue
    maxConcurrentScans   int                                 // Global concurrent scan limit
    currentScans         map[string]bool                     // Active scan tracking
    schedulerLogs        []*models.SchedulerLog              // In-memory scheduler logs
    schedulingConfigs    map[string]*models.ScanSchedulingConfig // Per-repo scheduling rules
    targetQueueSize      int                                 // Continuous scheduling target
    continuousScheduling bool                                // Continuous scheduling enabled
}
```

### 2. Configuration Management

#### Repository-Specific Auto-Scan Configuration
**Storage**: `data/auto_scan_configs/repo_{id}.json`

```json
{
  "id": "repo_1",
  "repositoryId": "repo_1",
  "enabled": true,
  "groups": ["group1", "group2"],
  "maxConcurrentScans": 3,
  "loadBalancing": {
    "spreadAcrossDay": false,
    "minIntervalBetween": "10m",
    "maxScansPerHour": 10
  },
  "scheduling": {
    "enabled": true,
    "priority": 5,
    "timeWindows": [
      {
        "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
        "startTime": "09:00",
        "endTime": "17:00"
      }
    ]
  }
}
```

**Configuration Fields**:
- `enabled`: Whether auto-scanning is enabled for this repository
- `groups`: List of groups to scan within the repository
- `maxConcurrentScans`: Per-repository concurrent scan limit
- `loadBalancing.minIntervalBetween`: Minimum time between scans for the same repository
- `loadBalancing.maxScansPerHour`: Maximum scans per hour for this repository
- `scheduling.priority`: Priority level (1-10, higher = more priority)
- `scheduling.timeWindows`: Allowed time windows for scanning

#### Continuous Scheduling Configuration
**Storage**: `data/continuous_scheduling.json`

```json
{
  "enabled": false,
  "targetQueueSize": 15,
  "checkIntervalMinutes": 2,
  "maxConcurrentScans": 5
}
```

**Configuration Fields**:
- `enabled`: Whether continuous scheduling is active
- `targetQueueSize`: Target number of pending tasks to maintain
- `checkIntervalMinutes`: How often to check and replenish the queue
- `maxConcurrentScans`: Global maximum concurrent scans across all repositories

### 3. Job Management

#### Auto-Scan Job Structure
**Storage**: `data/auto_scan_jobs/{job_id}.json`

```json
{
  "id": "uuid-string",
  "repositoryId": "repo_1",
  "groupName": "group1",
  "status": "pending|running|completed|failed",
  "createdAt": "2025-07-28T10:00:00Z",
  "startedAt": "2025-07-28T10:01:00Z",
  "completedAt": "2025-07-28T10:05:00Z",
  "scheduledBy": "continuous|manual|scheduled",
  "priority": 5,
  "error": "error message if failed"
}
```

#### Job Status Tracking
**Storage**: `data/auto_scan_status/{repository_id}_{group_name}.json`

Tracks the last scan status for each repository-group combination to prevent duplicate scheduling.

### 4. Scheduling System

#### Regular Scheduler (Every 5 Minutes)
**Function**: `scheduleAutoScans()`

**Process**:
1. Iterate through all repository configurations
2. Check scheduling rules (time windows, priorities)
3. Apply load balancing constraints
4. Create jobs for eligible repository-group combinations
5. Log scheduling decisions

#### Continuous Scheduler (Configurable Interval)
**Function**: `maintainContinuousQueue()`

**Process**:
1. Count current pending jobs
2. If below target queue size, schedule additional jobs
3. Prioritize based on repository priority settings
4. Respect load balancing and time window constraints
5. Log queue management decisions

### 5. Load Balancing

#### Per-Repository Constraints
- `maxConcurrentScans`: Limits concurrent scans per repository
- `minIntervalBetween`: Enforces minimum time between scans
- `maxScansPerHour`: Rate limiting per repository

#### Global Constraints
- `maxConcurrentScans` (service-level): Total concurrent scans across all repositories
- Queue-based execution to prevent system overload

### 6. Scheduler Logging

#### Log Structure
**Storage**: `data/scheduler_logs/{timestamp}_{uuid}.json`

```json
{
  "timestamp": "2025-07-28T10:00:00Z",
  "repositoryId": "repo_1",
  "groupName": "group1",
  "action": "scheduled|skipped|queued",
  "reason": "time_window|load_balancing|already_pending|priority",
  "details": {
    "priority": 5,
    "queueSize": 12,
    "lastScanTime": "2025-07-28T09:30:00Z"
  }
}
```

**Log Categories**:
- **Scheduled**: Job was created and queued
- **Skipped**: Job was not created due to constraints
- **Queued**: Job added to continuous queue
- **Load Balanced**: Job delayed due to load balancing rules

## API Endpoints

### Repository Configuration Management
```
GET    /api/scheduler/auto-scan/configs           # List all configurations
GET    /api/scheduler/auto-scan/configs/{id}      # Get specific configuration
POST   /api/scheduler/auto-scan/configs           # Create new configuration
PUT    /api/scheduler/auto-scan/configs/{id}      # Update configuration
DELETE /api/scheduler/auto-scan/configs/{id}      # Delete configuration
```

### Job Management
```
GET    /api/scheduler/auto-scan/jobs              # List jobs with filtering
POST   /api/scheduler/auto-scan/jobs              # Create manual job
DELETE /api/scheduler/auto-scan/jobs/{id}         # Cancel job
```

### Continuous Scheduling
```
GET    /api/scheduler/auto-scan/continuous-config # Get continuous config
PUT    /api/scheduler/auto-scan/continuous-config # Update continuous config
```

### Scheduler Logs
```
GET    /api/scheduler/auto-scan/logs              # Get scheduler logs with filtering
DELETE /api/scheduler/auto-scan/logs              # Clear scheduler logs
```

### Statistics and Monitoring
```
GET    /api/scheduler/auto-scan/stats             # Get scheduling statistics
GET    /api/scheduler/auto-scan/queue-status      # Get current queue status
```

## Frontend Components

### 1. Repository Configuration Interface
**Location**: Auto-scan configuration tabs in repository management

**Features**:
- Enable/disable auto-scanning per repository
- Configure groups to scan
- Set load balancing parameters
- Define scheduling time windows
- Set repository priority levels

### 2. Continuous Scheduling Dashboard
**Location**: Dedicated configuration tab

**Features**:
- Enable/disable continuous scheduling
- Configure target queue size
- Set check interval
- Configure global concurrent scan limits
- Real-time queue status monitoring

### 3. Job Monitoring Dashboard
**Location**: Scheduler dashboard

**Features**:
- Real-time job status display
- Progress tracking with WebSocket updates
- Job filtering and search
- Manual job creation
- Job cancellation capabilities

### 4. Scheduler Logs Interface
**Location**: Task history tabs

**Features**:
- Scheduler decision logging
- Filtering by repository, action, time range
- Export capabilities
- Log retention management

### 5. Statistics and Analytics
**Location**: Administrative dashboard

**Features**:
- Scheduling efficiency metrics
- Repository scan frequency analysis
- Load balancing effectiveness
- System performance monitoring

## Data Flow

### 1. Configuration Setup
```
User → Frontend Config UI → API → AutoScanService → File Storage
```

### 2. Regular Scheduling (Every 5 Minutes)
```
Timer → scheduleAutoScans() → Check Configs → Apply Rules → Create Jobs → Queue
```

### 3. Continuous Scheduling (Every N Minutes)
```
Timer → maintainContinuousQueue() → Check Queue Size → Schedule Jobs → Update Queue
```

### 4. Job Execution
```
Queue → Worker Goroutine → UsageScanner → Update Status → Complete/Fail
```

### 5. Real-time Updates
```
Job Status Change → WebSocket → Frontend → UI Update
```

## Configuration Persistence

### File-Based Storage
All configurations are stored as JSON files in the `data/` directory:

- **Repository configs**: `data/auto_scan_configs/repo_{id}.json`
- **Continuous config**: `data/continuous_scheduling.json`
- **Job definitions**: `data/auto_scan_jobs/{job_id}.json`
- **Status tracking**: `data/auto_scan_status/{repo}_{group}.json`
- **Scheduler logs**: `data/scheduler_logs/{timestamp}_{uuid}.json`

### Configuration Loading
- Configurations are loaded at service startup
- File watchers can be implemented for hot-reloading
- API endpoints provide runtime configuration updates

## Concurrency and Performance

### Concurrency Model
- **Global Limit**: `maxConcurrentScans` at service level
- **Per-Repository Limit**: `maxConcurrentScans` per repository
- **Queue-Based Execution**: Buffered channel for job queuing
- **Worker Pool**: Configurable number of worker goroutines

### Performance Optimizations
- **In-Memory Caching**: Active configurations cached in memory
- **Batch Processing**: Multiple jobs can be processed simultaneously
- **Load Balancing**: Prevents system overload through rate limiting
- **Priority Queuing**: Higher priority repositories get preference

### Monitoring and Observability
- **Real-time Metrics**: Queue size, active jobs, completion rates
- **Scheduler Logs**: Detailed decision logging for troubleshooting
- **WebSocket Updates**: Real-time status updates to frontend
- **Performance Metrics**: Scan duration, success rates, error tracking

## Error Handling and Recovery

### Job Failure Handling
- **Automatic Retry**: Configurable retry attempts for failed jobs
- **Error Logging**: Detailed error information in job records
- **Notification System**: Alerts for persistent failures
- **Manual Intervention**: UI for manual job retry/cancellation

### System Recovery
- **Graceful Shutdown**: Proper cleanup of active jobs on shutdown
- **State Persistence**: Job state survives service restarts
- **Orphan Detection**: Cleanup of stale job records
- **Configuration Validation**: Prevent invalid configurations

## Security Considerations

### Access Control
- **API Authentication**: Secure API endpoints
- **Role-Based Access**: Different permission levels for configuration
- **Audit Logging**: Track configuration changes
- **Input Validation**: Prevent malicious configuration injection

### Resource Protection
- **Rate Limiting**: Prevent abuse of scheduling APIs
- **Resource Quotas**: Limit per-repository resource usage
- **Isolation**: Separate job execution contexts
- **Monitoring**: Detect unusual scheduling patterns

## Future Enhancements

### Planned Features
- **Advanced Scheduling**: Cron-like scheduling expressions
- **Dependency Management**: Job dependencies and workflows
- **Resource Optimization**: Dynamic resource allocation
- **Machine Learning**: Predictive scheduling based on usage patterns
- **Multi-Tenant Support**: Organization-level isolation
- **Advanced Analytics**: Comprehensive reporting and insights

### Scalability Improvements
- **Distributed Scheduling**: Multi-node scheduler coordination
- **Database Backend**: Replace file-based storage with database
- **Message Queues**: External queue systems for job management
- **Microservices**: Split scheduler into dedicated services

## Implementation Requirements

### Backend Implementation

#### Core Service Requirements
```go
// Required service methods
type AutoScanServiceInterface interface {
    // Configuration Management
    CreateConfig(config *models.AutoScanConfig) error
    UpdateConfig(id string, config *models.AutoScanConfig) error
    GetConfig(id string) (*models.AutoScanConfig, error)
    ListConfigs() ([]*models.AutoScanConfig, error)
    DeleteConfig(id string) error

    // Job Management
    CreateJob(repositoryId, groupName string, priority int) (*models.AutoScanJob, error)
    GetJob(id string) (*models.AutoScanJob, error)
    ListJobs(filters JobFilters) ([]*models.AutoScanJob, error)
    CancelJob(id string) error

    // Continuous Scheduling
    GetContinuousConfig() models.ContinuousSchedulingConfig
    UpdateContinuousConfig(config models.ContinuousSchedulingConfig) error

    // Scheduler Logs
    GetSchedulerLogs(filters LogFilters) ([]*models.SchedulerLog, error)
    ClearSchedulerLogs() error

    // Statistics
    GetStats() (*models.SchedulerStats, error)
    GetQueueStatus() (*models.QueueStatus, error)
}
```

#### Required Data Models
```go
// Auto-scan configuration for a repository
type AutoScanConfig struct {
    ID                string                 `json:"id"`
    RepositoryID      string                 `json:"repositoryId"`
    Enabled           bool                   `json:"enabled"`
    Groups            []string               `json:"groups"`
    MaxConcurrentScans int                   `json:"maxConcurrentScans"`
    LoadBalancing     LoadBalancingConfig    `json:"loadBalancing"`
    Scheduling        SchedulingConfig       `json:"scheduling"`
    CreatedAt         time.Time              `json:"createdAt"`
    UpdatedAt         time.Time              `json:"updatedAt"`
}

// Load balancing configuration
type LoadBalancingConfig struct {
    SpreadAcrossDay     bool   `json:"spreadAcrossDay"`
    MinIntervalBetween  string `json:"minIntervalBetween"`  // Duration string (e.g., "10m")
    MaxScansPerHour     int    `json:"maxScansPerHour"`
}

// Scheduling configuration
type SchedulingConfig struct {
    Enabled     bool         `json:"enabled"`
    Priority    int          `json:"priority"`     // 1-10, higher = more priority
    TimeWindows []TimeWindow `json:"timeWindows"`
}

// Time window for scheduling
type TimeWindow struct {
    Days      []string `json:"days"`      // ["monday", "tuesday", ...]
    StartTime string   `json:"startTime"` // "09:00"
    EndTime   string   `json:"endTime"`   // "17:00"
}

// Auto-scan job
type AutoScanJob struct {
    ID           string    `json:"id"`
    RepositoryID string    `json:"repositoryId"`
    GroupName    string    `json:"groupName"`
    Status       JobStatus `json:"status"`
    CreatedAt    time.Time `json:"createdAt"`
    StartedAt    *time.Time `json:"startedAt,omitempty"`
    CompletedAt  *time.Time `json:"completedAt,omitempty"`
    ScheduledBy  string    `json:"scheduledBy"` // "continuous", "manual", "scheduled"
    Priority     int       `json:"priority"`
    Error        string    `json:"error,omitempty"`
    Progress     *JobProgress `json:"progress,omitempty"`
}

// Job progress information
type JobProgress struct {
    CurrentFile    string  `json:"currentFile"`
    FilesProcessed int     `json:"filesProcessed"`
    TotalFiles     int     `json:"totalFiles"`
    Percentage     float64 `json:"percentage"`
    Speed          string  `json:"speed"`
    ETA            string  `json:"eta"`
}

// Job status enumeration
type JobStatus string
const (
    JobStatusPending   JobStatus = "pending"
    JobStatusRunning   JobStatus = "running"
    JobStatusCompleted JobStatus = "completed"
    JobStatusFailed    JobStatus = "failed"
    JobStatusCancelled JobStatus = "cancelled"
)

// Continuous scheduling configuration
type ContinuousSchedulingConfig struct {
    Enabled              bool `json:"enabled"`
    TargetQueueSize      int  `json:"targetQueueSize"`
    CheckIntervalMinutes int  `json:"checkIntervalMinutes"`
    MaxConcurrentScans   int  `json:"maxConcurrentScans"`
}

// Scheduler log entry
type SchedulerLog struct {
    ID           string                 `json:"id"`
    Timestamp    time.Time              `json:"timestamp"`
    RepositoryID string                 `json:"repositoryId"`
    GroupName    string                 `json:"groupName"`
    Action       SchedulerAction        `json:"action"`
    Reason       string                 `json:"reason"`
    Details      map[string]interface{} `json:"details"`
}

// Scheduler action enumeration
type SchedulerAction string
const (
    ActionScheduled SchedulerAction = "scheduled"
    ActionSkipped   SchedulerAction = "skipped"
    ActionQueued    SchedulerAction = "queued"
    ActionBalanced  SchedulerAction = "load_balanced"
)

// Scheduler statistics
type SchedulerStats struct {
    TotalJobs        int                    `json:"totalJobs"`
    CompletedJobs    int                    `json:"completedJobs"`
    FailedJobs       int                    `json:"failedJobs"`
    AverageRunTime   time.Duration          `json:"averageRunTime"`
    JobsByRepository map[string]int         `json:"jobsByRepository"`
    JobsByStatus     map[JobStatus]int      `json:"jobsByStatus"`
    LastUpdated      time.Time              `json:"lastUpdated"`
}

// Queue status information
type QueueStatus struct {
    PendingJobs     int       `json:"pendingJobs"`
    RunningJobs     int       `json:"runningJobs"`
    TargetQueueSize int       `json:"targetQueueSize"`
    LastCheck       time.Time `json:"lastCheck"`
    NextCheck       time.Time `json:"nextCheck"`
}
```

#### Required API Controllers
```go
// Auto-scan configuration controller
type AutoScanConfigController struct {
    service AutoScanServiceInterface
}

// HTTP handlers
func (c *AutoScanConfigController) ListConfigs(w http.ResponseWriter, r *http.Request)
func (c *AutoScanConfigController) GetConfig(w http.ResponseWriter, r *http.Request)
func (c *AutoScanConfigController) CreateConfig(w http.ResponseWriter, r *http.Request)
func (c *AutoScanConfigController) UpdateConfig(w http.ResponseWriter, r *http.Request)
func (c *AutoScanConfigController) DeleteConfig(w http.ResponseWriter, r *http.Request)

// Auto-scan job controller
type AutoScanJobController struct {
    service AutoScanServiceInterface
}

func (c *AutoScanJobController) ListJobs(w http.ResponseWriter, r *http.Request)
func (c *AutoScanJobController) CreateJob(w http.ResponseWriter, r *http.Request)
func (c *AutoScanJobController) CancelJob(w http.ResponseWriter, r *http.Request)

// Continuous scheduling controller
type ContinuousSchedulingController struct {
    service AutoScanServiceInterface
}

func (c *ContinuousSchedulingController) GetConfig(w http.ResponseWriter, r *http.Request)
func (c *ContinuousSchedulingController) UpdateConfig(w http.ResponseWriter, r *http.Request)

// Scheduler logs controller
type SchedulerLogsController struct {
    service AutoScanServiceInterface
}

func (c *SchedulerLogsController) GetLogs(w http.ResponseWriter, r *http.Request)
func (c *SchedulerLogsController) ClearLogs(w http.ResponseWriter, r *http.Request)
```

#### WebSocket Implementation
```go
// WebSocket hub for real-time updates
type SchedulerHub struct {
    clients    map[*SchedulerClient]bool
    broadcast  chan []byte
    register   chan *SchedulerClient
    unregister chan *SchedulerClient
}

// WebSocket client
type SchedulerClient struct {
    hub  *SchedulerHub
    conn *websocket.Conn
    send chan []byte
}

// Message types for WebSocket communication
type WSMessage struct {
    Type string      `json:"type"`
    Data interface{} `json:"data"`
}

// WebSocket message types
const (
    WSJobStatusUpdate    = "job_status_update"
    WSJobProgressUpdate  = "job_progress_update"
    WSQueueStatusUpdate  = "queue_status_update"
    WSSchedulerLogUpdate = "scheduler_log_update"
)
```

### Frontend Implementation

#### Required React Components

##### 1. Repository Auto-Scan Configuration Component
```typescript
interface AutoScanConfigProps {
    repositoryId: string;
    onConfigChange?: (config: AutoScanConfig) => void;
}

interface AutoScanConfig {
    id: string;
    repositoryId: string;
    enabled: boolean;
    groups: string[];
    maxConcurrentScans: number;
    loadBalancing: LoadBalancingConfig;
    scheduling: SchedulingConfig;
}

const AutoScanConfigComponent: React.FC<AutoScanConfigProps> = ({
    repositoryId,
    onConfigChange
}) => {
    // Component implementation
    // - Enable/disable toggle
    // - Group selection multi-select
    // - Concurrent scans slider
    // - Load balancing settings form
    // - Time window configuration
    // - Priority setting
};
```

##### 2. Continuous Scheduling Dashboard Component
```typescript
interface ContinuousSchedulingProps {
    onConfigChange?: (config: ContinuousSchedulingConfig) => void;
}

interface ContinuousSchedulingConfig {
    enabled: boolean;
    targetQueueSize: number;
    checkIntervalMinutes: number;
    maxConcurrentScans: number;
}

const ContinuousSchedulingDashboard: React.FC<ContinuousSchedulingProps> = ({
    onConfigChange
}) => {
    // Component implementation
    // - Enable/disable toggle
    // - Target queue size input
    // - Check interval configuration
    // - Global concurrent scans setting
    // - Real-time queue status display
    // - Queue management controls
};
```

##### 3. Job Monitoring Dashboard Component
```typescript
interface JobMonitoringProps {
    repositoryFilter?: string;
    statusFilter?: JobStatus[];
    autoRefresh?: boolean;
}

interface AutoScanJob {
    id: string;
    repositoryId: string;
    groupName: string;
    status: JobStatus;
    createdAt: Date;
    startedAt?: Date;
    completedAt?: Date;
    scheduledBy: string;
    priority: number;
    error?: string;
    progress?: JobProgress;
}

const JobMonitoringDashboard: React.FC<JobMonitoringProps> = ({
    repositoryFilter,
    statusFilter,
    autoRefresh = true
}) => {
    // Component implementation
    // - Job list with filtering
    // - Real-time status updates
    // - Progress bars for running jobs
    // - Job creation form
    // - Job cancellation controls
    // - Export functionality
};
```

##### 4. Scheduler Logs Component
```typescript
interface SchedulerLogsProps {
    repositoryFilter?: string;
    actionFilter?: SchedulerAction[];
    dateRange?: [Date, Date];
}

interface SchedulerLog {
    id: string;
    timestamp: Date;
    repositoryId: string;
    groupName: string;
    action: SchedulerAction;
    reason: string;
    details: Record<string, any>;
}

const SchedulerLogsComponent: React.FC<SchedulerLogsProps> = ({
    repositoryFilter,
    actionFilter,
    dateRange
}) => {
    // Component implementation
    // - Log list with filtering
    // - Search functionality
    // - Export capabilities
    // - Log retention management
    // - Detailed log viewer
};
```

##### 5. Statistics Dashboard Component
```typescript
interface StatsDashboardProps {
    timeRange?: 'day' | 'week' | 'month';
}

interface SchedulerStats {
    totalJobs: number;
    completedJobs: number;
    failedJobs: number;
    averageRunTime: number;
    jobsByRepository: Record<string, number>;
    jobsByStatus: Record<JobStatus, number>;
    lastUpdated: Date;
}

const StatsDashboard: React.FC<StatsDashboardProps> = ({
    timeRange = 'week'
}) => {
    // Component implementation
    // - Job statistics charts
    // - Repository performance metrics
    // - Success/failure rates
    // - Performance trends
    // - System health indicators
};
```

#### Required Hooks and Services

##### WebSocket Hook
```typescript
interface UseSchedulerWebSocketOptions {
    onJobUpdate?: (job: AutoScanJob) => void;
    onQueueUpdate?: (status: QueueStatus) => void;
    onLogUpdate?: (log: SchedulerLog) => void;
}

const useSchedulerWebSocket = (options: UseSchedulerWebSocketOptions) => {
    // WebSocket connection management
    // Message handling and routing
    // Automatic reconnection
    // Connection status tracking
};
```

##### API Service
```typescript
class SchedulerApiService {
    // Configuration management
    async getConfigs(): Promise<AutoScanConfig[]>
    async getConfig(id: string): Promise<AutoScanConfig>
    async createConfig(config: Partial<AutoScanConfig>): Promise<AutoScanConfig>
    async updateConfig(id: string, config: Partial<AutoScanConfig>): Promise<AutoScanConfig>
    async deleteConfig(id: string): Promise<void>

    // Job management
    async getJobs(filters?: JobFilters): Promise<AutoScanJob[]>
    async createJob(repositoryId: string, groupName: string, priority?: number): Promise<AutoScanJob>
    async cancelJob(id: string): Promise<void>

    // Continuous scheduling
    async getContinuousConfig(): Promise<ContinuousSchedulingConfig>
    async updateContinuousConfig(config: ContinuousSchedulingConfig): Promise<ContinuousSchedulingConfig>

    // Logs and statistics
    async getLogs(filters?: LogFilters): Promise<SchedulerLog[]>
    async clearLogs(): Promise<void>
    async getStats(): Promise<SchedulerStats>
    async getQueueStatus(): Promise<QueueStatus>
}
```

#### UI/UX Requirements

##### Design Patterns
- **Consistent Styling**: Follow existing application design system
- **Responsive Design**: Mobile-friendly interfaces
- **Accessibility**: WCAG 2.1 AA compliance
- **Loading States**: Skeleton screens and progress indicators
- **Error Handling**: User-friendly error messages and recovery options

##### User Experience Features
- **Real-time Updates**: Live status updates without page refresh
- **Intuitive Navigation**: Clear information hierarchy
- **Bulk Operations**: Multi-select for batch operations
- **Search and Filtering**: Advanced filtering capabilities
- **Export Functionality**: CSV/JSON export for logs and statistics
- **Help Documentation**: Contextual help and tooltips

##### Performance Requirements
- **Fast Loading**: Initial page load under 2 seconds
- **Efficient Updates**: Minimal re-renders for real-time updates
- **Memory Management**: Proper cleanup of WebSocket connections
- **Caching**: Intelligent caching of configuration data
- **Pagination**: Handle large datasets efficiently

This comprehensive architecture document provides all the technical details needed to implement the auto-schedule and auto-scan system from scratch, including both backend services and frontend components.
