# Group Usage Tracking User Guide

This guide explains how to use the Group Usage Tracking feature to monitor where and how your groups are being used across different systems and repositories.

## Overview

The Group Usage Tracking feature allows you to:

- Configure multiple sources (Git repositories, APIs, file systems) to scan for group usage
- Automatically track where groups are referenced across your infrastructure
- Monitor usage patterns and identify dependencies
- Generate reports on group utilization
- Set up automated scanning schedules

## Getting Started

### 1. Configure Usage Sources

Before you can track group usage, you need to configure one or more usage sources. These are the systems that will be scanned for group references.

#### Supported Source Types

- **Git Repositories**: Scan code repositories for group references in configuration files
- **REST APIs**: Query APIs that return data containing group information
- **File Systems**: Scan local or network file systems for group usage

### 2. Access the Usage Tracking Interface

Navigate to the Settings page and select "Usage Sources" to manage your tracking configuration.

## Managing Usage Sources

### Creating a Git Repository Source

1. Click "Add Usage Source"
2. Select "Git Repository" as the source type
3. Fill in the required information:
   - **Name**: A descriptive name for this source
   - **Repository URL**: The Git repository URL (HTTPS or SSH)
   - **Branch**: The branch to scan (default: main)
   - **Authentication**: Choose the appropriate authentication method
   - **Scan Frequency**: How often to scan this source
   - **File Patterns**: Specify which files to include/exclude

#### Authentication Options

**No Authentication**
- Use for public repositories

**Personal Access Token**
- Generate a token in your Git provider (GitHub, GitLab, etc.)
- Paste the token in the Token field
- Recommended for private repositories

**Username/Password**
- Use your Git credentials
- Less secure than tokens

#### File Pattern Examples

**Include Patterns:**
- `*.yaml` - All YAML files
- `*.json` - All JSON files
- `config/**/*.yml` - YAML files in config directories
- `**/*config*` - Any file with "config" in the name

**Exclude Patterns:**
- `*.log` - Exclude log files
- `node_modules/**` - Exclude dependency directories
- `*.tmp` - Exclude temporary files

### Creating an API Source

1. Click "Add Usage Source"
2. Select "REST API" as the source type
3. Configure the API details:
   - **Name**: Descriptive name
   - **Endpoint URL**: The API endpoint to query
   - **HTTP Method**: Usually GET
   - **Authentication**: API key, bearer token, or basic auth
   - **Headers**: Any required HTTP headers
   - **Response Path**: JSON path to the data containing group references

#### API Response Processing

The system expects API responses to contain group references in a structured format. You can specify a JSON path to extract the relevant data.

Example API response:
```json
{
  "data": {
    "permissions": [
      {
        "group": "admin-group",
        "resource": "/api/users"
      }
    ]
  }
}
```

JSON Path: `$.data.permissions[*].group`

### Creating a File System Source

1. Click "Add Usage Source"
2. Select "File System" as the source type
3. Configure the file system details:
   - **Name**: Descriptive name
   - **Base Path**: Root directory to scan
   - **File Patterns**: Include/exclude patterns
   - **Recursive**: Whether to scan subdirectories

## Tracking Group Usage

### Manual Scanning

To immediately scan for a specific group's usage:

1. Navigate to the Groups page
2. Select the group you want to track
3. Click on the "Usage" tab
4. Click "Scan Now" to trigger an immediate scan

### Viewing Usage Results

The Usage tab shows:

- **Source**: Which system the usage was found in
- **File/Location**: Specific file or API endpoint
- **Line Number**: For file-based sources, the exact line
- **Context**: Surrounding text showing how the group is used
- **Last Detected**: When this usage was last confirmed

### Understanding Usage Types

**Exact Match**: The group name appears exactly as configured
**Partial Match**: The group name appears as part of a larger string
**Reference**: The group is referenced indirectly (e.g., in a variable)

## Automated Scanning

### Scan Schedules

Each usage source can be configured with an automatic scan frequency:

- **5 minutes**: For rapidly changing sources
- **15 minutes**: For moderately active sources
- **1 hour**: For stable sources with occasional changes
- **6 hours**: For relatively static sources
- **24 hours**: For archival or backup sources

### Monitoring Scan Status

You can monitor the status of automated scans:

1. Go to Settings > Usage Sources
2. Check the "Last Scan" column for each source
3. Click on a source to see detailed scan history
4. View any scan errors or warnings

## Best Practices

### Source Configuration

1. **Start Small**: Begin with one or two important sources
2. **Use Specific Patterns**: Avoid overly broad file patterns that might scan unnecessary files
3. **Set Appropriate Frequencies**: Don't scan too frequently for sources that rarely change
4. **Test Connections**: Always test source connections before saving

### Performance Optimization

1. **Exclude Large Directories**: Use exclude patterns for build outputs, dependencies, and logs
2. **Limit Scan Scope**: Focus on configuration and deployment files
3. **Monitor Resource Usage**: Check system performance during scans
4. **Stagger Scan Times**: Don't schedule all sources to scan simultaneously

### Security Considerations

1. **Use Tokens Over Passwords**: Prefer personal access tokens for Git authentication
2. **Limit Token Permissions**: Grant only the minimum required permissions
3. **Rotate Credentials**: Regularly update authentication credentials
4. **Monitor Access**: Review which systems have access to your repositories

## Troubleshooting

### Common Issues

**Source Connection Failed**
- Verify the URL is correct and accessible
- Check authentication credentials
- Ensure network connectivity
- Review firewall settings

**No Usage Found**
- Verify the group name is spelled correctly
- Check file patterns include the relevant files
- Review exclude patterns aren't too restrictive
- Confirm the group is actually used in the source

**Scan Taking Too Long**
- Reduce the scope with more specific file patterns
- Exclude large directories
- Consider splitting large sources into smaller ones
- Check system resources

**Authentication Errors**
- Verify credentials are correct and not expired
- Check token permissions
- Ensure the authentication method matches the source requirements

### Getting Help

If you encounter issues:

1. Check the scan logs in the Usage Sources settings
2. Review the system logs for detailed error messages
3. Test source connections individually
4. Contact your system administrator

## Advanced Features

### Custom File Patterns

You can use advanced glob patterns for precise file selection:

- `**/*.{yml,yaml}` - YAML files in any directory
- `**/config/**/*.json` - JSON files in config directories
- `!**/test/**` - Exclude test directories
- `{dev,prod,staging}/**/*.env` - Environment files in specific directories

### API Response Filtering

For complex API responses, you can use JSONPath expressions:

- `$.data[*].groups[*]` - All groups in a nested structure
- `$..group` - All "group" fields at any level
- `$.permissions[?(@.type=='group')].name` - Conditional filtering

### Integration with CI/CD

You can integrate usage tracking with your deployment pipeline:

1. Trigger scans after deployments
2. Generate usage reports for change reviews
3. Alert on new or removed group usage
4. Validate group references before deployment

## Reporting and Analytics

### Usage Reports

Generate reports showing:
- Groups with the most usage
- Sources with the most group references
- Usage trends over time
- Orphaned or unused groups

### Export Options

Usage data can be exported in various formats:
- CSV for spreadsheet analysis
- JSON for programmatic processing
- PDF for documentation and sharing

### Dashboard Integration

Usage metrics can be displayed on dashboards showing:
- Total groups tracked
- Active vs. inactive groups
- Scan success rates
- System health metrics
