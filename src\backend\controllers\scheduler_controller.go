package controllers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/gin-gonic/gin"
)

// SchedulerController handles scheduler monitoring and management
type SchedulerController struct {
	schedulerService    *services.SchedulerService
	usageScanner        services.UsageScanner
	autoScanService     *services.AutoScanService
	workerPool          *services.ScanWorkerPool
	dataProcessor       services.DataProcessorInterface
	scanLogger          *services.ScanLogger
	progressBroadcaster *services.ProgressBroadcaster
	repoManager         interface {
		GetConfiguration(id string) (models.RepositoryConfig, error)
	}
}

// NewSchedulerController creates a new scheduler controller
func NewSchedulerController(
	schedulerService *services.SchedulerService,
	usageScanner services.UsageScanner,
	autoScanService *services.AutoScanService,
	workerPool *services.ScanWorkerPool,
	dataProcessor services.DataProcessorInterface,
	scanLogger *services.ScanLogger,
	progressBroadcaster *services.ProgressBroadcaster,
	repoManager interface {
		GetConfiguration(id string) (models.RepositoryConfig, error)
	},
) *SchedulerController {
	return &SchedulerController{
		schedulerService:    schedulerService,
		usageScanner:        usageScanner,
		autoScanService:     autoScanService,
		workerPool:          workerPool,
		dataProcessor:       dataProcessor,
		scanLogger:          scanLogger,
		progressBroadcaster: progressBroadcaster,
		repoManager:         repoManager,
	}
}

// broadcastSchedulerEvent broadcasts a scheduler task state change event to admin subscribers
func (c *SchedulerController) broadcastSchedulerEvent(taskID, taskType, action, message string, success bool, err error) {
	if c.progressBroadcaster == nil {
		return
	}

	eventType := "scheduler_refresh"
	switch action {
	case "cancel":
		eventType = "task_cancelled"
	case "restart":
		eventType = "task_restarted"
	case "retry":
		eventType = "task_restarted"
	case "remove":
		eventType = "task_removed"
	case "pause":
		eventType = "task_paused"
	case "resume":
		eventType = "task_resumed"
	case "trigger":
		eventType = "task_triggered"
	}

	update := models.RealTimeProgressUpdate{
		Timestamp: time.Now(),
		EventType: eventType,
		Message:   message,
		TaskID:    taskID,
		TaskType:  taskType,
		Action:    action,
		Success:   success,
	}

	if err != nil {
		update.Error = err.Error()
		update.Success = false
	}

	// Send the update through the progress broadcaster's update channel
	// This will automatically broadcast to admin subscribers via the processUpdates goroutine
	select {
	case c.progressBroadcaster.GetUpdateChannel() <- update:
		// Successfully sent update
	default:
		// Channel is full, log warning but don't block
		log.Printf("Warning: Progress broadcaster channel is full, skipping scheduler event for task %s", taskID)
	}

	log.Printf("Broadcasted scheduler event: %s for task %s (%s) - success: %t", eventType, taskID, taskType, success)
}

// RegisterRoutes registers the scheduler controller routes
func (c *SchedulerController) RegisterRoutes(router *gin.RouterGroup) {
	schedulerRoutes := router.Group("/scheduler")
	{
		schedulerRoutes.GET("/status", c.GetSchedulerStatus)
		schedulerRoutes.GET("/overview", c.GetSchedulerOverview)
		schedulerRoutes.GET("/planned-work", c.GetPlannedWork)
		schedulerRoutes.GET("/active-tasks", c.GetActiveTasks)
		schedulerRoutes.GET("/failed-tasks", c.GetFailedTasks)
		schedulerRoutes.GET("/completed-tasks", c.GetCompletedTasks)

		// Interrupted scans management endpoints
		schedulerRoutes.GET("/interrupted-scans", c.GetInterruptedScans)
		schedulerRoutes.GET("/interrupted-scans/stats", c.GetInterruptedScansStats)

		// Task control endpoints
		controlRoutes := schedulerRoutes.Group("/control")
		{
			controlRoutes.POST("/pause", c.PauseScheduler)
			controlRoutes.POST("/resume", c.ResumeScheduler)
			controlRoutes.POST("/trigger", c.TriggerTask)
			controlRoutes.POST("/retry", c.RetryTask)
			controlRoutes.POST("/cancel", c.CancelTask)

			// Interrupted scans management
			controlRoutes.POST("/restart-interrupted", c.RestartInterruptedScans)
			controlRoutes.POST("/clear-failed", c.ClearFailedScans)
			controlRoutes.POST("/restart-scan", c.RestartSpecificScan)
			controlRoutes.DELETE("/remove-scan", c.RemoveSpecificScan)
			controlRoutes.POST("/cleanup-stale", c.CleanupStaleTasks)
			controlRoutes.DELETE("/all-tasks", c.DeleteAllScheduledTasks)

		}

		// Auto-scan configuration management endpoints
		autoScanRoutes := schedulerRoutes.Group("/auto-scan")
		{
			autoScanRoutes.GET("/configs", c.GetAutoScanConfigs)
			autoScanRoutes.GET("/configs/:repoId", c.GetAutoScanConfig)
			autoScanRoutes.POST("/configs", c.CreateAutoScanConfig)
			autoScanRoutes.PUT("/configs/:repoId", c.UpdateAutoScanConfig)
			autoScanRoutes.DELETE("/configs/:repoId", c.DeleteAutoScanConfig)
			autoScanRoutes.POST("/enable/:repoId", c.EnableAutoScan)
			autoScanRoutes.POST("/disable/:repoId", c.DisableAutoScan)
			autoScanRoutes.GET("/logs", c.GetSchedulerLogs)
			autoScanRoutes.DELETE("/logs", c.ClearSchedulerLogs)

			// Continuous scheduling configuration
			autoScanRoutes.GET("/continuous-config", c.GetContinuousConfig)
			autoScanRoutes.PUT("/continuous-config", c.UpdateContinuousConfig)
		}
	}
}

// DeleteAllScheduledTasks deletes all pending and scheduled auto-scan tasks
func (c *SchedulerController) DeleteAllScheduledTasks(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	// Call the service method to delete all scheduled tasks
	deletedCount, err := c.autoScanService.DeleteAllScheduledTasks()
	if err != nil {
		log.Printf("Error during bulk task deletion: %v", err)
		ctx.JSON(http.StatusInternalServerError, models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to delete all scheduled tasks: %v", err),
		})
		return
	}

	message := fmt.Sprintf("Successfully deleted %d scheduled tasks", deletedCount)
	log.Printf("Bulk deletion completed: %s", message)

	// Broadcast scheduler event for bulk deletion
	c.broadcastSchedulerEvent("bulk-deletion", "auto-scan", "delete", message, true, nil)

	ctx.JSON(http.StatusOK, models.TaskControlResponse{
		Success: true,
		Message: message,
		TaskID:  "bulk-deletion",
	})
}

// GetContinuousConfig returns the current continuous scheduling configuration
func (c *SchedulerController) GetContinuousConfig(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	config := c.autoScanService.GetContinuousConfig()
	ctx.JSON(http.StatusOK, gin.H{
		"config": config,
	})
}

// UpdateContinuousConfig updates the continuous scheduling configuration
func (c *SchedulerController) UpdateContinuousConfig(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	var config models.ContinuousSchedulingConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Invalid request body: %v", err),
		})
		return
	}

	if err := c.autoScanService.UpdateContinuousConfig(config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Failed to update configuration: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Continuous scheduling configuration updated successfully",
		"config":  config,
	})
}

// Auto-scan configuration management endpoints

// GetAutoScanConfigs returns all auto-scan configurations
func (c *SchedulerController) GetAutoScanConfigs(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	configs := c.autoScanService.GetAllConfigs()

	// Resolve repository names
	configsWithNames := make(map[string]interface{})
	for repoID, config := range configs {
		configData := map[string]interface{}{
			"id":                 config.ID,
			"repoId":             config.RepoID,
			"repositoryName":     repoID, // Default to ID
			"enabled":            config.Enabled,
			"frequency":          config.Frequency,
			"timeWindow":         config.TimeWindow,
			"maxConcurrentScans": config.MaxConcurrentScans,
			"scanAllGroups":      config.ScanAllGroups,
			"targetGroups":       config.TargetGroups,
			"loadBalancing":      config.LoadBalancing,
			"notificationConfig": config.NotificationConfig,
			"priorityConfig":     config.PriorityConfig,
			"createdAt":          config.CreatedAt,
			"updatedAt":          config.UpdatedAt,
			"lastScanTime":       config.LastScanTime,
			"nextScanTime":       config.NextScanTime,
		}

		// Resolve repository name
		if c.repoManager != nil {
			if repoConfig, err := c.repoManager.GetConfiguration(repoID); err == nil {
				configData["repositoryName"] = repoConfig.Name
			}
		}

		configsWithNames[repoID] = configData
	}

	ctx.JSON(http.StatusOK, gin.H{
		"configs": configsWithNames,
		"total":   len(configsWithNames),
	})
}

// GetAutoScanConfig returns a specific auto-scan configuration
func (c *SchedulerController) GetAutoScanConfig(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	config, err := c.autoScanService.GetConfig(repoID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": fmt.Sprintf("Configuration not found for repository %s", repoID),
		})
		return
	}

	// Resolve repository name
	repositoryName := repoID
	if c.repoManager != nil {
		if repoConfig, err := c.repoManager.GetConfiguration(repoID); err == nil {
			repositoryName = repoConfig.Name
		}
	}

	configData := map[string]interface{}{
		"id":                 config.ID,
		"repoId":             config.RepoID,
		"repositoryName":     repositoryName,
		"enabled":            config.Enabled,
		"frequency":          config.Frequency,
		"timeWindow":         config.TimeWindow,
		"maxConcurrentScans": config.MaxConcurrentScans,
		"scanAllGroups":      config.ScanAllGroups,
		"targetGroups":       config.TargetGroups,
		"loadBalancing":      config.LoadBalancing,
		"notificationConfig": config.NotificationConfig,
		"priorityConfig":     config.PriorityConfig,
		"createdAt":          config.CreatedAt,
		"updatedAt":          config.UpdatedAt,
		"lastScanTime":       config.LastScanTime,
		"nextScanTime":       config.NextScanTime,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"config": configData,
	})
}

// CreateAutoScanConfig creates a new auto-scan configuration
func (c *SchedulerController) CreateAutoScanConfig(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	var config models.AutoScanConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Invalid request body: %v", err),
		})
		return
	}

	if err := c.autoScanService.CreateConfig(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Failed to create configuration: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"message": "Auto-scan configuration created successfully",
		"config":  config,
	})
}

// UpdateAutoScanConfig updates an existing auto-scan configuration
func (c *SchedulerController) UpdateAutoScanConfig(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	var config models.AutoScanConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Invalid request body: %v", err),
		})
		return
	}

	// Ensure the repository ID matches
	config.RepoID = repoID

	if err := c.autoScanService.UpdateConfig(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Failed to update configuration: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Auto-scan configuration updated successfully",
		"config":  config,
	})
}

// DeleteAutoScanConfig deletes an auto-scan configuration
func (c *SchedulerController) DeleteAutoScanConfig(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	if err := c.autoScanService.DeleteConfig(repoID); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Failed to delete configuration: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Auto-scan configuration deleted successfully",
	})
}

// EnableAutoScan enables auto-scanning for a repository with default settings
func (c *SchedulerController) EnableAutoScan(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	if err := c.autoScanService.EnableAutoScan(repoID); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Failed to enable auto-scan: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Auto-scan enabled successfully for repository",
		"repoId":  repoID,
	})
}

// DisableAutoScan disables auto-scanning for a repository
func (c *SchedulerController) DisableAutoScan(ctx *gin.Context) {
	repoID := ctx.Param("repoId")
	if repoID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	if err := c.autoScanService.DisableAutoScan(repoID); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Failed to disable auto-scan: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Auto-scan disabled successfully for repository",
		"repoId":  repoID,
	})
}

// GetSchedulerLogs returns scheduler logs with optional filtering
func (c *SchedulerController) GetSchedulerLogs(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	// Parse query parameters for filtering
	var filter models.SchedulerLogFilter

	// Parse time range
	if startTime := ctx.Query("startTime"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			filter.StartTime = &t
		}
	}
	if endTime := ctx.Query("endTime"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			filter.EndTime = &t
		}
	}

	// Parse other filters
	if level := ctx.Query("level"); level != "" {
		logLevel := models.SchedulerLogLevel(level)
		filter.Level = &logLevel
	}
	if action := ctx.Query("action"); action != "" {
		logAction := models.SchedulerLogAction(action)
		filter.Action = &logAction
	}
	filter.RepoID = ctx.Query("repoId")
	filter.GroupName = ctx.Query("groupName")
	filter.SearchText = ctx.Query("search")

	// Parse pagination
	if limit := ctx.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 {
			filter.Limit = l
		}
	}
	if offset := ctx.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil && o >= 0 {
			filter.Offset = o
		}
	}

	logs, err := c.autoScanService.GetSchedulerLogs(&filter)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get scheduler logs: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"logs":  logs,
		"total": len(logs),
	})
}

// ClearSchedulerLogs clears all scheduler logs
func (c *SchedulerController) ClearSchedulerLogs(ctx *gin.Context) {
	if c.autoScanService == nil {
		ctx.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Auto-scan service not available",
		})
		return
	}

	c.autoScanService.ClearSchedulerLogs()

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Scheduler logs cleared successfully",
	})
}

// CleanupStaleTasks manually cleans up stale/orphaned tasks
func (c *SchedulerController) CleanupStaleTasks(ctx *gin.Context) {
	log.Println("Manual stale task cleanup requested")

	var totalCleaned int
	var errors []string

	// Clean up stale scan tasks
	if c.usageScanner != nil {
		cleaned, err := c.usageScanner.CleanupStaleTasks()
		if err != nil {
			log.Printf("Error cleaning up stale scan tasks: %v", err)
			errors = append(errors, fmt.Sprintf("Scan tasks: %v", err))
		} else {
			totalCleaned += cleaned
			log.Printf("Cleaned up %d stale scan tasks", cleaned)
		}
	}

	// TODO: Add cleanup for other task types (reports, auto-scans) when implemented

	response := gin.H{
		"success":      len(errors) == 0,
		"totalCleaned": totalCleaned,
		"message":      fmt.Sprintf("Cleaned up %d stale tasks", totalCleaned),
		"timestamp":    time.Now().Format(time.RFC3339),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["message"] = fmt.Sprintf("Partially completed: cleaned %d tasks with %d errors", totalCleaned, len(errors))
	}

	ctx.JSON(http.StatusOK, response)
}

// CancelTask cancels a running task
func (c *SchedulerController) CancelTask(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	if request.TaskID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	log.Printf("Cancelling task: %s (type: %s)", request.TaskID, request.TaskType)

	var err error
	var message string

	// Route cancellation request based on task type or ID pattern
	switch {
	case request.TaskType == "usage_scan" || strings.Contains(request.TaskID, ":"):
		// Usage scan tasks have format "groupName:repoID" or "groupName:repoID:timestamp"
		parts := strings.Split(request.TaskID, ":")
		if len(parts) < 2 {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid usage scan task ID format. Expected 'groupName:repoID' or 'groupName:repoID:timestamp'"})
			return
		}
		// Extract groupName and repoID (ignore timestamp if present)
		groupName, repoID := parts[0], parts[1]
		err = c.usageScanner.CancelScan(groupName, repoID)
		if err == nil {
			message = fmt.Sprintf("Successfully cancelled usage scan for group '%s' in repository '%s'", groupName, repoID)
		}

	case request.TaskType == "report" || request.TaskType == "scheduled_report":
		// TODO: Implement report task cancellation when report service supports it
		err = fmt.Errorf("report task cancellation not yet implemented")

	case request.TaskType == "auto-scan" || request.TaskType == "auto_scan":
		// Cancel auto-scan task
		if c.autoScanService != nil {
			err = c.autoScanService.CancelTask(request.TaskID)
			if err == nil {
				message = fmt.Sprintf("Successfully cancelled auto-scan task '%s'", request.TaskID)
			}
		} else {
			err = fmt.Errorf("auto-scan service not available")
		}

	default:
		// Try to infer task type from ID or attempt generic cancellation
		if strings.Contains(request.TaskID, ":") {
			// Assume it's a usage scan
			parts := strings.Split(request.TaskID, ":")
			if len(parts) == 2 {
				groupName, repoID := parts[0], parts[1]
				err = c.usageScanner.CancelScan(groupName, repoID)
				if err == nil {
					message = fmt.Sprintf("Successfully cancelled usage scan for group '%s' in repository '%s'", groupName, repoID)
				}
			} else {
				err = fmt.Errorf("unknown task ID format: %s", request.TaskID)
			}
		} else {
			err = fmt.Errorf("unknown task type '%s' or unable to determine task type from ID '%s'", request.TaskType, request.TaskID)
		}
	}

	// Prepare response
	response := models.TaskControlResponse{
		TaskID: request.TaskID,
	}

	if err != nil {
		response.Success = false
		response.Message = fmt.Sprintf("Failed to cancel task: %s", err.Error())
		log.Printf("Failed to cancel task %s: %v", request.TaskID, err)

		// Broadcast scheduler event for failed cancellation
		c.broadcastSchedulerEvent(request.TaskID, request.TaskType, "cancel", response.Message, false, err)

		// Return appropriate HTTP status based on error type
		errorMsg := err.Error()
		switch {
		case strings.Contains(errorMsg, "not found") || strings.Contains(errorMsg, "no scan in progress"):
			ctx.JSON(http.StatusNotFound, response)
		case strings.Contains(errorMsg, "already cancelled"):
			response.Message = fmt.Sprintf("Task %s has already been cancelled", request.TaskID)
			ctx.JSON(http.StatusConflict, response)
		case strings.Contains(errorMsg, "not yet implemented"):
			ctx.JSON(http.StatusNotImplemented, response)
		case strings.Contains(errorMsg, "invalid") || strings.Contains(errorMsg, "format"):
			ctx.JSON(http.StatusBadRequest, response)
		default:
			ctx.JSON(http.StatusInternalServerError, response)
		}
		return
	}

	response.Success = true
	response.Message = message
	log.Printf("Successfully cancelled task: %s", request.TaskID)

	// Broadcast scheduler event for successful cancellation
	c.broadcastSchedulerEvent(request.TaskID, request.TaskType, "cancel", message, true, nil)

	ctx.JSON(http.StatusOK, response)
}

// RemoveSpecificScan removes a specific scan and its data by ID
func (c *SchedulerController) RemoveSpecificScan(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Parse task ID to extract groupName and repoID
	// Expected format: "groupName:repoID"
	parts := strings.Split(request.TaskID, ":")
	if len(parts) != 2 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid task ID format. Expected format: 'groupName:repoID'",
		})
		return
	}

	groupName := parts[0]
	repoID := parts[1]

	log.Printf("Remove specific scan request received for group: %s, repo: %s", groupName, repoID)

	// Check if scan exists
	currentStatus, err := c.usageScanner.GetScanStatus(groupName, repoID)
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Scan not found for group '%s' in repository '%s'", groupName, repoID),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusNotFound, response)
		return
	}

	// If scan is currently in progress, cancel it first
	if currentStatus.InProgress {
		log.Printf("Canceling current scan for group %s in repo %s before removal", groupName, repoID)
		if err := c.usageScanner.CancelScan(groupName, repoID); err != nil {
			log.Printf("Warning: Failed to cancel current scan: %v", err)
		}
		// Wait a moment for cancellation to take effect
		time.Sleep(1 * time.Second)
	}

	// Clear/remove the scan data
	if err := c.usageScanner.ClearUsageResults(groupName, repoID); err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to remove scan data: %v", err),
			TaskID:  request.TaskID,
		}

		// Broadcast scheduler event for failed removal
		c.broadcastSchedulerEvent(request.TaskID, "usage_scan", "remove", response.Message, false, err)

		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	response := models.TaskControlResponse{
		Success: true,
		Message: fmt.Sprintf("Successfully removed scan for group '%s' in repository '%s'", groupName, repoID),
		TaskID:  request.TaskID,
	}

	log.Printf("Successfully removed scan for group %s in repo %s", groupName, repoID)

	// Broadcast scheduler event for successful removal
	c.broadcastSchedulerEvent(request.TaskID, "usage_scan", "remove", response.Message, true, nil)

	ctx.JSON(http.StatusOK, response)
}

// GetSchedulerStatus returns the overall status of all scheduler services
func (c *SchedulerController) GetSchedulerStatus(ctx *gin.Context) {
	status := models.SchedulerStatus{
		ReportScheduler: c.getReportSchedulerStatus(),
		UsageScanner:    c.getUsageScannerStatus(),
		AutoScanService: c.getAutoScanServiceStatus(),
		LastUpdated:     time.Now().Format(time.RFC3339),
	}

	// Determine overall health
	status.OverallHealth = c.calculateOverallHealth(status)

	ctx.JSON(http.StatusOK, status)
}

// GetSchedulerOverview returns summary statistics for the dashboard
func (c *SchedulerController) GetSchedulerOverview(ctx *gin.Context) {
	overview := models.SchedulerOverview{
		TotalPlannedTasks: c.countPlannedTasks(),
		ActiveTasks:       c.countActiveTasks(),
		FailedTasks:       c.countFailedTasks(),
		CompletedToday:    c.countCompletedToday(),
		SuccessRate:       c.calculateSuccessRate(),
		AverageTaskTime:   c.calculateAverageTaskTime(),
	}

	ctx.JSON(http.StatusOK, overview)
}

// GetPlannedWork returns upcoming scheduled tasks from all systems
func (c *SchedulerController) GetPlannedWork(ctx *gin.Context) {
	var plannedTasks []models.PlannedTask

	// Get planned report tasks
	reportTasks := c.getPlannedReportTasks()
	plannedTasks = append(plannedTasks, reportTasks...)

	// Get planned scan tasks
	scanTasks := c.getPlannedScanTasks()
	plannedTasks = append(plannedTasks, scanTasks...)

	// Get planned auto-scan tasks
	autoScanTasks := c.getPlannedAutoScanTasks()
	plannedTasks = append(plannedTasks, autoScanTasks...)

	// Sort by scheduled time
	c.sortPlannedTasksByTime(plannedTasks)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": plannedTasks,
		"total": len(plannedTasks),
	})
}

// GetActiveTasks returns currently running tasks with progress
func (c *SchedulerController) GetActiveTasks(ctx *gin.Context) {
	var activeTasks []models.ActiveTask

	// Get active report tasks
	reportTasks := c.getActiveReportTasks()
	activeTasks = append(activeTasks, reportTasks...)

	// Get active scan tasks
	scanTasks := c.getActiveScanTasks()
	activeTasks = append(activeTasks, scanTasks...)

	// Get active auto-scan tasks
	autoScanTasks := c.getActiveAutoScanTasks()
	activeTasks = append(activeTasks, autoScanTasks...)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": activeTasks,
		"total": len(activeTasks),
	})
}

// GetFailedTasks returns failed tasks with retry information
func (c *SchedulerController) GetFailedTasks(ctx *gin.Context) {
	var failedTasks []models.FailedTask

	// Get failed report tasks
	reportTasks := c.getFailedReportTasks()
	failedTasks = append(failedTasks, reportTasks...)

	// Get failed scan tasks
	scanTasks := c.getFailedScanTasks()
	failedTasks = append(failedTasks, scanTasks...)

	// Get failed auto-scan tasks
	autoScanTasks := c.getFailedAutoScanTasks()
	failedTasks = append(failedTasks, autoScanTasks...)

	// Sort by failed time (most recent first)
	c.sortFailedTasksByTime(failedTasks)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": failedTasks,
		"total": len(failedTasks),
	})
}

// GetCompletedTasks returns completed tasks with completion information
func (c *SchedulerController) GetCompletedTasks(ctx *gin.Context) {
	var completedTasks []models.CompletedTask

	// Get completed scan tasks
	scanTasks := c.getCompletedScanTasks()
	completedTasks = append(completedTasks, scanTasks...)

	// Get completed report tasks
	reportTasks := c.getCompletedReportTasks()
	completedTasks = append(completedTasks, reportTasks...)

	// Get completed auto-scan tasks
	autoScanTasks := c.getCompletedAutoScanTasks()
	completedTasks = append(completedTasks, autoScanTasks...)

	// Sort by completion time (most recent first)
	c.sortCompletedTasksByTime(completedTasks)

	ctx.JSON(http.StatusOK, gin.H{
		"tasks": completedTasks,
		"total": len(completedTasks),
	})
}

// PauseScheduler pauses scheduler services
func (c *SchedulerController) PauseScheduler(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	response := models.TaskControlResponse{
		Success: false,
		Message: "Pause functionality not yet implemented",
	}

	// TODO: Implement pause functionality for each service
	log.Printf("Pause request received for service: %s", request.Service)

	ctx.JSON(http.StatusOK, response)
}

// ResumeScheduler resumes scheduler services
func (c *SchedulerController) ResumeScheduler(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	response := models.TaskControlResponse{
		Success: false,
		Message: "Resume functionality not yet implemented",
	}

	// TODO: Implement resume functionality for each service
	log.Printf("Resume request received for service: %s", request.Service)

	ctx.JSON(http.StatusOK, response)
}

// TriggerTask manually triggers a specific task
func (c *SchedulerController) TriggerTask(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	log.Printf("Trigger request received for task: %s, type: %s, service: %s", request.TaskID, request.TaskType, request.Service)

	// Handle different task types
	switch request.Service {
	case "usage", "auto-scan":
		c.triggerScheduledScan(ctx, request)
		return
	case "report":
		c.triggerScheduledReport(ctx, request)
		return
	default:
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Trigger not supported for service: %s", request.Service),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusBadRequest, response)
		return
	}
}

// RetryTask retries a failed task
func (c *SchedulerController) RetryTask(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	log.Printf("Retry request received for task: %s, type: %s, service: %s", request.TaskID, request.TaskType, request.Service)

	// Handle different task types
	switch request.Service {
	case "usage":
		c.retryUsageScan(ctx, request)
		return
	case "auto-scan":
		c.retryAutoScan(ctx, request)
		return
	case "report":
		c.retryReport(ctx, request)
		return
	default:
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Retry not supported for service: %s", request.Service),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusBadRequest, response)
		return
	}
}

// retryUsageScan retries a failed usage scan
func (c *SchedulerController) retryUsageScan(ctx *gin.Context, request models.TaskControlRequest) {
	// Parse task ID to extract group name and repo ID (format: GroupName:RepoID)
	parts := strings.Split(request.TaskID, ":")
	if len(parts) < 2 {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Invalid task ID format: %s. Expected format: GroupName:RepoID", request.TaskID),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusBadRequest, response)
		return
	}

	groupName := parts[0]
	repoID := parts[1]

	log.Printf("Retrying usage scan for group %s in repo %s", groupName, repoID)

	// Clear any failed status first to prepare for retry
	if err := c.usageScanner.ClearFailedStatus(groupName, repoID); err != nil {
		log.Printf("Warning: Failed to clear failed status for retried scan %s:%s: %v", groupName, repoID, err)
		// Continue with retry even if clearing failed status fails
	}

	// Cancel any existing scan to prevent conflicts
	if err := c.usageScanner.CancelScan(groupName, repoID); err != nil {
		log.Printf("Warning: Failed to cancel existing scan for group %s in repo %s: %v", groupName, repoID, err)
		// Continue with retry even if cancellation fails - the new scan will handle conflicts
	}

	// Wait for cancellation to take effect and any cleanup to complete
	time.Sleep(500 * time.Millisecond)

	// Create a new scan request
	scanRequest := models.UsageScanRequest{
		GroupName: groupName,
		RepoID:    repoID,
		Force:     true, // Force retry even if recently scanned
	}

	// Start the retry scan
	scanCtx := context.Background()
	actualScanID, err := c.usageScanner.ScanGroupUsage(scanCtx, scanRequest)
	if err != nil {
		// Provide more specific error messages based on error type
		var errorMessage string
		if strings.Contains(err.Error(), "authentication") || strings.Contains(err.Error(), "auth") {
			errorMessage = fmt.Sprintf("Authentication failed for group '%s' in repository '%s'. Please check repository credentials.", groupName, repoID)
		} else if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "network") {
			errorMessage = fmt.Sprintf("Network connection failed for group '%s' in repository '%s'. Please check network connectivity.", groupName, repoID)
		} else if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "404") {
			errorMessage = fmt.Sprintf("Group '%s' or repository '%s' not found. The resource may have been deleted.", groupName, repoID)
		} else if strings.Contains(err.Error(), "permission") || strings.Contains(err.Error(), "forbidden") {
			errorMessage = fmt.Sprintf("Permission denied for group '%s' in repository '%s'. Please check access permissions.", groupName, repoID)
		} else if strings.Contains(err.Error(), "timeout") {
			errorMessage = fmt.Sprintf("Timeout occurred while retrying scan for group '%s' in repository '%s'. The repository may be too large or temporarily unavailable.", groupName, repoID)
		} else {
			errorMessage = fmt.Sprintf("Failed to retry scan for group '%s' in repository '%s': %v", groupName, repoID, err)
		}

		response := models.TaskControlResponse{
			Success: false,
			Message: errorMessage,
			TaskID:  request.TaskID,
		}

		// Broadcast scheduler event for failed retry
		c.broadcastSchedulerEvent(request.TaskID, "usage_scan", "retry", response.Message, false, err)

		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	response := models.TaskControlResponse{
		Success: true,
		Message: fmt.Sprintf("Successfully retried scan for group '%s' in repository '%s' (scan ID: %s)", groupName, repoID, actualScanID),
		TaskID:  request.TaskID,
	}

	// Broadcast scheduler event for successful retry
	c.broadcastSchedulerEvent(request.TaskID, "usage_scan", "retry", response.Message, true, nil)

	ctx.JSON(http.StatusOK, response)
}

// retryAutoScan retries a failed auto-scan task
func (c *SchedulerController) retryAutoScan(ctx *gin.Context, request models.TaskControlRequest) {
	// For auto-scan tasks, delegate to the usage scan retry logic
	// since auto-scan tasks are essentially usage scans triggered by the scheduler
	c.retryUsageScan(ctx, request)
}

// retryReport retries a failed report generation
func (c *SchedulerController) retryReport(ctx *gin.Context, request models.TaskControlRequest) {
	response := models.TaskControlResponse{
		Success: false,
		Message: "Report retry functionality not yet implemented",
		TaskID:  request.TaskID,
	}

	log.Printf("Report retry requested for task: %s (not yet implemented)", request.TaskID)
	ctx.JSON(http.StatusNotImplemented, response)
}

// triggerScheduledScan triggers a scheduled scan task immediately
func (c *SchedulerController) triggerScheduledScan(ctx *gin.Context, request models.TaskControlRequest) {
	log.Printf("Triggering scheduled scan task: %s", request.TaskID)

	// Get the scheduled task details from auto-scan service
	job, err := c.autoScanService.GetJobByID(request.TaskID)
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to find scheduled task: %v", err),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusNotFound, response)
		return
	}

	if job == nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: "Scheduled task not found",
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusNotFound, response)
		return
	}

	// Create a scan request from the job details
	scanRequest := models.UsageScanRequest{
		GroupName: job.GroupName,
		RepoID:    job.RepoID,
		Force:     true, // Force trigger even if recently scanned
	}

	// Update job status to running before starting scan
	if err := c.autoScanService.UpdateJobStatus(request.TaskID, "running"); err != nil {
		log.Printf("Warning: Failed to update job status to running: %v", err)
	}

	// Start the scan immediately
	scanCtx := context.Background()
	actualScanID, err := c.usageScanner.ScanGroupUsage(scanCtx, scanRequest)
	if err != nil {
		// If scan fails to start, mark job as failed but keep it visible
		if updateErr := c.autoScanService.UpdateJobStatus(request.TaskID, "failed"); updateErr != nil {
			log.Printf("Warning: Failed to update job status to failed: %v", updateErr)
		}

		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to trigger scan: %v", err),
			TaskID:  request.TaskID,
		}

		// Broadcast scheduler event for failed trigger
		c.broadcastSchedulerEvent(request.TaskID, "auto_scan", "trigger", response.Message, false, err)

		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	// Store the actual scan ID in the job for tracking
	if err := c.autoScanService.UpdateJobScanID(request.TaskID, actualScanID); err != nil {
		log.Printf("Warning: Failed to update job with scan ID: %v", err)
	}

	// Don't remove the job from queue yet - let it complete naturally
	// The job will be marked as completed when the scan finishes

	response := models.TaskControlResponse{
		Success: true,
		Message: fmt.Sprintf("Successfully triggered scan for group '%s' in repository '%s' (scan ID: %s)", job.GroupName, job.RepoID, actualScanID),
		TaskID:  request.TaskID,
	}

	// Broadcast scheduler event for successful trigger
	c.broadcastSchedulerEvent(request.TaskID, "auto_scan", "trigger", response.Message, true, nil)

	ctx.JSON(http.StatusOK, response)
}

// triggerScheduledReport triggers a scheduled report generation immediately
func (c *SchedulerController) triggerScheduledReport(ctx *gin.Context, request models.TaskControlRequest) {
	response := models.TaskControlResponse{
		Success: false,
		Message: "Report trigger functionality not yet implemented",
		TaskID:  request.TaskID,
	}

	log.Printf("Report trigger requested for task: %s (not yet implemented)", request.TaskID)
	ctx.JSON(http.StatusNotImplemented, response)
}

// Helper methods for status aggregation

// getReportSchedulerStatus returns the status of the report scheduler service
func (c *SchedulerController) getReportSchedulerStatus() models.ServiceStatus {
	// TODO: Add method to SchedulerService to get status
	return models.ServiceStatus{
		Name:         "Report Scheduler",
		IsRunning:    true, // Placeholder - need to implement status check
		Status:       "running",
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  0,
		QueuedTasks:  0,
	}
}

// getUsageScannerStatus returns the status of the usage scanner service
func (c *SchedulerController) getUsageScannerStatus() models.ServiceStatus {
	var isRunning bool
	var status string
	var activeTasks int

	if c.usageScanner != nil {
		if usageScanner, ok := c.usageScanner.(*services.UsageScannerService); ok {
			isRunning = usageScanner.IsRunning()
			if isRunning {
				status = "running"
			} else {
				status = "stopped"
			}

			// Get active tasks count
			if activeScans, err := usageScanner.GetActiveScans(); err == nil {
				activeTasks = len(activeScans)
			}
		} else {
			isRunning = false
			status = "unknown"
		}
	} else {
		isRunning = false
		status = "not_initialized"
	}

	return models.ServiceStatus{
		Name:         "Usage Scanner",
		IsRunning:    isRunning,
		Status:       status,
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  activeTasks,
		QueuedTasks:  0, // TODO: Add method to get queued tasks count
	}
}

// getAutoScanServiceStatus returns the status of the auto scan service
func (c *SchedulerController) getAutoScanServiceStatus() models.ServiceStatus {
	// TODO: Add method to AutoScanService to get status
	return models.ServiceStatus{
		Name:         "Auto Scan Service",
		IsRunning:    true, // Placeholder - need to implement status check
		Status:       "running",
		LastActivity: time.Now().Format(time.RFC3339),
		ActiveTasks:  0,
		QueuedTasks:  0,
	}
}

// calculateOverallHealth determines the overall health based on individual service statuses
func (c *SchedulerController) calculateOverallHealth(status models.SchedulerStatus) string {
	services := []models.ServiceStatus{
		status.ReportScheduler,
		status.UsageScanner,
		status.AutoScanService,
	}

	healthyCount := 0
	errorCount := 0

	for _, service := range services {
		if service.Status == "running" && service.IsRunning {
			healthyCount++
		} else if service.Status == "error" {
			errorCount++
		}
	}

	if errorCount > 0 {
		return "unhealthy"
	} else if healthyCount == len(services) {
		return "healthy"
	} else {
		return "degraded"
	}
}

// Helper methods for counting tasks (placeholders for now)

func (c *SchedulerController) countPlannedTasks() int {
	var plannedTasks []models.PlannedTask

	// Get planned report tasks
	reportTasks := c.getPlannedReportTasks()
	plannedTasks = append(plannedTasks, reportTasks...)

	// Get planned scan tasks
	scanTasks := c.getPlannedScanTasks()
	plannedTasks = append(plannedTasks, scanTasks...)

	// Get planned auto-scan tasks
	autoScanTasks := c.getPlannedAutoScanTasks()
	plannedTasks = append(plannedTasks, autoScanTasks...)

	return len(plannedTasks)
}

func (c *SchedulerController) countActiveTasks() int {
	var activeTasks []models.ActiveTask

	// Get active report tasks
	reportTasks := c.getActiveReportTasks()
	activeTasks = append(activeTasks, reportTasks...)

	// Get active scan tasks
	scanTasks := c.getActiveScanTasks()
	activeTasks = append(activeTasks, scanTasks...)

	// Get active auto-scan tasks
	autoScanTasks := c.getActiveAutoScanTasks()
	activeTasks = append(activeTasks, autoScanTasks...)

	return len(activeTasks)
}

func (c *SchedulerController) countFailedTasks() int {
	var failedTasks []models.FailedTask

	// Get failed report tasks
	reportTasks := c.getFailedReportTasks()
	failedTasks = append(failedTasks, reportTasks...)

	// Get failed scan tasks
	scanTasks := c.getFailedScanTasks()
	failedTasks = append(failedTasks, scanTasks...)

	// Get failed auto-scan tasks
	autoScanTasks := c.getFailedAutoScanTasks()
	failedTasks = append(failedTasks, autoScanTasks...)

	return len(failedTasks)
}

func (c *SchedulerController) countCompletedToday() int {
	var completedTasks []models.CompletedTask

	// Get completed report tasks
	reportTasks := c.getCompletedReportTasks()
	completedTasks = append(completedTasks, reportTasks...)

	// Get completed scan tasks
	scanTasks := c.getCompletedScanTasks()
	completedTasks = append(completedTasks, scanTasks...)

	// Get completed auto-scan tasks
	autoScanTasks := c.getCompletedAutoScanTasks()
	completedTasks = append(completedTasks, autoScanTasks...)

	// Filter for tasks completed today
	today := time.Now().Format("2006-01-02")
	todayCount := 0

	for _, task := range completedTasks {
		if task.CompletedAt != "" {
			// Parse the completion time and check if it's today
			if completedTime, err := time.Parse(time.RFC3339, task.CompletedAt); err == nil {
				if completedTime.Format("2006-01-02") == today {
					todayCount++
				}
			}
		}
	}

	return todayCount
}

func (c *SchedulerController) calculateSuccessRate() int {
	// TODO: Implement actual calculation
	return 95 // Placeholder
}

func (c *SchedulerController) calculateAverageTaskTime() int {
	// TODO: Implement actual calculation
	return 120 // Placeholder: 2 minutes
}

// Helper methods for task aggregation (placeholders for now)

func (c *SchedulerController) getPlannedReportTasks() []models.PlannedTask {
	// TODO: Get scheduled report presets and convert to PlannedTask format
	return []models.PlannedTask{}
}

func (c *SchedulerController) getPlannedScanTasks() []models.PlannedTask {
	// TODO: Get scheduled scans and convert to PlannedTask format
	return []models.PlannedTask{}
}

func (c *SchedulerController) getPlannedAutoScanTasks() []models.PlannedTask {
	// Get auto scan jobs from the auto scan service
	if c.autoScanService == nil {
		return []models.PlannedTask{}
	}

	// Get active jobs from the auto scan service
	jobs := c.autoScanService.GetActiveJobs()

	var plannedTasks []models.PlannedTask
	for _, job := range jobs {
		if job.Status == "pending" {
			// Resolve repository name from repository ID
			repositoryName := job.RepoID // Default to ID if name resolution fails
			if c.repoManager != nil {
				if config, err := c.repoManager.GetConfiguration(job.RepoID); err == nil {
					repositoryName = config.Name
				}
			}

			plannedTask := models.PlannedTask{
				ID:           job.ID,
				Name:         fmt.Sprintf("Auto Scan: %s", job.GroupName),
				Type:         "auto-scan",
				Repository:   repositoryName,
				GroupName:    job.GroupName,
				ScheduledFor: job.ScheduledFor.Format(time.RFC3339),
				Priority:     "medium", // Default priority for auto scans
				Description:  fmt.Sprintf("Automatically scheduled scan for group %s in repository %s", job.GroupName, repositoryName),
				Metadata: map[string]interface{}{
					"configId":     job.ConfigID,
					"createdAt":    job.CreatedAt.Format(time.RFC3339),
					"status":       "scheduled",
					"repositoryId": job.RepoID, // Keep the original ID for reference
				},
			}
			plannedTasks = append(plannedTasks, plannedTask)
		}
	}

	return plannedTasks
}

func (c *SchedulerController) getActiveReportTasks() []models.ActiveTask {
	// TODO: Get currently running report generations
	return []models.ActiveTask{}
}

func (c *SchedulerController) getActiveScanTasks() []models.ActiveTask {
	var activeTasks []models.ActiveTask

	if c.usageScanner == nil {
		return activeTasks
	}

	// Get active scans from usage scanner
	scanTasks, err := c.usageScanner.GetActiveScans()
	if err != nil {
		log.Printf("Error getting active scan tasks: %v", err)
		return activeTasks
	}

	return scanTasks
}

func (c *SchedulerController) getActiveAutoScanTasks() []models.ActiveTask {
	// Get auto scan jobs from the auto scan service
	if c.autoScanService == nil {
		return []models.ActiveTask{}
	}

	// Get active jobs from the auto scan service
	jobs := c.autoScanService.GetActiveJobs()

	var activeTasks []models.ActiveTask
	for _, job := range jobs {
		if job.Status == "running" {
			// Resolve repository name from repository ID
			repositoryName := job.RepoID // Default to ID if name resolution fails
			if c.repoManager != nil {
				if config, err := c.repoManager.GetConfiguration(job.RepoID); err == nil {
					repositoryName = config.Name
				}
			}

			// Calculate duration if started
			var startedAt string
			if job.StartedAt != nil {
				startedAt = job.StartedAt.Format(time.RFC3339)
			} else {
				startedAt = job.CreatedAt.Format(time.RFC3339)
			}

			activeTask := models.ActiveTask{
				ID:         job.ID,
				Type:       "auto-scan",
				Name:       fmt.Sprintf("Auto Scan: %s", job.GroupName),
				StartedAt:  startedAt,
				Repository: repositoryName,
				GroupName:  job.GroupName,
				Status:     "running",
				Metadata: map[string]interface{}{
					"configId":     job.ConfigID,
					"createdAt":    job.CreatedAt.Format(time.RFC3339),
					"scheduledFor": job.ScheduledFor.Format(time.RFC3339),
					"repositoryId": job.RepoID, // Keep the original ID for reference
				},
			}

			// Add progress information if available
			// Note: Progress tracking for auto-scan jobs would need to be implemented
			// in the auto-scan service to provide real-time progress updates

			activeTasks = append(activeTasks, activeTask)
		}
	}

	return activeTasks
}

func (c *SchedulerController) getFailedReportTasks() []models.FailedTask {
	// TODO: Get failed report executions
	return []models.FailedTask{}
}

func (c *SchedulerController) getFailedScanTasks() []models.FailedTask {
	var failedTasks []models.FailedTask

	if c.usageScanner == nil {
		return failedTasks
	}

	// Get failed scans from usage scanner
	failedScans, err := c.usageScanner.GetFailedScans()
	if err != nil {
		log.Printf("Error getting failed scan tasks: %v", err)
		return failedTasks
	}

	// Convert UsageScanStatus to FailedTask
	for _, scan := range failedScans {
		// Create a task ID from group name and repo ID
		taskID := fmt.Sprintf("%s:%s", scan.GroupName, scan.RepoID)

		// Determine the primary error message
		errorMessage := "Scan completed with failures"
		if len(scan.FailedSources) > 0 {
			errorMessage = fmt.Sprintf("Failed to scan %d sources: %s",
				len(scan.FailedSources), scan.FailedSources[0].Error)
		}

		// Calculate retry count (for now, assume 0 - could be enhanced later)
		retryCount := 0
		canRetry := true

		failedTask := models.FailedTask{
			ID:           taskID,
			Type:         "scan",
			Name:         fmt.Sprintf("Usage Scan: %s", scan.GroupName),
			FailedAt:     scan.LastScanTime.Format(time.RFC3339),
			ErrorMessage: errorMessage,
			RetryCount:   retryCount,
			CanRetry:     canRetry,
			Repository:   scan.RepoID,
			GroupName:    scan.GroupName,
			LastAttempt:  scan.LastScanTime.Format(time.RFC3339),
			Metadata: map[string]interface{}{
				"sourcesTotal":     scan.SourcesTotal,
				"sourcesScanned":   scan.SourcesScanned,
				"sourcesCompleted": len(scan.CompletedSources),
				"sourcesFailed":    len(scan.FailedSources),
				"totalUsages":      scan.TotalUsages,
				"scanDuration":     scan.ScanDuration.Seconds(),
			},
		}

		failedTasks = append(failedTasks, failedTask)
	}

	return failedTasks
}

func (c *SchedulerController) getFailedAutoScanTasks() []models.FailedTask {
	// Get auto scan jobs from the auto scan service
	if c.autoScanService == nil {
		return []models.FailedTask{}
	}

	// Get active jobs from the auto scan service
	jobs := c.autoScanService.GetActiveJobs()

	var failedTasks []models.FailedTask
	for _, job := range jobs {
		if job.Status == "failed" {
			// Resolve repository name from repository ID
			repositoryName := job.RepoID // Default to ID if name resolution fails
			if c.repoManager != nil {
				if config, err := c.repoManager.GetConfiguration(job.RepoID); err == nil {
					repositoryName = config.Name
				}
			}

			// Use CompletedAt as FailedAt, or CreatedAt if not available
			var failedAt string
			if job.CompletedAt != nil {
				failedAt = job.CompletedAt.Format(time.RFC3339)
			} else {
				failedAt = job.CreatedAt.Format(time.RFC3339)
			}

			// Use StartedAt as LastAttempt, or CreatedAt if not available
			var lastAttempt string
			if job.StartedAt != nil {
				lastAttempt = job.StartedAt.Format(time.RFC3339)
			} else {
				lastAttempt = job.CreatedAt.Format(time.RFC3339)
			}

			// Error message from job or default
			errorMessage := job.Error
			if errorMessage == "" {
				errorMessage = "Auto scan job failed"
			}

			failedTask := models.FailedTask{
				ID:           job.ID,
				Type:         "auto-scan",
				Name:         fmt.Sprintf("Auto Scan: %s", job.GroupName),
				FailedAt:     failedAt,
				ErrorMessage: errorMessage,
				RetryCount:   0,    // Auto-scan jobs don't currently track retry count
				CanRetry:     true, // Auto-scan jobs can typically be retried
				Repository:   repositoryName,
				GroupName:    job.GroupName,
				LastAttempt:  lastAttempt,
				Metadata: map[string]interface{}{
					"configId":     job.ConfigID,
					"createdAt":    job.CreatedAt.Format(time.RFC3339),
					"scheduledFor": job.ScheduledFor.Format(time.RFC3339),
					"repositoryId": job.RepoID, // Keep the original ID for reference
					"resultsCount": job.ResultsCount,
				},
			}

			failedTasks = append(failedTasks, failedTask)
		}
	}

	return failedTasks
}

// Helper methods for completed tasks

func (c *SchedulerController) getCompletedReportTasks() []models.CompletedTask {
	// TODO: Get completed report executions
	return []models.CompletedTask{}
}

func (c *SchedulerController) getCompletedScanTasks() []models.CompletedTask {
	var completedTasks []models.CompletedTask

	if c.usageScanner == nil {
		return completedTasks
	}

	// Get completed scans from usage scanner
	completedScans, err := c.usageScanner.GetCompletedScans()
	if err != nil {
		log.Printf("Error getting completed scans: %v", err)
		return completedTasks
	}

	// Convert UsageScanStatus to CompletedTask
	for _, scan := range completedScans {
		// Create a task ID from group name and repo ID
		taskID := fmt.Sprintf("%s:%s", scan.GroupName, scan.RepoID)

		// Calculate duration in milliseconds
		duration := int64(scan.ScanDuration / time.Millisecond)

		// Use LastScanTime as both start and completion time
		// Note: We don't have separate start/end times in UsageScanStatus
		completedTime := scan.LastScanTime.Format(time.RFC3339)
		startTime := completedTime // For now, use the same time

		// Use actual file count if available, otherwise fall back to source count
		filesScanned := scan.SourcesScanned // Default fallback
		if scan.ProcessedFiles > 0 {
			filesScanned = scan.ProcessedFiles
		}

		completedTask := models.CompletedTask{
			ID:           taskID,
			Type:         "scan",
			Name:         fmt.Sprintf("Usage Scan: %s", scan.GroupName),
			StartedAt:    startTime,
			CompletedAt:  completedTime,
			Duration:     duration,
			Repository:   scan.RepoID,
			GroupName:    scan.GroupName,
			ResultsCount: scan.TotalUsages,
			FilesScanned: filesScanned,
			Metadata: map[string]interface{}{
				"totalSources":     scan.SourcesTotal,
				"sourcesScanned":   scan.SourcesScanned,
				"completedSources": scan.CompletedSources,
				"failedSources":    scan.FailedSources,
				"totalUsages":      scan.TotalUsages,
				"totalFiles":       scan.TotalFiles,
				"processedFiles":   scan.ProcessedFiles,
			},
		}

		completedTasks = append(completedTasks, completedTask)
	}

	return completedTasks
}

func (c *SchedulerController) getCompletedAutoScanTasks() []models.CompletedTask {
	// Get auto scan jobs from the auto scan service
	if c.autoScanService == nil {
		return []models.CompletedTask{}
	}

	// Get active jobs from the auto scan service
	jobs := c.autoScanService.GetActiveJobs()

	var completedTasks []models.CompletedTask
	for _, job := range jobs {
		// Include completed, cancelled, and skipped jobs as completed tasks
		if job.Status == "completed" || job.Status == "cancelled" || job.Status == "skipped" {
			// Resolve repository name from repository ID
			repositoryName := job.RepoID // Default to ID if name resolution fails
			if c.repoManager != nil {
				if config, err := c.repoManager.GetConfiguration(job.RepoID); err == nil {
					repositoryName = config.Name
				}
			}

			// Use StartedAt or CreatedAt for start time
			var startedAt string
			if job.StartedAt != nil {
				startedAt = job.StartedAt.Format(time.RFC3339)
			} else {
				startedAt = job.CreatedAt.Format(time.RFC3339)
			}

			// Use CompletedAt or current time for completion time
			var completedAt string
			if job.CompletedAt != nil {
				completedAt = job.CompletedAt.Format(time.RFC3339)
			} else {
				completedAt = time.Now().Format(time.RFC3339)
			}

			// Calculate duration in milliseconds
			var duration int64
			if job.StartedAt != nil && job.CompletedAt != nil {
				duration = job.CompletedAt.Sub(*job.StartedAt).Milliseconds()
			} else if job.CompletedAt != nil {
				duration = job.CompletedAt.Sub(job.CreatedAt).Milliseconds()
			}

			completedTask := models.CompletedTask{
				ID:           job.ID,
				Type:         "auto-scan",
				Name:         fmt.Sprintf("Auto Scan: %s", job.GroupName),
				StartedAt:    startedAt,
				CompletedAt:  completedAt,
				Duration:     duration,
				Repository:   repositoryName,
				GroupName:    job.GroupName,
				ResultsCount: job.ResultsCount,
				Metadata: map[string]interface{}{
					"configId":     job.ConfigID,
					"createdAt":    job.CreatedAt.Format(time.RFC3339),
					"scheduledFor": job.ScheduledFor.Format(time.RFC3339),
					"repositoryId": job.RepoID, // Keep the original ID for reference
					"status":       job.Status, // Include the actual status (completed/cancelled/skipped)
				},
			}

			// Add error information if the job was cancelled or had an error
			if job.Error != "" {
				completedTask.Metadata["error"] = job.Error
			}

			completedTasks = append(completedTasks, completedTask)
		}
	}

	return completedTasks
}

// Utility methods for sorting

func (c *SchedulerController) sortPlannedTasksByTime(tasks []models.PlannedTask) {
	sort.Slice(tasks, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, tasks[i].ScheduledFor)
		timeJ, errJ := time.Parse(time.RFC3339, tasks[j].ScheduledFor)

		// If parsing fails, put the failed item at the end
		if errI != nil {
			return false
		}
		if errJ != nil {
			return true
		}

		// Sort by scheduled time (earliest first)
		return timeI.Before(timeJ)
	})
}

func (c *SchedulerController) sortFailedTasksByTime(tasks []models.FailedTask) {
	sort.Slice(tasks, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, tasks[i].FailedAt)
		timeJ, errJ := time.Parse(time.RFC3339, tasks[j].FailedAt)

		// If parsing fails, put the failed item at the end
		if errI != nil {
			return false
		}
		if errJ != nil {
			return true
		}

		// Sort by failed time (most recent first)
		return timeI.After(timeJ)
	})
}

func (c *SchedulerController) sortCompletedTasksByTime(tasks []models.CompletedTask) {
	sort.Slice(tasks, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, tasks[i].CompletedAt)
		timeJ, errJ := time.Parse(time.RFC3339, tasks[j].CompletedAt)

		// If parsing fails, put the failed item at the end
		if errI != nil {
			return false
		}
		if errJ != nil {
			return true
		}

		// Sort by completion time (most recent first)
		return timeI.After(timeJ)
	})
}

// GetInterruptedScans returns all interrupted scans with details
func (c *SchedulerController) GetInterruptedScans(ctx *gin.Context) {
	// Initialize with empty slice to avoid null in JSON
	allScans := make([]models.InterruptedScan, 0)
	var stats models.InterruptedScansStats

	if c.usageScanner != nil {
		// Get interrupted scans
		interruptedScans, err := c.usageScanner.GetInterruptedScans()
		if err != nil {
			log.Printf("Error getting interrupted scans: %v", err)
		} else {
			for _, scan := range interruptedScans {
				interruptedScan := c.convertToInterruptedScan(scan, "interrupted")
				allScans = append(allScans, interruptedScan)
				stats.InterruptedCount++
			}
		}

		// Get failed scans
		failedScans, err := c.usageScanner.GetFailedScans()
		if err != nil {
			log.Printf("Error getting failed scans: %v", err)
		} else {
			for _, scan := range failedScans {
				failedScan := c.convertToInterruptedScan(scan, "failed")
				allScans = append(allScans, failedScan)
				stats.FailedCount++
			}
		}
	}

	// For now, pending restart is the same as interrupted count
	stats.PendingRestart = stats.InterruptedCount

	response := models.InterruptedScansResponse{
		Scans: allScans,
		Total: len(allScans),
		Stats: stats,
	}

	ctx.JSON(http.StatusOK, response)
}

// GetInterruptedScansStats returns statistics about interrupted scans
func (c *SchedulerController) GetInterruptedScansStats(ctx *gin.Context) {
	var stats models.InterruptedScansStats

	if c.usageScanner != nil {
		// Get interrupted scans count
		interruptedScans, err := c.usageScanner.GetInterruptedScans()
		if err != nil {
			log.Printf("Error getting interrupted scans: %v", err)
		} else {
			stats.InterruptedCount = len(interruptedScans)
		}

		// Get failed scans count
		failedScans, err := c.usageScanner.GetFailedScans()
		if err != nil {
			log.Printf("Error getting failed scans: %v", err)
		} else {
			stats.FailedCount = len(failedScans)
		}
	}

	// For now, pending restart is the same as interrupted count
	stats.PendingRestart = stats.InterruptedCount

	ctx.JSON(http.StatusOK, stats)
}

// RestartInterruptedScans restarts all interrupted scans
func (c *SchedulerController) RestartInterruptedScans(ctx *gin.Context) {
	log.Printf("Restart interrupted scans request received")

	// Get all interrupted scans
	interruptedScans, err := c.usageScanner.GetInterruptedScans()
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to retrieve interrupted scans: %v", err),
		}
		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	if len(interruptedScans) == 0 {
		response := models.TaskControlResponse{
			Success: true,
			Message: "No interrupted scans found to restart",
		}
		ctx.JSON(http.StatusOK, response)
		return
	}

	// Restart each interrupted scan
	restartedCount := 0
	var restartErrors []string

	for _, scan := range interruptedScans {
		log.Printf("Restarting interrupted scan for group %s in repo %s", scan.GroupName, scan.RepoID)

		// Cancel current scan if still in progress
		if scan.InProgress {
			if err := c.usageScanner.CancelScan(scan.GroupName, scan.RepoID); err != nil {
				log.Printf("Warning: Failed to cancel current scan for group %s in repo %s: %v",
					scan.GroupName, scan.RepoID, err)
			}
			// Wait a moment for cancellation to take effect
			time.Sleep(500 * time.Millisecond)
		}

		// Create a new scan request
		scanRequest := models.UsageScanRequest{
			GroupName: scan.GroupName,
			RepoID:    scan.RepoID,
			Force:     true, // Force restart even if recently scanned
		}

		// Start the new scan
		scanCtx := context.Background()
		if _, err := c.usageScanner.ScanGroupUsage(scanCtx, scanRequest); err != nil {
			errorMsg := fmt.Sprintf("Failed to restart scan for group '%s' in repo '%s': %v",
				scan.GroupName, scan.RepoID, err)
			restartErrors = append(restartErrors, errorMsg)
			log.Printf("Error: %s", errorMsg)
		} else {
			restartedCount++
			log.Printf("Successfully restarted scan for group %s in repo %s", scan.GroupName, scan.RepoID)
		}
	}

	// Prepare response based on results
	var response models.TaskControlResponse
	if len(restartErrors) == 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Successfully restarted %d interrupted scan(s)", restartedCount),
		}
	} else if restartedCount > 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Partially successful: restarted %d scan(s), %d error(s). Errors: %v",
				restartedCount, len(restartErrors), restartErrors),
		}
	} else {
		response = models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to restart any scans. Errors: %v", restartErrors),
		}
	}

	log.Printf("Restart interrupted scans completed: %d restarted, %d errors", restartedCount, len(restartErrors))
	ctx.JSON(http.StatusOK, response)
}

// ClearFailedScans clears all failed scan records
func (c *SchedulerController) ClearFailedScans(ctx *gin.Context) {
	log.Printf("Clear failed scans request received")

	// Get all failed scans
	failedScans, err := c.usageScanner.GetFailedScans()
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to retrieve failed scans: %v", err),
		}
		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	if len(failedScans) == 0 {
		response := models.TaskControlResponse{
			Success: true,
			Message: "No failed scans found to clear",
		}
		ctx.JSON(http.StatusOK, response)
		return
	}

	// Clear each failed scan
	clearedCount := 0
	var clearErrors []string

	for _, scan := range failedScans {
		log.Printf("Clearing failed scan for group %s in repo %s", scan.GroupName, scan.RepoID)

		if err := c.usageScanner.ClearUsageResults(scan.GroupName, scan.RepoID); err != nil {
			errorMsg := fmt.Sprintf("Failed to clear scan for group '%s' in repo '%s': %v",
				scan.GroupName, scan.RepoID, err)
			clearErrors = append(clearErrors, errorMsg)
			log.Printf("Error: %s", errorMsg)
		} else {
			clearedCount++
			log.Printf("Successfully cleared scan for group %s in repo %s", scan.GroupName, scan.RepoID)
		}
	}

	// Prepare response based on results
	var response models.TaskControlResponse
	if len(clearErrors) == 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Successfully cleared %d failed scan(s)", clearedCount),
		}
	} else if clearedCount > 0 {
		response = models.TaskControlResponse{
			Success: true,
			Message: fmt.Sprintf("Partially successful: cleared %d scan(s), %d error(s). Errors: %v",
				clearedCount, len(clearErrors), clearErrors),
		}
	} else {
		response = models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to clear any scans. Errors: %v", clearErrors),
		}
	}

	log.Printf("Clear failed scans completed: %d cleared, %d errors", clearedCount, len(clearErrors))
	ctx.JSON(http.StatusOK, response)
}

// RestartSpecificScan restarts a specific scan by ID
func (c *SchedulerController) RestartSpecificScan(ctx *gin.Context) {
	var request models.TaskControlRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Parse task ID to extract groupName and repoID
	// Expected format: "groupName:repoID" or "groupName:repoID:timestamp"
	parts := strings.Split(request.TaskID, ":")
	if len(parts) < 2 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid task ID format. Expected format: 'groupName:repoID' or 'groupName:repoID:timestamp'",
		})
		return
	}

	// Extract groupName and repoID (ignore timestamp if present)
	groupName := parts[0]
	repoID := parts[1]

	log.Printf("Restart specific scan request received for group: %s, repo: %s", groupName, repoID)

	// Check if scan exists and get current status
	currentStatus, err := c.usageScanner.GetScanStatus(groupName, repoID)
	if err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Scan not found for group '%s' in repository '%s'", groupName, repoID),
			TaskID:  request.TaskID,
		}
		ctx.JSON(http.StatusNotFound, response)
		return
	}

	// If scan is currently in progress, cancel it first
	if currentStatus.InProgress {
		log.Printf("Canceling current scan for group %s in repo %s before restart", groupName, repoID)
		if err := c.usageScanner.CancelScan(groupName, repoID); err != nil {
			log.Printf("Warning: Failed to cancel current scan: %v", err)
		}
		// Wait a moment for cancellation to take effect
		time.Sleep(1 * time.Second)
	}

	// Create a new scan request
	scanRequest := models.UsageScanRequest{
		GroupName: groupName,
		RepoID:    repoID,
		Force:     true, // Force restart even if recently scanned
	}

	// Start the new scan
	scanCtx := context.Background()
	if _, err := c.usageScanner.ScanGroupUsage(scanCtx, scanRequest); err != nil {
		response := models.TaskControlResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to restart scan: %v", err),
			TaskID:  request.TaskID,
		}

		// Broadcast scheduler event for failed restart
		c.broadcastSchedulerEvent(request.TaskID, "usage_scan", "restart", response.Message, false, err)

		ctx.JSON(http.StatusInternalServerError, response)
		return
	}

	// If scan started successfully, clear any failed status for this task
	// This ensures the task is removed from the failed tasks list
	if err := c.usageScanner.ClearFailedStatus(groupName, repoID); err != nil {
		log.Printf("Warning: Failed to clear failed status for restarted scan %s:%s: %v", groupName, repoID, err)
		// Don't fail the request since the scan was started successfully
	}

	response := models.TaskControlResponse{
		Success: true,
		Message: fmt.Sprintf("Successfully restarted scan for group '%s' in repository '%s'", groupName, repoID),
		TaskID:  request.TaskID,
	}

	log.Printf("Successfully restarted scan for group %s in repo %s", groupName, repoID)

	// Broadcast scheduler event for successful restart
	c.broadcastSchedulerEvent(request.TaskID, "usage_scan", "restart", response.Message, true, nil)

	ctx.JSON(http.StatusOK, response)
}

// convertToInterruptedScan converts a UsageScanStatus to InterruptedScan
func (c *SchedulerController) convertToInterruptedScan(scan models.UsageScanStatus, status string) models.InterruptedScan {
	// Calculate progress percentage
	var progress int
	if scan.SourcesTotal > 0 {
		progress = int(float64(scan.SourcesScanned) / float64(scan.SourcesTotal) * 100)
	}

	// Determine error message
	var errorMessage string
	if len(scan.FailedSources) > 0 {
		errorMessage = fmt.Sprintf("Failed to scan %d sources: %s",
			len(scan.FailedSources), scan.FailedSources[0].Error)
	} else if status == "interrupted" {
		errorMessage = "Scan was interrupted or orphaned"
	}

	return models.InterruptedScan{
		ID:           fmt.Sprintf("%s:%s", scan.GroupName, scan.RepoID),
		GroupName:    scan.GroupName,
		Repository:   scan.RepoID,
		Status:       status,
		StartedAt:    scan.LastScanTime.Format(time.RFC3339),
		Progress:     progress,
		ErrorMessage: errorMessage,
		CanRestart:   true,
		Metadata: map[string]interface{}{
			"sourcesTotal":     scan.SourcesTotal,
			"sourcesScanned":   scan.SourcesScanned,
			"sourcesCompleted": len(scan.CompletedSources),
			"sourcesFailed":    len(scan.FailedSources),
			"totalUsages":      scan.TotalUsages,
			"scanDuration":     scan.ScanDuration.Seconds(),
			"inProgress":       scan.InProgress,
		},
	}
}
