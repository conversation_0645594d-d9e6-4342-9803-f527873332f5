package services

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// TaskInfo represents information about a cancellable task
type TaskInfo struct {
	ID         string
	Type       string
	Name       string
	StartedAt  time.Time
	CancelFunc context.CancelFunc
	GroupName  string
	RepoID     string
	Status     string
}

// TaskCancellationManager manages cancellable contexts for active tasks
type TaskCancellationManager struct {
	activeTasks map[string]*TaskInfo // taskID -> TaskInfo
	mutex       sync.RWMutex
}

// NewTaskCancellationManager creates a new task cancellation manager
func NewTaskCancellationManager() *TaskCancellationManager {
	return &TaskCancellationManager{
		activeTasks: make(map[string]*TaskInfo),
	}
}

// RegisterTask registers a new cancellable task
func (m *TaskCancellationManager) RegisterTask(taskID, taskType, taskName, groupName, repoID string, cancelFunc context.CancelFunc) error {
	if taskID == "" {
		return fmt.Errorf("task ID cannot be empty")
	}
	if cancelFunc == nil {
		return fmt.Errorf("cancel function cannot be nil")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if task already exists
	if _, exists := m.activeTasks[taskID]; exists {
		return fmt.Errorf("task %s is already registered", taskID)
	}

	taskInfo := &TaskInfo{
		ID:         taskID,
		Type:       taskType,
		Name:       taskName,
		StartedAt:  time.Now(),
		CancelFunc: cancelFunc,
		GroupName:  groupName,
		RepoID:     repoID,
		Status:     "running",
	}

	m.activeTasks[taskID] = taskInfo
	log.Printf("Registered cancellable task: %s (%s)", taskID, taskType)
	return nil
}

// CancelTask cancels a task by ID
func (m *TaskCancellationManager) CancelTask(taskID string) error {
	if taskID == "" {
		return fmt.Errorf("task ID cannot be empty")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	taskInfo, exists := m.activeTasks[taskID]
	if !exists {
		return fmt.Errorf("task %s not found or not currently running", taskID)
	}

	// Check if task is already cancelled
	if taskInfo.Status == "cancelled" {
		return fmt.Errorf("task %s is already cancelled", taskID)
	}

	// Cancel the context
	taskInfo.CancelFunc()
	taskInfo.Status = "cancelled"

	log.Printf("Cancelled task: %s (%s)", taskID, taskInfo.Type)
	return nil
}

// CleanupTask removes a task from tracking (called when task completes)
func (m *TaskCancellationManager) CleanupTask(taskID string) {
	if taskID == "" {
		return
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if taskInfo, exists := m.activeTasks[taskID]; exists {
		delete(m.activeTasks, taskID)
		log.Printf("Cleaned up task: %s (%s)", taskID, taskInfo.Type)
	}
}

// GetTaskInfo returns information about a task
func (m *TaskCancellationManager) GetTaskInfo(taskID string) (*TaskInfo, error) {
	if taskID == "" {
		return nil, fmt.Errorf("task ID cannot be empty")
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	taskInfo, exists := m.activeTasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	// Return a copy to avoid race conditions
	return &TaskInfo{
		ID:         taskInfo.ID,
		Type:       taskInfo.Type,
		Name:       taskInfo.Name,
		StartedAt:  taskInfo.StartedAt,
		CancelFunc: nil, // Don't expose the cancel function
		GroupName:  taskInfo.GroupName,
		RepoID:     taskInfo.RepoID,
		Status:     taskInfo.Status,
	}, nil
}

// GetActiveTasks returns a list of all active tasks
func (m *TaskCancellationManager) GetActiveTasks() []*TaskInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	tasks := make([]*TaskInfo, 0, len(m.activeTasks))
	for _, taskInfo := range m.activeTasks {
		// Return copies to avoid race conditions
		tasks = append(tasks, &TaskInfo{
			ID:         taskInfo.ID,
			Type:       taskInfo.Type,
			Name:       taskInfo.Name,
			StartedAt:  taskInfo.StartedAt,
			CancelFunc: nil, // Don't expose the cancel function
			GroupName:  taskInfo.GroupName,
			RepoID:     taskInfo.RepoID,
			Status:     taskInfo.Status,
		})
	}

	return tasks
}

// GetActiveTasksByType returns active tasks of a specific type
func (m *TaskCancellationManager) GetActiveTasksByType(taskType string) []*TaskInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	tasks := make([]*TaskInfo, 0)
	for _, taskInfo := range m.activeTasks {
		if taskInfo.Type == taskType {
			// Return copies to avoid race conditions
			tasks = append(tasks, &TaskInfo{
				ID:         taskInfo.ID,
				Type:       taskInfo.Type,
				Name:       taskInfo.Name,
				StartedAt:  taskInfo.StartedAt,
				CancelFunc: nil, // Don't expose the cancel function
				GroupName:  taskInfo.GroupName,
				RepoID:     taskInfo.RepoID,
				Status:     taskInfo.Status,
			})
		}
	}

	return tasks
}

// IsTaskActive checks if a task is currently active
func (m *TaskCancellationManager) IsTaskActive(taskID string) bool {
	if taskID == "" {
		return false
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	_, exists := m.activeTasks[taskID]
	return exists
}

// GetActiveTaskCount returns the number of active tasks
func (m *TaskCancellationManager) GetActiveTaskCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.activeTasks)
}

// CancelAllTasks cancels all active tasks (useful for shutdown)
func (m *TaskCancellationManager) CancelAllTasks() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for taskID, taskInfo := range m.activeTasks {
		taskInfo.CancelFunc()
		taskInfo.Status = "cancelled"
		log.Printf("Cancelled task during shutdown: %s (%s)", taskID, taskInfo.Type)
	}

	log.Printf("Cancelled %d active tasks", len(m.activeTasks))
}
