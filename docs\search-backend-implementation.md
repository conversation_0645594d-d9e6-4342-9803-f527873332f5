# Search Backend Implementation Guide

This document provides technical details for implementing the search functionality on the backend of the ADGitOps UI application. It focuses on the server-side implementation requirements to support the frontend search features.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Query Parser Implementation](#query-parser-implementation)
- [Search Engine Requirements](#search-engine-requirements)
- [API Endpoints](#api-endpoints)
- [Performance Considerations](#performance-considerations)
- [Error Handling](#error-handling)
- [Testing Strategy](#testing-strategy)

## Architecture Overview

The search backend consists of the following components:

1. **Query Parser**: Parses the search query string into a structured representation
2. **Search Engine**: Executes the parsed query against the data store
3. **API Layer**: Exposes endpoints for the frontend to consume
4. **Caching Layer**: (Optional) Improves performance for common queries

### Data Flow

```
Frontend Search Input → API Endpoint → Query Parser → Search Engine → Data Store → Response Formatter → Frontend
```

## Query Parser Implementation

The query parser should convert the search syntax into a structured query that can be executed by the search engine.

### Parser Requirements

1. **Tokenization**: Break the query string into tokens
2. **Syntax Recognition**: Identify field specifiers, operators, and values
3. **Query Structure**: Build a tree or graph representing the query logic

### Grammar Definition

The search query follows this simplified grammar:

```
query       ::= expression
expression  ::= term (operator term)*
term        ::= field_search | exact_phrase | simple_text
field_search ::= field_name ":" [modifier] value
modifier    ::= "~" | "*"
exact_phrase ::= '"' text '"'
operator    ::= "AND" | "OR" | "NOT"
```

### Parser Implementation Example (Golang)

```go
package search

import (
	"strings"
	"unicode"
)

// Token represents a single token in the search query
type TokenType int

const (
	TOKEN_FIELD TokenType = iota
	TOKEN_MODIFIER
	TOKEN_VALUE
	TOKEN_OPERATOR
	TOKEN_QUOTED_STRING
)

type Token struct {
	Type  TokenType
	Value string
}

// Node represents a node in the abstract syntax tree
type NodeType int

const (
	NODE_TERM NodeType = iota
	NODE_AND
	NODE_OR
	NODE_NOT
)

type Node struct {
	Type      NodeType
	Field     string
	Modifier  string
	Value     string
	Left      *Node
	Right     *Node
}

// ParseQuery parses a search query string into an AST
func ParseQuery(queryString string) (*Node, error) {
	tokens, err := Tokenize(queryString)
	if err != nil {
		return nil, err
	}

	ast, err := BuildAST(tokens)
	if err != nil {
		return nil, err
	}

	return ast, nil
}

// Tokenize splits the query string into tokens
func Tokenize(queryString string) ([]Token, error) {
	// Implementation to split into tokens while preserving quoted phrases
	// Handle field specifiers, modifiers, and operators
	// Return list of tokens
	// ...

	return []Token{}, nil // Placeholder
}

// BuildAST builds an abstract syntax tree from tokens
func BuildAST(tokens []Token) (*Node, error) {
	// Build a tree structure representing the query logic
	// Handle operator precedence
	// Return the root node of the AST
	// ...

	return &Node{}, nil // Placeholder
}
```

## Search Engine Requirements

The search engine should support the following features:

### Query Types

1. **Exact Match**: Field equals value (case-insensitive)
   ```
   field == value (case-insensitive)
   ```

2. **Contains Match**: Field contains substring
   ```
   field LIKE '%value%' (case-insensitive)
   ```

3. **Prefix Match**: Field starts with value
   ```
   field LIKE 'value%' (case-insensitive)
   ```

4. **Full-Text Search**: Search across all searchable fields
   ```
   field1 LIKE '%value%' OR field2 LIKE '%value%' OR ... (case-insensitive)
   ```

### Logical Operations

1. **AND**: Both conditions must be true
   ```
   condition1 AND condition2
   ```

2. **OR**: Either condition can be true
   ```
   condition1 OR condition2
   ```

3. **NOT**: Exclude matches
   ```
   NOT condition
   ```

4. **Parentheses**: Group expressions
   ```
   (condition1 OR condition2) AND condition3
   ```

### Implementation Approaches in Golang

Depending on the data store, consider these implementation approaches with Golang:

1. **SQL Database with Go**:
   - Use `database/sql` package with a driver like `github.com/lib/pq` for PostgreSQL
   - Build dynamic SQL queries with parameterized statements
   - Example for substring matching:
   ```go
   query := "SELECT * FROM groups WHERE LOWER(groupname) LIKE LOWER($1)"
   rows, err := db.Query(query, "%"+searchTerm+"%")
   ```

2. **NoSQL Database with Go**:
   - Use MongoDB with `go.mongodb.org/mongo-driver/mongo`
   - Build filter documents for queries
   - Example for substring matching:
   ```go
   filter := bson.D{{
       "groupname",
       bson.D{{
           "$regex", primitive.Regex{Pattern: searchTerm, Options: "i"},
       }},
   }}
   cursor, err := collection.Find(ctx, filter)
   ```

3. **Search Engine with Go**:
   - Use Elasticsearch with `github.com/elastic/go-elasticsearch`
   - Build query DSL using Go structs
   - Example for complex query:
   ```go
   query := map[string]interface{}{
       "query": map[string]interface{}{
           "bool": map[string]interface{}{
               "must": []map[string]interface{}{
                   {
                       "match": map[string]interface{}{
                           "groupname": searchTerm,
                       },
                   },
               },
           },
       },
   }
   res, err := es.Search(
       es.Search.WithContext(ctx),
       es.Search.WithIndex("groups"),
       es.Search.WithBody(strings.NewReader(query)),
   )
   ```

4. **In-Memory Search with Go**:
   - Use `github.com/blevesearch/bleve` for full-text search capabilities
   - Create indexes and search queries
   - Example:
   ```go
   mapping := bleve.NewIndexMapping()
   index, err := bleve.New("groups.bleve", mapping)
   // Index documents...
   query := bleve.NewMatchQuery(searchTerm)
   search := bleve.NewSearchRequest(query)
   results, err := index.Search(search)
   ```

## API Endpoints with Golang Implementation

The backend should expose the following endpoints using a Golang web framework like Gin, Echo, or the standard library's `net/http`.

### Groups Search

```
GET /api/groups
```

Query Parameters:
- `search`: The search query string
- `page`: Page number (default: 1)
- `pageSize`: Number of results per page (default: 20)
- `lob`: (Optional) Filter by line of business

Response:
```json
{
  "items": [
    {
      "id": "string",
      "groupname": "string",
      "type": "string",
      "lob": "string",
      "description": "string",
      "members": [
        {
          "id": "string",
          "name": "string",
          "type": "string"
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

#### Golang Implementation Example (using Gin):

```go
package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"your-org/adgitops-ui/internal/search"
	"your-org/adgitops-ui/internal/models"
)

// GroupSearchResponse represents the response for group search
type GroupSearchResponse struct {
	Items      []models.Group       `json:"items"`
	Pagination models.PaginationInfo `json:"pagination"`
}

// SearchGroups handles the group search endpoint
func SearchGroups(c *gin.Context) {
	// Get query parameters
	searchQuery := c.Query("search")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	lob := c.Query("lob")

	// Parse the search query
	parsedQuery, err := search.ParseQuery(searchQuery)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Execute the search
	groups, total, err := search.SearchGroups(c.Request.Context(), parsedQuery, page, pageSize, lob)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := (total + pageSize - 1) / pageSize

	// Return the response
	c.JSON(http.StatusOK, GroupSearchResponse{
		Items: groups,
		Pagination: models.PaginationInfo{
			Page:       page,
			PageSize:   pageSize,
			TotalItems: total,
			TotalPages: totalPages,
		},
	})
}
```

### Users Search

```
GET /api/users
```

Query Parameters:
- `search`: The search query string
- `page`: Page number (default: 1)
- `pageSize`: Number of results per page (default: 20)
- `lob`: (Optional) Filter by line of business

Response:
```json
{
  "items": [
    {
      "id": "string",
      "name": "string",
      "lob": "string",
      "groups": [
        {
          "id": "string",
          "groupname": "string",
          "type": "string"
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 100,
    "totalPages": 5
  }
}
```

#### Golang Implementation Example (using Gin):

```go
package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"your-org/adgitops-ui/internal/search"
	"your-org/adgitops-ui/internal/models"
)

// UserSearchResponse represents the response for user search
type UserSearchResponse struct {
	Items      []models.User        `json:"items"`
	Pagination models.PaginationInfo `json:"pagination"`
}

// SearchUsers handles the user search endpoint
func SearchUsers(c *gin.Context) {
	// Get query parameters
	searchQuery := c.Query("search")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))
	lob := c.Query("lob")

	// Parse the search query
	parsedQuery, err := search.ParseQuery(searchQuery)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Execute the search
	users, total, err := search.SearchUsers(c.Request.Context(), parsedQuery, page, pageSize, lob)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := (total + pageSize - 1) / pageSize

	// Return the response
	c.JSON(http.StatusOK, UserSearchResponse{
		Items: users,
		Pagination: models.PaginationInfo{
			Page:       page,
			PageSize:   pageSize,
			TotalItems: total,
			TotalPages: totalPages,
		},
	})
}
```

### Search Suggestions

```
GET /api/search/suggestions
```

Query Parameters:
- `query`: Partial search query
- `type`: "groups" or "users"

Response:
```json
{
  "suggestions": [
    {
      "type": "field",
      "value": "groupname:",
      "description": "Group name"
    },
    {
      "type": "operator",
      "value": "AND",
      "description": "Logical AND"
    },
    {
      "type": "value",
      "value": "admin",
      "description": "Common value"
    }
  ]
}
```

#### Golang Implementation Example (using Gin):

```go
package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"your-org/adgitops-ui/internal/search"
)

// Suggestion represents a search suggestion
type Suggestion struct {
	Type        string `json:"type"`
	Value       string `json:"value"`
	Description string `json:"description"`
}

// SuggestionsResponse represents the response for search suggestions
type SuggestionsResponse struct {
	Suggestions []Suggestion `json:"suggestions"`
}

// GetSearchSuggestions handles the search suggestions endpoint
func GetSearchSuggestions(c *gin.Context) {
	// Get query parameters
	partialQuery := c.Query("query")
	searchType := c.DefaultQuery("type", "groups")

	// Generate suggestions based on the partial query and type
	suggestions, err := search.GenerateSuggestions(partialQuery, searchType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Map to response format
	response := make([]Suggestion, 0, len(suggestions))
	for _, s := range suggestions {
		response = append(response, Suggestion{
			Type:        s.Type,
			Value:       s.Value,
			Description: s.Description,
		})
	}

	// Return the response
	c.JSON(http.StatusOK, SuggestionsResponse{
		Suggestions: response,
	})
}
```

## Performance Considerations for Golang Implementation

### Query Optimization

1. **Database Indexing**:
   - Create indexes on all searchable fields in your database
   - Consider composite indexes for common search patterns
   - Use full-text indexes for substring searches
   - Example for PostgreSQL with Go:
   ```go
   // Execute index creation SQL
   _, err := db.Exec(`
     CREATE INDEX IF NOT EXISTS idx_groups_groupname ON groups(LOWER(groupname));
     CREATE INDEX IF NOT EXISTS idx_groups_type ON groups(LOWER(type));
     CREATE INDEX IF NOT EXISTS idx_groups_lob ON groups(LOWER(lob));
   `)
   ```

2. **Query Execution**:
   - Use context with timeout for database queries
   - Implement pagination with SQL LIMIT and OFFSET
   - Consider prepared statements for repeated queries
   ```go
   // Example of paginated query with context
   ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
   defer cancel()

   query := `SELECT * FROM groups WHERE LOWER(groupname) LIKE LOWER($1) LIMIT $2 OFFSET $3`
   rows, err := db.QueryContext(ctx, query, "%"+searchTerm+"%", pageSize, (page-1)*pageSize)
   ```

3. **Caching with Go**:
   - Use `github.com/patrickmn/go-cache` for in-memory caching
   - Consider `github.com/go-redis/redis` for distributed caching
   - Implement cache keys based on search parameters
   ```go
   // Example of caching search results
   import (
       "github.com/patrickmn/go-cache"
       "encoding/json"
       "fmt"
   )

   // Create a cache with 5 minute expiration and 10 minute cleanup
   c := cache.New(5*time.Minute, 10*time.Minute)

   // Generate cache key
   cacheKey := fmt.Sprintf("search:%s:page:%d:size:%d", query, page, pageSize)

   // Try to get from cache first
   if cachedResult, found := c.Get(cacheKey); found {
       return cachedResult.(*SearchResult), nil
   }

   // If not in cache, perform search and cache result
   result, err := performSearch(query, page, pageSize)
   if err == nil {
       c.Set(cacheKey, result, cache.DefaultExpiration)
   }
   ```

### Request Handling in Go

1. **Context-Based Cancellation**:
   - Use `context.Context` for propagating cancellation
   - Check for context cancellation in long-running operations
   - Clean up resources when context is cancelled
   ```go
   func SearchHandler(c *gin.Context) {
       // Create a context that cancels when the client disconnects
       ctx, cancel := context.WithCancel(c.Request.Context())
       defer cancel()

       // Use a goroutine for the search operation
       resultCh := make(chan *SearchResult, 1)
       errCh := make(chan error, 1)

       go func() {
           result, err := search.PerformSearch(ctx, query, page, pageSize)
           if err != nil {
               errCh <- err
               return
           }
           resultCh <- result
       }()

       // Wait for result, error, or cancellation
       select {
       case result := <-resultCh:
           c.JSON(http.StatusOK, result)
       case err := <-errCh:
           c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
       case <-ctx.Done():
           // Client disconnected, no need to send response
           log.Println("Client disconnected, search cancelled")
       }
   }
   ```

2. **Rate Limiting in Go**:
   - Use `golang.org/x/time/rate` for token bucket rate limiting
   - Implement per-IP or per-user rate limiting
   - Return 429 status code when rate limit is exceeded
   ```go
   import "golang.org/x/time/rate"

   // Create a rate limiter: 10 requests per second with burst of 30
   limiter := rate.NewLimiter(rate.Limit(10), 30)

   // Middleware for rate limiting
   func RateLimitMiddleware() gin.HandlerFunc {
       return func(c *gin.Context) {
           if !limiter.Allow() {
               c.JSON(http.StatusTooManyRequests, gin.H{
                   "error": "Rate limit exceeded",
               })
               c.Abort()
               return
           }
           c.Next()
       }
   }
   ```

## Error Handling in Golang

The backend should handle the following error cases with appropriate Go error types and HTTP responses:

### Error Types

```go
package errors

import "fmt"

// ErrorCode represents a specific error code
type ErrorCode string

const (
	InvalidSyntax    ErrorCode = "INVALID_SYNTAX"
	UnknownField     ErrorCode = "UNKNOWN_FIELD"
	DatabaseError    ErrorCode = "DATABASE_ERROR"
	TimeoutError     ErrorCode = "TIMEOUT_ERROR"
	PermissionError  ErrorCode = "PERMISSION_ERROR"
)

// AppError represents an application error
type AppError struct {
	Code    ErrorCode         `json:"code"`
	Message string            `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewSyntaxError creates a new syntax error
func NewSyntaxError(message string, position int, token string) *AppError {
	return &AppError{
		Code:    InvalidSyntax,
		Message: message,
		Details: map[string]interface{}{
			"position": position,
			"token":    token,
		},
	}
}

// NewUnknownFieldError creates a new unknown field error
func NewUnknownFieldError(fieldName string) *AppError {
	return &AppError{
		Code:    UnknownField,
		Message: fmt.Sprintf("Unknown field: %s", fieldName),
		Details: map[string]interface{}{
			"fieldName": fieldName,
		},
	}
}
```

### Error Handling Middleware

```go
package middleware

import (
	"github.com/gin-gonic/gin"
	"your-org/adgitops-ui/internal/errors"
	"net/http"
)

// ErrorHandler middleware for handling application errors
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check if there are any errors
		if len(c.Errors) > 0 {
			for _, e := range c.Errors {
				// Check if it's an AppError
				if appErr, ok := e.Err.(*errors.AppError); ok {
					statusCode := getStatusCodeForErrorCode(appErr.Code)
					c.JSON(statusCode, gin.H{"error": appErr})
					return
				}
			}

			// Default error handling
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "INTERNAL_ERROR",
					"message": "An internal error occurred",
				},
			})
		}
	}
}

// getStatusCodeForErrorCode maps error codes to HTTP status codes
func getStatusCodeForErrorCode(code errors.ErrorCode) int {
	switch code {
	case errors.InvalidSyntax, errors.UnknownField:
		return http.StatusBadRequest
	case errors.PermissionError:
		return http.StatusForbidden
	case errors.TimeoutError:
		return http.StatusGatewayTimeout
	default:
		return http.StatusInternalServerError
	}
}
```

### Example Usage in Handler

```go
func SearchGroups(c *gin.Context) {
	searchQuery := c.Query("search")

	// Parse the search query
	parsedQuery, err := search.ParseQuery(searchQuery)
	if err != nil {
		// Check if it's a specific error type
		if syntaxErr, ok := err.(*errors.AppError); ok {
			c.Error(syntaxErr) // This will be caught by the middleware
			return
		}

		// Generic error
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Continue with search...
}
```

### Response Format

```json
{
  "error": {
    "code": "INVALID_SYNTAX",
    "message": "Invalid search syntax: Unmatched quotes",
    "details": {
      "position": 10,
      "token": "\""
    }
  }
}
```

## Testing Strategy with Go

### Unit Tests with Go

1. **Parser Tests with Go's testing package**:
   ```go
   package search_test

   import (
       "testing"

       "your-org/adgitops-ui/internal/search"
       "github.com/stretchr/testify/assert"
   )

   func TestParseQuery(t *testing.T) {
       tests := []struct {
           name        string
           query       string
           expectError bool
           errorCode   string
       }{
           {"Valid exact match", "groupname:org", false, ""},
           {"Valid contains match", "groupname:~org", false, ""},
           {"Valid prefix match", "groupname:org*", false, ""},
           {"Valid AND operator", "type:security AND lob:fm", false, ""},
           {"Valid OR operator", "type:security OR type:admin", false, ""},
           {"Valid NOT operator", "groupname:~admin NOT lob:fm", false, ""},
           {"Invalid syntax", "groupname:", true, "INVALID_SYNTAX"},
           {"Unknown field", "unknown:value", true, "UNKNOWN_FIELD"},
           {"Empty query", "", false, ""},
           {"Very long query", "groupname:" + strings.Repeat("a", 1000), false, ""},
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               result, err := search.ParseQuery(tt.query)

               if tt.expectError {
                   assert.Error(t, err)
                   if appErr, ok := err.(*errors.AppError); ok {
                       assert.Equal(t, tt.errorCode, string(appErr.Code))
                   }
               } else {
                   assert.NoError(t, err)
                   assert.NotNil(t, result)
               }
           })
       }
   }
   ```

2. **Search Engine Tests**:
   ```go
   func TestSearchExecution(t *testing.T) {
       // Setup test database or mock
       db := setupTestDB(t)
       defer cleanupTestDB(t, db)

       // Insert test data
       insertTestGroups(t, db)

       tests := []struct {
           name          string
           query         string
           expectedCount int
       }{
           {"Exact match", "groupname:org", 1},
           {"Contains match", "groupname:~org", 3},
           {"Prefix match", "groupname:org*", 2},
           {"AND operator", "type:security AND lob:fm", 2},
           {"OR operator", "type:security OR type:admin", 5},
           {"NOT operator", "groupname:~admin NOT lob:fm", 1},
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               parsedQuery, err := search.ParseQuery(tt.query)
               assert.NoError(t, err)

               results, total, err := search.SearchGroups(context.Background(), parsedQuery, 1, 10, "")
               assert.NoError(t, err)
               assert.Equal(t, tt.expectedCount, total)
               assert.Len(t, results, min(tt.expectedCount, 10))
           })
       }
   }
   ```

### Integration Tests with Go

1. **API Tests with httptest**:
   ```go
   func TestSearchAPI(t *testing.T) {
       // Setup test server
       router := setupRouter()
       server := httptest.NewServer(router)
       defer server.Close()

       // Setup test database
       db := setupTestDB(t)
       defer cleanupTestDB(t, db)

       // Insert test data
       insertTestGroups(t, db)

       tests := []struct {
           name         string
           endpoint     string
           query        string
           page         int
           pageSize     int
           expectedCode int
           expectedLen  int
       }{
           {"Valid search", "/api/groups", "groupname:~org", 1, 10, 200, 3},
           {"Invalid syntax", "/api/groups", "groupname:", 1, 10, 400, 0},
           {"Unknown field", "/api/groups", "unknown:value", 1, 10, 400, 0},
           {"Pagination", "/api/groups", "type:security", 1, 2, 200, 2},
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               url := fmt.Sprintf("%s%s?search=%s&page=%d&pageSize=%d",
                   server.URL, tt.endpoint, url.QueryEscape(tt.query), tt.page, tt.pageSize)

               resp, err := http.Get(url)
               assert.NoError(t, err)
               assert.Equal(t, tt.expectedCode, resp.StatusCode)

               if resp.StatusCode == 200 {
                   var result map[string]interface{}
                   err = json.NewDecoder(resp.Body).Decode(&result)
                   assert.NoError(t, err)

                   items, ok := result["items"].([]interface{})
                   assert.True(t, ok)
                   assert.Len(t, items, tt.expectedLen)
               }
           })
       }
   }
   ```

2. **Performance Tests with benchmarks**:
   ```go
   func BenchmarkSearch(b *testing.B) {
       // Setup test database with large dataset
       db := setupBenchmarkDB(b)
       defer cleanupBenchmarkDB(b, db)

       queries := []string{
           "groupname:org",
           "groupname:~org",
           "type:security AND lob:fm",
           "type:security OR type:admin",
           "groupname:~admin NOT lob:fm",
       }

       for _, query := range queries {
           b.Run(query, func(b *testing.B) {
               parsedQuery, _ := search.ParseQuery(query)
               b.ResetTimer()

               for i := 0; i < b.N; i++ {
                   _, _, err := search.SearchGroups(context.Background(), parsedQuery, 1, 20, "")
                   if err != nil {
                       b.Fatal(err)
                   }
               }
           })
       }
   }
   ```

### Example Test Cases

| Test Case | Input | Expected Output | Go Test Function |
|-----------|-------|-----------------|------------------|
| Exact Match | `groupname:org` | Groups with name exactly "org" | `TestExactMatch` |
| Contains Match | `groupname:~org` | Groups with "org" in their name | `TestContainsMatch` |
| Prefix Match | `groupname:org*` | Groups with names starting with "org" | `TestPrefixMatch` |
| AND Operator | `type:security AND lob:fm` | Security groups in FM LOB | `TestAndOperator` |
| OR Operator | `type:security OR type:admin` | Security or admin groups | `TestOrOperator` |
| NOT Operator | `groupname:~admin NOT lob:fm` | Admin groups not in FM LOB | `TestNotOperator` |
| Invalid Syntax | `groupname:` | Error: Invalid syntax | `TestInvalidSyntax` |
| Unknown Field | `unknown:value` | Error: Unknown field | `TestUnknownField` |

## Implementation Checklist for Go

- [ ] Set up Go project structure with proper packages
- [ ] Implement query parser with Go's lexer/parser patterns
- [ ] Implement search engine with database integration
- [ ] Create API endpoints using Gin or similar framework
- [ ] Add error handling with custom error types
- [ ] Implement context-based cancellation
- [ ] Implement caching with go-cache or Redis
- [ ] Write unit tests with testing package and testify
- [ ] Write integration tests with httptest
- [ ] Write benchmarks for performance testing
- [ ] Optimize performance with profiling
- [ ] Document API endpoints with Swagger/OpenAPI
