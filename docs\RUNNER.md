# ADGitOps UI Runner

It provides a unified, cross-platform way to run development, build, and start commands for the ADGitOps UI application.

## Building the Runner

### Windows
```
build-runner.bat
```

### Linux/macOS
```
chmod +x build-runner.sh
./build-runner.sh
```

## Usage

The runner supports the same commands and options as the original scripts:

```
runner [dev|build|start] [options]
```

### Commands

#### Development Mode
```
runner dev
```

Starts both the backend and frontend in development mode with hot reload.

**New Features:**
- Checks if servers are already running and prompts to restart them
- Verifies that servers actually started successfully
- Provides a 10-second timeout for the restart prompt
- Automatically cleans up processes when exiting

#### Build
```
runner build [options]
```

Builds the application using GoReleaser, including the frontend embedded in the binary.

Options:
- `--snapshot` - Create a snapshot build (default)
- `--no-snapshot` - Create a release build (requires a Git tag)
- `--skip-docker` - Skip Docker image creation
- `--registry REG` - Specify the Docker registry (default: localhost)
- `--clean` - Clean before building (default)
- `--no-clean` - Skip cleaning before building

#### Start
```
runner start [options]
```

Starts the built application.

Options:
- `--port PORT` - Specify the server port (default: 8080)
- `--repo-dir DIR` - Path to local GitLab repository clone
- `--export-dir DIR` - Directory for exported data (default: export)
- `--poll-seconds SEC` - Polling frequency in seconds (default: 300)

## Help

For more information on any command, use the `--help` flag:

```
runner --help
runner dev --help
runner build --help
runner start --help
```