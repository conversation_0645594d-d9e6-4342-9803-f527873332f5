package search

import (
	"adgitops-ui/src/backend/models"
	"context"
	"fmt"
	"strings"
)

// SearchGroups searches for groups matching the query
// This function implements the search behavior as described in the search-behaviour.md documentation
func (s *BleveSearchService) SearchGroups(ctx context.Context, query string, groups []models.Group) ([]models.Group, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.initialized {
		return nil, fmt.Errorf("search service not initialized")
	}

	// If the query is empty or _all:true, return all groups
	if strings.TrimSpace(query) == "" || strings.TrimSpace(query) == "_all:true" {
		return groups, nil
	}

	// Handle simple keyword search (no special syntax)
	if IsSimpleKeywordSearch(query) {
		// For simple keyword searches, we want to match partial words in all fields
		// as per the documentation for groups
		var results []models.Group
		value := strings.ToLower(query)

		for _, group := range groups {
			// Check all fields for partial matches
			groupname := strings.ToLower(group.Groupname)
			groupType := strings.ToLower(group.Type)
			description := strings.ToLower(group.Description)
			lob := strings.ToLower(group.Lob)

			// Check if any field contains the search term
			if strings.Contains(groupname, value) ||
				strings.Contains(groupType, value) ||
				strings.Contains(description, value) ||
				strings.Contains(lob, value) {
				results = append(results, group)
				continue
			}

			// Check members
			for _, member := range group.Members {
				if strings.Contains(strings.ToLower(member.Name), value) {
					results = append(results, group)
					break
				}
			}
		}

		// Debug output
		fmt.Printf("SearchGroups: simple keyword search returned %d results\n", len(results))
		for _, group := range results {
			fmt.Printf("SearchGroups: simple keyword result: %s\n", group.Groupname)
		}

		return results, nil
	}

	// Fix parentheses in the query if needed
	query = FixParentheses(query)

	// Create a new search engine
	engine := NewEngine()

	// Clean the query by removing type filters
	cleanQuery := CleanQuery(query, "group")

	// Use the generic search engine for all queries
	fmt.Printf("SearchGroups: using generic engine with query=%s\n", cleanQuery)

	// Use the engine to search
	filteredResults, err := engine.SearchGroups(ctx, cleanQuery, groups)
	if err != nil {
		// Log the error for debugging
		fmt.Printf("SearchGroups: error searching groups: %v\n", err)
		// Return the original error to preserve the error message
		return nil, err
	}

	return filteredResults, nil
}
