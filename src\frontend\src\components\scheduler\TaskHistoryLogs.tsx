import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  RefreshCw,
  Search,
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';

import api from '@/api/client';
import type {
  ScanLogEntry,
  ScanLogsOverview,
  ScanLogFilter,
  ScanStatus,
  ScanLogLevel
} from '@/types/scanLogs';
import {
  ScanStatusColors,
  LogLevelColors,
  formatDuration,
  formatTimestamp,
  formatRelativeTime,
  getScanStatusText,
  getLogLevelText
} from '@/types/scanLogs';

interface TaskHistoryLogsProps {
  isExpanded?: boolean;
  onToggleExpanded?: () => void;
}

const TaskHistoryLogs: React.FC<TaskHistoryLogsProps> = ({
  isExpanded = false,
  onToggleExpanded
}) => {
  const [overview, setOverview] = useState<ScanLogsOverview | null>(null);
  const [logs, setLogs] = useState<ScanLogEntry[]>([]);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);

  // Filter state
  const [filter, setFilter] = useState<ScanLogFilter>({
    page: 1,
    pageSize: isExpanded ? 50 : 20
  });

  // Backend now handles milestone filtering, so no frontend filtering needed
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<ScanLogLevel | ''>('');
  const [selectedStatus, setSelectedStatus] = useState<ScanStatus | ''>('');

  // Pagination state
  const [totalPages, setTotalPages] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);

  // Load overview data
  const loadOverview = useCallback(async () => {
    try {
      const overviewData = await api.scanLogs.getOverview();
      setOverview(overviewData);
    } catch (err: any) {
      console.error('Failed to load scan logs overview:', err);
      // Check if it's a 404 error (API not implemented)
      if (err?.message?.includes('404') || err?.status === 404) {
        setError('Scan logs API is not yet implemented');
      } else {
        setError('Failed to load overview data');
      }
      setOverview(null);
    }
  }, []);

  // Load logs data
  const loadLogs = useCallback(async (currentFilter?: ScanLogFilter) => {
    try {
      const filterToUse = currentFilter || {
        ...filter,
        searchQuery: searchQuery || undefined,
        level: selectedLevel || undefined,
        status: selectedStatus || undefined,
      };

      const response = await api.scanLogs.getLogs(filterToUse);
      setLogs(response.logs || []);
      setTotalPages(response.totalPages || 1);
      setTotalLogs(response.total || 0);
    } catch (err: any) {
      console.error('Failed to load scan logs:', err);
      // Check if it's a 404 error (API not implemented)
      if (err?.message?.includes('404') || err?.status === 404) {
        setError('Scan logs API is not yet implemented');
      } else {
        setError('Failed to load scan logs');
      }
      setLogs([]);
      setTotalPages(1);
      setTotalLogs(0);
    }
  }, [filter, searchQuery, selectedLevel, selectedStatus]);



  // Load all data (for manual refresh)
  const loadAllData = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }
      setError(null);

      await Promise.all([
        loadOverview(),
        loadLogs()
      ]);

      setLastRefreshTime(new Date());
    } catch (err) {
      console.error('Failed to load data:', err);
      setError('Failed to load scan logs and summaries');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [loadOverview, loadLogs]);

  // Initial load
  useEffect(() => {
    const initialLoad = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load overview
        try {
          const overviewData = await api.scanLogs.getOverview();
          setOverview(overviewData);
        } catch (err: any) {
          console.error('Failed to load scan logs overview:', err);
          if (err?.message?.includes('404') || err?.status === 404) {
            setError('Scan logs API is not yet implemented');
          } else {
            setError('Failed to load overview data');
          }
          setOverview(null);
        }

        // Load logs
        try {
          const currentFilter = {
            ...filter,
            searchQuery: searchQuery || undefined,
            level: selectedLevel || undefined,
            status: selectedStatus || undefined,
          };
          const response = await api.scanLogs.getLogs(currentFilter);
          setLogs(response.logs || []);
          setTotalPages(response.totalPages || 1);
          setTotalLogs(response.total || 0);
        } catch (err: any) {
          console.error('Failed to load scan logs:', err);
          setLogs([]);
          setTotalPages(1);
          setTotalLogs(0);
        }



        setLastRefreshTime(new Date());
      } catch (err) {
        console.error('Failed to load data:', err);
        setError('Failed to load scan logs');
      } finally {
        setLoading(false);
      }
    };

    initialLoad();
  }, []); // Empty dependency array for initial load only

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        setRefreshing(true);
        setError(null);

        // Load overview
        try {
          const overviewData = await api.scanLogs.getOverview();
          setOverview(overviewData);
        } catch (err: any) {
          console.error('Failed to load scan logs overview:', err);
          setOverview(null);
        }

        // Load logs
        try {
          const currentFilter = {
            ...filter,
            searchQuery: searchQuery || undefined,
            level: selectedLevel || undefined,
            status: selectedStatus || undefined,
          };
          const response = await api.scanLogs.getLogs(currentFilter);
          setLogs(response.logs || []);
          setTotalPages(response.totalPages || 1);
          setTotalLogs(response.total || 0);
        } catch (err: any) {
          console.error('Failed to load scan logs:', err);
          setLogs([]);
          setTotalPages(1);
          setTotalLogs(0);
        }



        setLastRefreshTime(new Date());
      } catch (err) {
        console.error('Failed to refresh data:', err);
        setError('Failed to refresh scan logs');
      } finally {
        setRefreshing(false);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, []); // Empty dependency array to prevent recreation

  // Update page size when expanded state changes
  useEffect(() => {
    setFilter(prev => ({
      ...prev,
      pageSize: isExpanded ? 50 : 20
    }));
  }, [isExpanded]);

  // Handle refresh
  const handleRefresh = () => {
    loadAllData(false);
  };

  // Handle search
  const handleSearch = () => {
    setFilter(prev => ({ ...prev, page: 1 }));
    loadLogs();
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedLevel('');
    setSelectedStatus('');
    setFilter({ page: 1, pageSize: isExpanded ? 50 : 20 });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilter(prev => ({ ...prev, page: newPage }));
  };

  // Get status icon
  const getStatusIcon = (status: ScanStatus) => {
    switch (status) {
      case 'running':
        return <Loader className="h-4 w-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      case 'cancelled':
        return <X className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Task History & Logs</span>
            {onToggleExpanded && (
              <Button variant="ghost" size="sm" onClick={onToggleExpanded}>
                {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Task History & Logs</span>
            {onToggleExpanded && (
              <Button variant="ghost" size="sm" onClick={onToggleExpanded}>
                {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span>{error}</span>
          </div>
          <Button onClick={handleRefresh} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <span>Task History & Logs</span>
            {overview && (
              <Badge variant="secondary">
                {overview.totalScans} total scans
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center space-x-2">
            {lastRefreshTime && (
              <span className="text-sm text-gray-500">
                Updated: {formatRelativeTime(lastRefreshTime.toISOString())}
              </span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {onToggleExpanded && (
              <Button variant="ghost" size="sm" onClick={onToggleExpanded}>
                {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>


        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search logs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-64"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Level: {selectedLevel || 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSelectedLevel('')}>All Levels</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedLevel('info')}>Info</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedLevel('warning')}>Warning</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedLevel('error')}>Error</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Status: {selectedStatus || 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSelectedStatus('')}>All Status</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedStatus('running')}>Running</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedStatus('completed')}>Completed</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedStatus('failed')}>Failed</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" size="sm" onClick={handleSearch}>
            Search
          </Button>

          <Button variant="ghost" size="sm" onClick={handleClearFilters}>
            Clear Filters
          </Button>
        </div>

        {/* Content - Logs Only (Summaries moved to Completed Tasks) */}
        <div className="space-y-4">

          {(logs?.length || 0) === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No logs found matching the current filters
            </div>
          ) : (
            <div className="space-y-2">
              {(logs || []).slice(0, isExpanded ? (logs?.length || 0) : 10).map((log) => (
                  <div
                    key={log.id}
                    className="border rounded-lg p-3 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Badge className={`${LogLevelColors[log.level]} border-0`}>
                          {getLogLevelText(log.level)}
                        </Badge>
                        <span className="text-sm font-medium">{log.step || 'General'}</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(log.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 mb-1">{log.message}</p>
                    {log.details && (
                      <p className="text-xs text-gray-500">{log.details}</p>
                    )}
                    <div className="flex items-center space-x-4 text-xs text-gray-400 mt-2">
                      <span>Scan: {log.scanId}</span>
                      <span>Group: {log.groupName}</span>
                      <span>Repo: {log.repoId}</span>
                    </div>
                  </div>
                ))}

              {!isExpanded && (logs?.length || 0) > 10 && (
                <div className="text-center py-4">
                  <Button variant="outline" onClick={onToggleExpanded}>
                    View All Logs ({logs?.length || 0})
                  </Button>
                </div>
              )}
            </div>
          )}

        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              Showing {((filter.page || 1) - 1) * (filter.pageSize || 20) + 1} to {Math.min((filter.page || 1) * (filter.pageSize || 20), totalLogs)} of {totalLogs} logs
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange((filter.page || 1) - 1)}
                disabled={(filter.page || 1) <= 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {filter.page || 1} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange((filter.page || 1) + 1)}
                disabled={(filter.page || 1) >= totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TaskHistoryLogs;
