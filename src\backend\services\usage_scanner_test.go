package services

import (
	"context"
	"os"
	"testing"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockUsageSourceManager for testing
type MockUsageSourceManager struct {
	mock.Mock
}

func (m *MockUsageSourceManager) CreateSource(source models.UsageSource) (models.UsageSource, error) {
	args := m.Called(source)
	return args.Get(0).(models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) GetSource(id string) (models.UsageSource, error) {
	args := m.Called(id)
	return args.Get(0).(models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) GetAllSources() ([]models.UsageSource, error) {
	args := m.Called()
	return args.Get(0).([]models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) GetActiveSourcesByType(sourceType models.SourceType) ([]models.UsageSource, error) {
	args := m.Called(sourceType)
	return args.Get(0).([]models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) UpdateSource(source models.UsageSource) (models.UsageSource, error) {
	args := m.Called(source)
	return args.Get(0).(models.UsageSource), args.Error(1)
}

func (m *MockUsageSourceManager) DeleteSource(id string) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockUsageSourceManager) LoadSources() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockUsageSourceManager) SaveSources() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockUsageSourceManager) ValidateSource(source models.UsageSource) error {
	args := m.Called(source)
	return args.Error(0)
}

func (m *MockUsageSourceManager) TestSourceConnection(id string) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockUsageSourceManager) InvalidateCache() {
	m.Called()
}

func setupTestUsageScannerService(t *testing.T) (*UsageScannerService, *MockUsageSourceManager, string, func()) {
	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "usage_scanner_test")
	require.NoError(t, err)

	// Create mock source manager
	mockSourceManager := &MockUsageSourceManager{}

	// Create scan logger
	scanLogger := NewScanLogger()

	// Create scanner service
	scanner := NewUsageScannerService(mockSourceManager, tempDir, scanLogger)

	// Return cleanup function
	cleanup := func() {
		scanner.Stop()
		os.RemoveAll(tempDir)
	}

	return scanner, mockSourceManager, tempDir, cleanup
}

func createTestUsageSource() models.UsageSource {
	return models.UsageSource{
		ID:            "test-source-1",
		Name:          "Test Git Source",
		Type:          models.SourceTypeGit,
		IsActive:      true,
		ScanFrequency: 300,
		GitConfig: &models.GitSourceConfig{
			RepoURL:  "https://github.com/test/repo.git",
			Branch:   "main",
			AuthType: "token",
			Token:    "test-token",
		},
	}
}

func TestUsageScannerService_NewUsageScannerService(t *testing.T) {
	scanner, _, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	assert.NotNil(t, scanner)
	assert.NotNil(t, scanner.sourceManager)
	assert.NotNil(t, scanner.handlerRegistry)
	assert.NotNil(t, scanner.statusManager)
	assert.NotNil(t, scanner.workerPool)
	assert.False(t, scanner.isRunning)
}

func TestUsageScannerService_StartStop(t *testing.T) {
	scanner, mockSourceManager, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Mock GetAllSources for the scheduler check
	mockSourceManager.On("GetAllSources").Return([]models.UsageSource{}, nil)

	// Start the scanner
	err := scanner.Start()
	assert.NoError(t, err)
	assert.True(t, scanner.isRunning)

	// Stop the scanner
	scanner.Stop()
	assert.False(t, scanner.isRunning)
}

func TestUsageScannerService_ScanGroupUsage(t *testing.T) {
	scanner, mockSourceManager, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Create test source
	testSource := createTestUsageSource()

	// Mock GetAllSources to return our test source (called by scheduler and scan)
	mockSourceManager.On("GetAllSources").Return([]models.UsageSource{testSource}, nil)

	// Start the scanner
	err := scanner.Start()
	require.NoError(t, err)

	// Create scan request
	request := models.UsageScanRequest{
		GroupName: "test-group",
		RepoID:    "test-repo",
		Force:     false,
	}

	// Perform scan
	ctx := context.Background()
	err = scanner.ScanGroupUsage(ctx, request)
	assert.NoError(t, err)

	// Verify that scan status was created
	status, err := scanner.GetScanStatus("test-group", "test-repo")
	assert.NoError(t, err)
	assert.Equal(t, "test-group", status.GroupName)
	assert.Equal(t, "test-repo", status.RepoID)
	assert.Equal(t, 1, status.SourcesTotal)
	assert.True(t, status.InProgress)

	// Wait for scan to complete or timeout
	timeout := time.After(5 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Log("Timeout waiting for scan to complete")
			goto stopScanner
		case <-ticker.C:
			status, err := scanner.GetScanStatus("test-group", "test-repo")
			if err == nil && !status.InProgress {
				t.Log("Scan completed")
				goto stopScanner
			}
		}
	}

stopScanner:
	// Stop the scanner
	scanner.Stop()
}

func TestUsageScannerService_ScanGroupUsage_NoActiveSources(t *testing.T) {
	scanner, mockSourceManager, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Mock GetAllSources to return empty slice
	mockSourceManager.On("GetAllSources").Return([]models.UsageSource{}, nil)

	// Create scan request
	request := models.UsageScanRequest{
		GroupName: "test-group",
		RepoID:    "test-repo",
		Force:     false,
	}

	// Perform scan
	ctx := context.Background()
	err := scanner.ScanGroupUsage(ctx, request)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no active sources found")
}

func TestUsageScannerService_ScanGroupUsage_SpecificSources(t *testing.T) {
	scanner, mockSourceManager, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Create test source
	testSource := createTestUsageSource()

	// Mock GetAllSources for scheduler
	mockSourceManager.On("GetAllSources").Return([]models.UsageSource{}, nil)

	// Mock GetSource to return our test source
	mockSourceManager.On("GetSource", "test-source-1").Return(testSource, nil)

	// Start the scanner
	err := scanner.Start()
	require.NoError(t, err)

	// Create scan request with specific source
	request := models.UsageScanRequest{
		GroupName: "test-group",
		RepoID:    "test-repo",
		SourceIDs: []string{"test-source-1"},
		Force:     false,
	}

	// Perform scan
	ctx := context.Background()
	err = scanner.ScanGroupUsage(ctx, request)
	assert.NoError(t, err)

	// Verify that scan status was created
	status, err := scanner.GetScanStatus("test-group", "test-repo")
	assert.NoError(t, err)
	assert.Equal(t, "test-group", status.GroupName)
	assert.Equal(t, "test-repo", status.RepoID)
	assert.Equal(t, 1, status.SourcesTotal)

	// Stop the scanner
	scanner.Stop()
}

func TestUsageScannerService_GetUsageResults(t *testing.T) {
	scanner, _, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Test getting results when none exist
	results, err := scanner.GetUsageResults("test-group", "test-repo", 1, 10)
	assert.NoError(t, err)
	assert.Empty(t, results.Results)
	assert.Equal(t, 0, results.Total)
	assert.Equal(t, 1, results.Page)
	assert.Equal(t, 10, results.PageSize)
}

func TestUsageScannerService_ClearUsageResults(t *testing.T) {
	scanner, _, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Clear results (should not error even if none exist)
	err := scanner.ClearUsageResults("test-group", "test-repo")
	assert.NoError(t, err)
}

func TestUsageScannerService_GetUsageStatistics(t *testing.T) {
	scanner, mockSourceManager, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Create test sources
	testSources := []models.UsageSource{
		{
			ID:       "source-1",
			Name:     "Active Source",
			IsActive: true,
		},
		{
			ID:       "source-2",
			Name:     "Inactive Source",
			IsActive: false,
		},
	}

	// Mock GetAllSources
	mockSourceManager.On("GetAllSources").Return(testSources, nil)

	// Get statistics
	stats, err := scanner.GetUsageStatistics("test-repo")
	assert.NoError(t, err)
	assert.Equal(t, 2, stats.SourcesConfigured)
	assert.Equal(t, 1, stats.SourcesActive)
}

func TestScanStatusManager_GetStatus_NotFound(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "status_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	manager := NewScanStatusManager(tempDir)

	_, err = manager.GetStatus("non-existent-group", "non-existent-repo")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "scan status not found")
}

func TestScanStatusManager_UpdateStatus(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "status_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	manager := NewScanStatusManager(tempDir)

	status := models.UsageScanStatus{
		GroupName:        "test-group",
		RepoID:           "test-repo",
		SourcesTotal:     2,
		SourcesScanned:   1,
		InProgress:       true,
		LastScanTime:     time.Now(),
		CompletedSources: []string{"source-1"},
		PendingSources:   []string{"source-2"},
		TotalUsages:      5,
	}

	// Update status
	err = manager.UpdateStatus(status)
	assert.NoError(t, err)

	// Retrieve status
	retrieved, err := manager.GetStatus("test-group", "test-repo")
	assert.NoError(t, err)
	assert.Equal(t, status.GroupName, retrieved.GroupName)
	assert.Equal(t, status.RepoID, retrieved.RepoID)
	assert.Equal(t, status.SourcesTotal, retrieved.SourcesTotal)
	assert.Equal(t, status.SourcesScanned, retrieved.SourcesScanned)
	assert.Equal(t, status.InProgress, retrieved.InProgress)
}

func TestScanWorkerPool_StartStop(t *testing.T) {
	pool := NewScanWorkerPool(2)

	assert.False(t, pool.IsRunning())

	pool.Start()
	assert.True(t, pool.IsRunning())

	pool.Stop()
	assert.False(t, pool.IsRunning())
}

func TestScanWorkerPool_SubmitJob_NotRunning(t *testing.T) {
	pool := NewScanWorkerPool(2)

	job := ScanJob{
		GroupName: "test-group",
		RepoID:    "test-repo",
		Source:    createTestUsageSource(),
		Handler:   &MockSourceHandler{sourceType: models.SourceTypeGit},
		Context:   context.Background(),
	}

	err := pool.SubmitJob(job)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "worker pool is not running")
}

func TestUsageScannerService_GetActiveScans(t *testing.T) {
	scanner, mockSourceManager, _, cleanup := setupTestUsageScannerService(t)
	defer cleanup()

	// Mock GetAllSources for scheduler
	mockSourceManager.On("GetAllSources").Return([]models.UsageSource{}, nil)

	t.Run("NoActiveScans", func(t *testing.T) {
		activeScans, err := scanner.GetActiveScans()
		assert.NoError(t, err)
		assert.Empty(t, activeScans)
	})

	t.Run("WithActiveScans", func(t *testing.T) {
		// Directly add active scan statuses to the status manager
		activeStatus1 := models.UsageScanStatus{
			GroupName:        "test-group-1",
			RepoID:           "test-repo-1",
			SourcesTotal:     3,
			SourcesScanned:   1,
			InProgress:       true,
			LastScanTime:     time.Now(),
			CompletedSources: []string{"source1"},
			PendingSources:   []string{"source2", "source3"},
			FailedSources:    []models.SourceScanFailure{},
			TotalUsages:      5,
		}

		activeStatus2 := models.UsageScanStatus{
			GroupName:        "test-group-2",
			RepoID:           "test-repo-1",
			SourcesTotal:     2,
			SourcesScanned:   0,
			InProgress:       true,
			LastScanTime:     time.Now(),
			CompletedSources: []string{},
			PendingSources:   []string{"source1", "source2"},
			FailedSources:    []models.SourceScanFailure{},
			TotalUsages:      0,
		}

		// Update status manager directly
		err := scanner.statusManager.UpdateStatus(activeStatus1)
		require.NoError(t, err)

		err = scanner.statusManager.UpdateStatus(activeStatus2)
		require.NoError(t, err)

		// Get active scans
		activeScans, err := scanner.GetActiveScans()
		assert.NoError(t, err)
		assert.Len(t, activeScans, 2)

		// Verify active scan properties
		for _, task := range activeScans {
			assert.Equal(t, "usage_scan", task.Type)
			assert.Equal(t, "running", task.Status)
			assert.NotEmpty(t, task.ID)
			assert.NotEmpty(t, task.Name)
			assert.NotEmpty(t, task.StartedAt)
			assert.NotNil(t, task.Progress)
			assert.GreaterOrEqual(t, task.Progress.Current, 0)
			assert.LessOrEqual(t, task.Progress.Current, task.Progress.Total)
			assert.GreaterOrEqual(t, task.Progress.Percentage, 0)
			assert.LessOrEqual(t, task.Progress.Percentage, 100)

			// Check specific values based on group name
			if task.ID == "test-group-1:test-repo-1" {
				assert.Equal(t, "Usage Scan: test-group-1", task.Name)
				assert.Equal(t, 3, task.Progress.Total)
				assert.Equal(t, 1, task.Progress.Current)
				assert.Equal(t, 33, task.Progress.Percentage) // 1/3 * 100 = 33
			} else if task.ID == "test-group-2:test-repo-1" {
				assert.Equal(t, "Usage Scan: test-group-2", task.Name)
				assert.Equal(t, 2, task.Progress.Total)
				assert.Equal(t, 0, task.Progress.Current)
				assert.Equal(t, 0, task.Progress.Percentage)
			}
		}
	})

	t.Run("NilStatusManager", func(t *testing.T) {
		// Create scanner with nil status manager
		scannerWithNilStatus := &UsageScannerService{
			statusManager: nil,
		}

		activeScans, err := scannerWithNilStatus.GetActiveScans()
		assert.NoError(t, err)
		assert.Empty(t, activeScans)
	})
}
