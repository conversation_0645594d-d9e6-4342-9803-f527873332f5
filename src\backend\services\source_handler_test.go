package services

import (
	"context"
	"testing"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
)

func TestSourceHandlerRegistry(t *testing.T) {
	registry := NewSourceHandlerRegistry()

	// Test getting supported types
	types := registry.GetSupportedTypes()
	assert.Len(t, types, 3)
	assert.Contains(t, types, models.SourceTypeGit)
	assert.Contains(t, types, models.SourceTypeAPI)
	assert.Contains(t, types, models.SourceTypeFile)

	// Test getting handlers
	gitHandler, err := registry.GetHandler(models.SourceTypeGit)
	assert.NoError(t, err)
	assert.NotNil(t, gitHandler)
	assert.Equal(t, models.SourceTypeGit, gitHandler.GetSourceType())

	apiHandler, err := registry.GetHandler(models.SourceTypeAPI)
	assert.NoError(t, err)
	assert.NotNil(t, apiHandler)
	assert.Equal(t, models.SourceTypeAPI, apiHandler.GetSourceType())

	fileHandler, err := registry.GetHandler(models.SourceTypeFile)
	assert.NoError(t, err)
	assert.NotNil(t, fileHandler)
	assert.Equal(t, models.SourceTypeFile, fileHandler.GetSourceType())

	// Test unsupported type
	_, err = registry.GetHandler("unsupported")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported source type")
}

func TestSourceHandlerRegistry_RegisterHandler(t *testing.T) {
	registry := NewSourceHandlerRegistry()

	// Register a custom handler
	customType := models.SourceType("custom")
	registry.RegisterHandler(customType, func() SourceHandler {
		return &MockSourceHandler{sourceType: customType}
	})

	// Test getting the custom handler
	handler, err := registry.GetHandler(customType)
	assert.NoError(t, err)
	assert.NotNil(t, handler)
	assert.Equal(t, customType, handler.GetSourceType())

	// Test that it's included in supported types
	types := registry.GetSupportedTypes()
	assert.Contains(t, types, customType)
}

func TestMatchesPatterns(t *testing.T) {
	tests := []struct {
		name            string
		filePath        string
		includePatterns []string
		excludePatterns []string
		expected        bool
	}{
		{
			name:            "no patterns - include all",
			filePath:        "test.yaml",
			includePatterns: []string{},
			excludePatterns: []string{},
			expected:        true,
		},
		{
			name:            "include yaml files",
			filePath:        "config.yaml",
			includePatterns: []string{"*.yaml"},
			excludePatterns: []string{},
			expected:        true,
		},
		{
			name:            "exclude yaml files",
			filePath:        "config.yaml",
			includePatterns: []string{},
			excludePatterns: []string{"*.yaml"},
			expected:        false,
		},
		{
			name:            "include yaml but exclude config",
			filePath:        "config.yaml",
			includePatterns: []string{"*.yaml"},
			excludePatterns: []string{"config.*"},
			expected:        false,
		},
		{
			name:            "include json files - no match",
			filePath:        "config.yaml",
			includePatterns: []string{"*.json"},
			excludePatterns: []string{},
			expected:        false,
		},
		{
			name:            "prefix pattern match",
			filePath:        "config/app.yaml",
			includePatterns: []string{"config/*"},
			excludePatterns: []string{},
			expected:        true,
		},
		{
			name:            "prefix pattern no match",
			filePath:        "data/app.yaml",
			includePatterns: []string{"config/*"},
			excludePatterns: []string{},
			expected:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchesPatterns(tt.filePath, tt.includePatterns, tt.excludePatterns)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMatchesPattern(t *testing.T) {
	tests := []struct {
		name     string
		filePath string
		pattern  string
		expected bool
	}{
		{
			name:     "wildcard matches all",
			filePath: "anything.txt",
			pattern:  "*",
			expected: true,
		},
		{
			name:     "yaml extension match",
			filePath: "config.yaml",
			pattern:  "*.yaml",
			expected: true,
		},
		{
			name:     "yaml extension no match",
			filePath: "config.json",
			pattern:  "*.yaml",
			expected: false,
		},
		{
			name:     "prefix pattern match",
			filePath: "config/app.yaml",
			pattern:  "config/*",
			expected: true,
		},
		{
			name:     "prefix pattern no match",
			filePath: "data/app.yaml",
			pattern:  "config/*",
			expected: false,
		},
		{
			name:     "exact match",
			filePath: "config.yaml",
			pattern:  "config.yaml",
			expected: true,
		},
		{
			name:     "exact no match",
			filePath: "config.yaml",
			pattern:  "app.yaml",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchesPattern(tt.filePath, tt.pattern)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFindGroupUsageInContent(t *testing.T) {
	content := `# Configuration file
groups:
  - "test-group"
  - 'another-group'
  - third-group

members:
  - user1
  - test-group
  - user2

# Comment with test-group reference
access: test-group
`

	results := findGroupUsageInContent(content, "test-group", "config.yaml")

	// Should find 4 occurrences of "test-group" (line 3, 9, 12 comment, 13 access)
	assert.Len(t, results, 4)

	// Check first result
	assert.Equal(t, "config.yaml", results[0].FilePath)
	assert.Equal(t, 3, results[0].LineNumber) // Line with "test-group"
	assert.Contains(t, results[0].Context, "test-group")
	assert.Equal(t, "exact", results[0].MatchType)

	// Check that all results have the correct file path
	for _, result := range results {
		assert.Equal(t, "config.yaml", result.FilePath)
		assert.Contains(t, result.Context, "test-group")
	}
}

func TestContainsGroupReference(t *testing.T) {
	tests := []struct {
		name      string
		line      string
		groupName string
		expected  bool
	}{
		{
			name:      "quoted with double quotes",
			line:      `groups: ["test-group"]`,
			groupName: "test-group",
			expected:  true,
		},
		{
			name:      "quoted with single quotes",
			line:      `groups: ['test-group']`,
			groupName: "test-group",
			expected:  true,
		},
		{
			name:      "unquoted reference",
			line:      `member: test-group`,
			groupName: "test-group",
			expected:  true,
		},
		{
			name:      "no reference",
			line:      `groups: ["other-group"]`,
			groupName: "test-group",
			expected:  false,
		},
		{
			name:      "partial match in word",
			line:      `groups: ["test-group-admin"]`,
			groupName: "test-group",
			expected:  true, // Current implementation finds substrings
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := containsGroupReference(tt.line, tt.groupName)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSplitLines(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected []string
	}{
		{
			name:     "unix line endings",
			content:  "line1\nline2\nline3",
			expected: []string{"line1", "line2", "line3"},
		},
		{
			name:     "windows line endings",
			content:  "line1\r\nline2\r\nline3",
			expected: []string{"line1", "line2", "line3"},
		},
		{
			name:     "mixed line endings",
			content:  "line1\nline2\r\nline3",
			expected: []string{"line1", "line2", "line3"},
		},
		{
			name:     "single line",
			content:  "single line",
			expected: []string{"single line"},
		},
		{
			name:     "empty content",
			content:  "",
			expected: []string{},
		},
		{
			name:     "trailing newline",
			content:  "line1\nline2\n",
			expected: []string{"line1", "line2", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := splitLines(tt.content)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetContextAroundLine(t *testing.T) {
	lines := []string{
		"line 0",
		"line 1",
		"line 2", // target line
		"line 3",
		"line 4",
	}

	// Test with context size 1
	context := getContextAroundLine(lines, 2, 1)
	expected := "line 1\nline 2\nline 3"
	assert.Equal(t, expected, context)

	// Test at beginning of file
	context = getContextAroundLine(lines, 0, 2)
	expected = "line 0\nline 1\nline 2"
	assert.Equal(t, expected, context)

	// Test at end of file
	context = getContextAroundLine(lines, 4, 2)
	expected = "line 2\nline 3\nline 4"
	assert.Equal(t, expected, context)
}

// MockSourceHandler for testing
type MockSourceHandler struct {
	sourceType models.SourceType
}

func (m *MockSourceHandler) Initialize(source models.UsageSource) error {
	return nil
}

func (m *MockSourceHandler) ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error) {
	return []models.UsageResult{}, nil
}

func (m *MockSourceHandler) GetSourceType() models.SourceType {
	return m.sourceType
}

func (m *MockSourceHandler) ValidateConfig(source models.UsageSource) error {
	return nil
}

func (m *MockSourceHandler) TestConnection(ctx context.Context) error {
	return nil
}

func (m *MockSourceHandler) GetStatus(ctx context.Context) (models.SourceStatus, error) {
	return models.SourceStatus{}, nil
}

func (m *MockSourceHandler) Cleanup() error {
	return nil
}

func TestGitSourceHandler_Initialize(t *testing.T) {
	handler := NewGitSourceHandler()

	// Test with valid Git source
	source := models.UsageSource{
		ID:   "test-git-source",
		Name: "Test Git Source",
		Type: models.SourceTypeGit,
		GitConfig: &models.GitSourceConfig{
			RepoURL:  "https://github.com/test/repo.git",
			Branch:   "main",
			AuthType: "token",
			Token:    "test-token",
		},
	}

	err := handler.Initialize(source)
	assert.NoError(t, err)
	assert.Equal(t, models.SourceTypeGit, handler.GetSourceType())

	// Test with missing Git config
	invalidSource := models.UsageSource{
		ID:   "invalid-source",
		Name: "Invalid Source",
		Type: models.SourceTypeGit,
		// GitConfig is nil
	}

	err = handler.Initialize(invalidSource)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "git configuration is required")
}

func TestGitSourceHandler_ValidateConfig(t *testing.T) {
	handler := NewGitSourceHandler()

	// Test valid config
	validSource := models.UsageSource{
		Name:          "Test Git Source",
		Type:          models.SourceTypeGit,
		ScanFrequency: 300,
		GitConfig: &models.GitSourceConfig{
			RepoURL:  "https://github.com/test/repo.git",
			Branch:   "main",
			AuthType: "token",
			Token:    "test-token",
		},
	}

	err := handler.ValidateConfig(validSource)
	assert.NoError(t, err)

	// Test missing Git config
	invalidSource := models.UsageSource{
		Name:          "Invalid Source",
		Type:          models.SourceTypeGit,
		ScanFrequency: 300,
		// GitConfig is nil
	}

	err = handler.ValidateConfig(invalidSource)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "git configuration is required")

	// Test invalid Git config (missing repo URL)
	invalidGitSource := models.UsageSource{
		Name:          "Invalid Git Source",
		Type:          models.SourceTypeGit,
		ScanFrequency: 300,
		GitConfig: &models.GitSourceConfig{
			// RepoURL is missing
			Branch:   "main",
			AuthType: "token",
		},
	}

	err = handler.ValidateConfig(invalidGitSource)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "repository URL is required")
}

func TestApiSourceHandler_Initialize(t *testing.T) {
	handler := NewApiSourceHandler()

	// Test with valid API source
	source := models.UsageSource{
		ID:   "test-api-source",
		Name: "Test API Source",
		Type: models.SourceTypeAPI,
		ApiConfig: &models.ApiSourceConfig{
			Endpoint: "https://api.example.com/data",
			Method:   "GET",
			AuthType: "bearer",
			Token:    "test-token",
		},
	}

	err := handler.Initialize(source)
	assert.NoError(t, err)
	assert.Equal(t, models.SourceTypeAPI, handler.GetSourceType())

	// Test with missing API config
	invalidSource := models.UsageSource{
		ID:   "invalid-source",
		Name: "Invalid Source",
		Type: models.SourceTypeAPI,
		// ApiConfig is nil
	}

	err = handler.Initialize(invalidSource)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "api configuration is required")
}

func TestFileSourceHandler_Initialize(t *testing.T) {
	handler := NewFileSourceHandler()

	// Test with valid file source
	source := models.UsageSource{
		ID:   "test-file-source",
		Name: "Test File Source",
		Type: models.SourceTypeFile,
		FileConfig: &models.FileSourceConfig{
			BasePath:  "/path/to/files",
			Recursive: true,
			FileTypes: []string{".yaml", ".json"},
		},
	}

	err := handler.Initialize(source)
	assert.NoError(t, err)
	assert.Equal(t, models.SourceTypeFile, handler.GetSourceType())

	// Test with missing file config
	invalidSource := models.UsageSource{
		ID:   "invalid-source",
		Name: "Invalid Source",
		Type: models.SourceTypeFile,
		// FileConfig is nil
	}

	err = handler.Initialize(invalidSource)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "file configuration is required")
}
