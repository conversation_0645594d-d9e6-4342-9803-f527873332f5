import React, { useState, useEffect } from 'react';
import { <PERSON>, User<PERSON><PERSON><PERSON>, Building2, <PERSON>ader2, User as UserIcon, GitBranch, ChevronDown, ChevronRight, Search } from 'lucide-react';
import { Group } from './GroupTypes';
import { apiClient } from '@/api/client';
import { useNavigate } from 'react-router-dom';
import GroupUsageTab from './GroupUsageTab';

interface TreeNode {
  name: string;
  type: 'parent' | 'child';
}

interface HorizontalTreeData {
  parentChain: TreeNode[];  // Ordered chain from root to current group
  children: TreeNode[];     // Direct children only
  currentGroup: string;
}

interface GroupMembershipInfoProps {
  group: Group;
  repoId: string;
  onGroupClick?: (groupName: string) => void;
}

const GroupMembershipInfo: React.FC<GroupMembershipInfoProps> = ({
  group,
  repoId,
  onGroupClick
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'direct' | 'resolved' | 'tree' | 'usage'>('direct');
  const [enhancedGroup, setEnhancedGroup] = useState<Group | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uniqueUsers, setUniqueUsers] = useState<any[]>([]);
  const [uniqueUsersLoading, setUniqueUsersLoading] = useState(false);
  const [uniqueUsersError, setUniqueUsersError] = useState<string | null>(null);
  const [expandedUsers, setExpandedUsers] = useState<Set<string>>(new Set());
  const [directUsers, setDirectUsers] = useState<any[]>([]);
  const [treeData, setTreeData] = useState<HorizontalTreeData | null>(null);
  const [treeDataBuilding, setTreeDataBuilding] = useState(false);

  // Create enhanced data from current group data
  useEffect(() => {
    const createEnhancedData = async () => {
      setLoading(true);
      setError(null);
      setTreeData(null); // Reset tree data when group changes
      setTreeDataBuilding(false);

      try {
        // Create enhanced data from the existing Members array
        const directUsers: string[] = [];
        const directGroups: string[] = [];

        // Process members to categorize them
        if (group.Members && Array.isArray(group.Members)) {
          group.Members.forEach(member => {
            const memberName = typeof member === 'string' ? member : member.name;
            const memberType = typeof member === 'string' ? 'user' : member.type;

            if (memberType === 'group') {
              directGroups.push(memberName);
            } else {
              directUsers.push(memberName);
            }
          });
        }

        // Recursively resolve users from nested groups
        const resolvedUsers: any[] = [];
        const processedGroups = new Set<string>();
        let resolveErrors: string[] = [];

        // Add direct users
        directUsers.forEach(user => {
          resolvedUsers.push({
            name: user,
            path: [group.Groupname],
            direct: true
          });
        });

        // Recursively resolve users from nested groups
        const resolveGroupMembers = async (groupName: string, currentPath: string[]): Promise<void> => {
          if (processedGroups.has(groupName)) {
            return; // Avoid infinite loops
          }
          processedGroups.add(groupName);

          try {
            // Fetch the nested group data
            const response = await apiClient.search.searchGroups(`groupname:${groupName}`, 1, 1, repoId);
            if (response.groups && response.groups.length > 0) {
              const nestedGroup = response.groups[0];
              const newPath = [...currentPath, groupName];

              if (nestedGroup.Members && Array.isArray(nestedGroup.Members)) {
                for (const member of nestedGroup.Members) {
                  const memberName = typeof member === 'string' ? member : member.name;
                  const memberType = typeof member === 'string' ? 'user' : member.type;

                  if (memberType === 'group') {
                    // Recursively resolve this nested group
                    await resolveGroupMembers(memberName, newPath);
                  } else {
                    // Add user with the full path
                    resolvedUsers.push({
                      name: memberName,
                      path: newPath,
                      direct: false
                    });
                  }
                }
              }
            }
          } catch (error) {
            console.warn(`Failed to resolve group ${groupName}:`, error);
            resolveErrors.push(`Failed to resolve group ${groupName}`);
          }
        };

        // Resolve all direct groups
        for (const groupName of directGroups) {
          await resolveGroupMembers(groupName, [group.Groupname]);
        }

        // Find parent groups by searching for groups that contain this group as a member
        const parentGroups: string[] = [];
        try {
          // Search for groups that contain this group as a member
          const searchResponse = await apiClient.search.searchGroups(`members:${group.Groupname}`, 1, 100, repoId);
          if (searchResponse.groups) {
            searchResponse.groups.forEach(parentGroup => {
              if (parentGroup.Groupname !== group.Groupname &&
                  parentGroup.Members &&
                  Array.isArray(parentGroup.Members)) {
                // Check if this group is actually a member
                const isMember = parentGroup.Members.some((member: any) => {
                  const memberName = typeof member === 'string' ? member : member.name;
                  return memberName === group.Groupname;
                });
                if (isMember) {
                  parentGroups.push(parentGroup.Groupname);
                }
              }
            });
          }
        } catch (error) {
          console.warn('Failed to find parent groups:', error);
        }

        // Create the enhanced group object
        const enhanced: Group = {
          ...group,
          DirectMembers: {
            users: directUsers,
            groups: directGroups,
            total: directUsers.length + directGroups.length
          },
          ResolvedMembers: {
            users: resolvedUsers,
            total: resolvedUsers.length
          },
          ParentGroups: parentGroups,
          // Store resolve errors for display
          resolveErrors: resolveErrors.length > 0 ? resolveErrors : undefined
        };

        setEnhancedGroup(enhanced);

        // Tree data will be built by the separate useEffect
      } catch (err) {
        console.error('Failed to create enhanced group data:', err);
        setError('Failed to process membership data');
      } finally {
        setLoading(false);
      }
    };

    createEnhancedData();
  }, [group, repoId]);

  // Separate effect to ensure tree data is built when enhanced group is available
  useEffect(() => {
    const buildTreeIfNeeded = async () => {
      if (enhancedGroup && !treeData && !treeDataBuilding && repoId) {
        console.log('Building tree data from separate effect for group:', enhancedGroup.Groupname);

        // Extract direct groups and parent groups from enhanced data
        const directGroups: string[] = [];
        const parentGroups: string[] = [];

        // Get direct groups from Members array
        if (enhancedGroup.Members && Array.isArray(enhancedGroup.Members)) {
          enhancedGroup.Members.forEach(member => {
            const memberName = typeof member === 'string' ? member : member.name;
            const memberType = typeof member === 'string' ? 'user' : member.type;

            if (memberType === 'group') {
              directGroups.push(memberName);
            }
          });
        }

        // Get parent groups
        if (enhancedGroup.ParentGroups && Array.isArray(enhancedGroup.ParentGroups)) {
          parentGroups.push(...enhancedGroup.ParentGroups);
        }

        await buildTreeData(enhancedGroup, directGroups, parentGroups);
      }
    };

    buildTreeIfNeeded();
  }, [enhancedGroup, treeData, treeDataBuilding, repoId]);

  // Fetch and process users for all tabs
  useEffect(() => {
    const fetchAndProcessUsers = async () => {
      if (!enhancedGroup) return;

      setUniqueUsersLoading(true);
      setUniqueUsersError(null);

      try {
        // Get all groups from the repository to find group memberships for each user
        const allGroupsResponse = await apiClient.data.getGroups(repoId, 1, 1000); // Get a large number to get all groups
        const allGroups = allGroupsResponse.items || [];

        // Helper function to process users and find their group memberships
        const processUsers = (users: any[], isDirectMember = false) => {
          const userMap = new Map();

          users.forEach(user => {
            const userName = typeof user === 'string' ? user : user.name;
            if (!userMap.has(userName)) {
              // Find all groups this user belongs to, categorizing them
              const directGroups: string[] = [];
              const otherGroups: string[] = [];

              allGroups.forEach(g => {
                if (g.Members && Array.isArray(g.Members)) {
                  const isMember = g.Members.some((member: any) =>
                    (typeof member === 'string' && member === userName) ||
                    (typeof member === 'object' && member.name === userName && member.type === 'user')
                  );
                  if (isMember) {
                    // Determine if this group is "direct" (should be purple)
                    let isDirectGroup = false;

                    if (isDirectMember) {
                      // For direct members tab: only the current group is direct
                      isDirectGroup = g.Groupname === group.Groupname;
                    } else {
                      // For all users tab: current group, direct member groups, and parent groups are direct
                      const isCurrentGroup = g.Groupname === group.Groupname;
                      const isDirectMemberGroup = group.Members && Array.isArray(group.Members) &&
                        group.Members.some(member => {
                          const memberName = typeof member === 'string' ? member : member.name;
                          const memberType = typeof member === 'string' ? 'group' : member.type;
                          return memberType === 'group' && memberName === g.Groupname;
                        });
                      const isParentGroup = enhancedGroup?.ParentGroups?.includes(g.Groupname);

                      isDirectGroup = !!(isCurrentGroup || isDirectMemberGroup || isParentGroup);
                    }

                    if (isDirectGroup) {
                      directGroups.push(g.Groupname);
                    } else {
                      otherGroups.push(g.Groupname);
                    }
                  }
                }
              });

              // Combine groups with direct groups first
              const allUserGroups = [...directGroups, ...otherGroups];

              userMap.set(userName, {
                name: userName,
                groups: allUserGroups.length > 0 ? allUserGroups : [group.Groupname],
                directGroups: directGroups,
                otherGroups: otherGroups
              });
            }
          });

          return Array.from(userMap.values());
        };

        // Process resolved members for "All Users" tab
        if (enhancedGroup?.ResolvedMembers?.users) {
          const uniqueUsersArray = processUsers(enhancedGroup.ResolvedMembers.users, false);
          setUniqueUsers(uniqueUsersArray);
        } else {
          setUniqueUsers([]);
        }

        // Process direct members for "Direct Members" tab
        // Show exactly what's in the group's Members array (both users and groups)
        const directMembers: any[] = [];

        if (group.Members && Array.isArray(group.Members)) {
          group.Members.forEach(member => {
            const memberName = typeof member === 'string' ? member : member.name;
            const memberType = typeof member === 'string' ? 'user' : member.type;

            directMembers.push({
              name: memberName,
              type: memberType
            });
          });
        }

        setDirectUsers(directMembers);


      } catch (err) {
        console.error('Failed to process users:', err);
        setUniqueUsersError('Failed to load users');
        setUniqueUsers([]);
        setDirectUsers([]);
      } finally {
        setUniqueUsersLoading(false);
      }
    };

    fetchAndProcessUsers();
  }, [repoId, group.Groupname, enhancedGroup, activeTab]);

  // Build horizontal tree data for the tree tab
  const buildTreeData = async (enhanced: Group, directGroups: string[], parentGroups: string[]) => {
    // Prevent duplicate calls
    if (treeDataBuilding) {
      console.log('Tree building already in progress, skipping...');
      return;
    }

    setTreeDataBuilding(true);

    try {
      console.log('Building tree data for group:', enhanced.Groupname, 'directGroups:', directGroups, 'parentGroups:', parentGroups);

      // Get all groups to build the tree structure
      const allGroupsResponse = await apiClient.data.getGroups(repoId, 1, 1000);
      const allGroups = allGroupsResponse.items || [];

      console.log('Retrieved', allGroups.length, 'groups for tree building');

      // Create a map for quick group lookup
      const groupMap = new Map();
      allGroups.forEach(g => {
        groupMap.set(g.Groupname, g);
      });

      // Build parent chain - trace back from current group to root
      const parentChain: TreeNode[] = [];

      // Helper function to build chain recursively
      const buildParentChain = (groupName: string, visited: Set<string> = new Set()): TreeNode[] => {
        if (visited.has(groupName)) {
          return []; // Prevent infinite loops
        }
        visited.add(groupName);

        const chain: TreeNode[] = [];
        const group = groupMap.get(groupName);

        if (group) {
          // Find parents of this group
          for (const potentialParent of allGroups) {
            if (potentialParent.Groupname !== groupName &&
                potentialParent.Members &&
                Array.isArray(potentialParent.Members)) {

              const containsGroup = potentialParent.Members.some((member: any) => {
                const memberName = typeof member === 'string' ? member : member.name;
                return memberName === groupName;
              });

              if (containsGroup) {
                // Recursively build chain for this parent
                const parentChain = buildParentChain(potentialParent.Groupname, visited);
                chain.push(...parentChain);

                // Add this parent to the chain
                chain.push({
                  name: potentialParent.Groupname,
                  type: 'parent'
                });
                break; // Only follow one parent path to avoid complexity
              }
            }
          }
        }

        return chain;
      };

      // Build the parent chain for the current group
      parentChain.push(...buildParentChain(enhanced.Groupname));

      // Build direct children only
      const children: TreeNode[] = [];
      for (const childName of directGroups) {
        children.push({
          name: childName,
          type: 'child'
        });
      }

      const treeResult = {
        parentChain,
        children,
        currentGroup: enhanced.Groupname
      };

      console.log('Tree data built successfully:', treeResult);
      setTreeData(treeResult);
    } catch (err) {
      console.error('Failed to build tree data:', err);

      // Check if it's a cancellation error
      if (err instanceof Error && err.message.includes('cancelled')) {
        console.log('Tree building was cancelled, this is normal during component updates');
        return; // Don't set empty data on cancellation
      }

      // Set empty tree data on other errors so the UI shows something
      setTreeData({
        parentChain: [],
        children: [],
        currentGroup: enhanced.Groupname
      });
    } finally {
      setTreeDataBuilding(false);
    }
  };



  // Handle group click navigation
  const handleGroupClick = (groupName: string) => {
    const params = new URLSearchParams();
    const searchValue = `groupname:"${groupName}"`;
    params.set('search', searchValue);
    params.set('page', '1');
    navigate(`/groups?${params.toString()}`);
  };

  // Handle user click navigation
  const handleUserClick = (userName: string) => {
    const params = new URLSearchParams();
    const searchValue = `name:"${userName}"`;
    params.set('search', searchValue);
    params.set('page', '1');
    navigate(`/users?${params.toString()}`);
  };

  // Toggle user expansion for showing more groups
  const toggleUserExpansion = (userName: string) => {
    const newExpanded = new Set(expandedUsers);
    if (newExpanded.has(userName)) {
      newExpanded.delete(userName);
    } else {
      newExpanded.add(userName);
    }
    setExpandedUsers(newExpanded);
  };



  // Function to render direct members and parent groups as pills
  const renderMemberPills = (members: any[], title: string, icon: React.ReactNode) => {
    if (members.length === 0) {
      return (
        <div className="text-gray-500 text-sm">
          No {title.toLowerCase()} found.
        </div>
      );
    }

    const userCount = members.filter(m => m.type === 'user').length;
    const groupCount = members.filter(m => m.type === 'group').length;

    return (
      <div className="space-y-3">
        {/* Statistics */}
        <div className="flex items-center gap-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-center gap-1">
            {icon}
            <span className="font-medium">{title}:</span>
          </div>
          <div className="flex items-center gap-4">
            {userCount > 0 && (
              <div className="flex items-center gap-1">
                <UserIcon className="w-3 h-3" />
                <span>{userCount} user{userCount !== 1 ? 's' : ''}</span>
              </div>
            )}
            {groupCount > 0 && (
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                <span>{groupCount} group{groupCount !== 1 ? 's' : ''}</span>
              </div>
            )}
            <div className="text-gray-500">
              Total: {members.length}
            </div>
          </div>
        </div>

        {/* Member Pills */}
        <div className="flex flex-wrap gap-1">
          {members.map((member, index) => {
            const memberKey = `member-${index}-${member.name}`;
            const isGroup = member.type === 'group';
            const IconComponent = isGroup ? Users : UserIcon;
            const pillClass = isGroup
              ? "bg-purple-100 text-purple-800"
              : "bg-blue-100 text-blue-800";

            return (
              <div
                key={memberKey}
                className={`${pillClass} px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer`}
                onClick={() => isGroup ? handleGroupClick(member.name) : handleUserClick(member.name)}
              >
                <IconComponent className="h-3 w-3 inline-block mr-1" />
                {member.name}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Reusable function to render users with their group memberships
  const renderUsersWithGroups = (users: any[], title: string, icon: React.ReactNode) => {
    if (users.length === 0) {
      return (
        <div className="text-gray-500 text-sm">
          No users found.
        </div>
      );
    }

    // Calculate statistics
    const totalUsers = users.length;
    const totalGroups = new Set(users.flatMap(user => user.groups || [])).size;
    const directGroups = new Set(users.flatMap(user => user.directGroups || [])).size;
    const otherGroups = new Set(users.flatMap(user => user.otherGroups || [])).size;

    return (
      <div className="space-y-4">
        {/* Statistics */}
        <div className="flex items-center gap-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-center gap-1">
            {icon}
            <span className="font-medium">{title}:</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <UserIcon className="w-3 h-3" />
              <span>{totalUsers} user{totalUsers !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3 text-purple-800" />
              <span>{directGroups} direct group{directGroups !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3 text-gray-600" />
              <span>{otherGroups} other group{otherGroups !== 1 ? 's' : ''}</span>
            </div>
            <div className="text-gray-500">
              Total: {totalGroups} groups
            </div>
          </div>
        </div>

        <div className="space-y-3">
          {users.map((user, index) => {
            const userKey = `user-${index}-${user.name}`;
            const isExpanded = expandedUsers.has(user.name);
            const visibleGroups = isExpanded ? user.groups : user.groups.slice(0, 5);
            const hasMoreGroups = user.groups.length > 5;

            return (
              <div key={userKey} className="border rounded-lg p-3 bg-white">
                <div className="flex items-center gap-2 mb-2">
                  <UserIcon className="w-4 h-4 text-blue-600" />
                  <span
                    className="font-medium text-sm cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
                    onClick={() => handleUserClick(user.name)}
                  >
                    {user.name}
                  </span>
                  <span className="text-xs text-gray-500">({user.groups.length} groups)</span>
                </div>

                <div className="flex flex-wrap gap-1 items-center">
                  {visibleGroups.map((groupName: string, groupIndex: number) => {
                    // Check if this is a direct group or other group
                    const isDirect = user.directGroups?.includes(groupName);
                    const pillClass = isDirect
                      ? "bg-purple-100 text-purple-800"
                      : "bg-gray-100 text-gray-600";

                    return (
                      <div
                        key={`${userKey}-${groupName}-${groupIndex}`}
                        className={`${pillClass} px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer`}
                        onClick={() => handleGroupClick(groupName)}
                      >
                        <Users className="h-3 w-3 inline-block mr-1" />
                        {groupName}
                      </div>
                    );
                  })}

                  {hasMoreGroups && (
                    <div
                      className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs cursor-pointer inline-block h-5 leading-4 ml-1"
                      onClick={() => toggleUserExpansion(user.name)}
                    >
                      {isExpanded ? (
                        <span>Show less</span>
                      ) : (
                        <span>+{user.groups.length - 5} more</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderDirectMembersTab = () => {
    if (loading || uniqueUsersLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error || uniqueUsersError) {
      return <div className="text-red-500 text-sm">{error || uniqueUsersError}</div>;
    }

    return renderMemberPills(directUsers, "Direct Members", <UserCheck className="w-4 h-4" />);
  };

  const renderResolvedMembers = () => {
    if (uniqueUsersLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading unique users...</span>
        </div>
      );
    }

    if (uniqueUsersError) {
      return <div className="text-red-500 text-sm">{uniqueUsersError}</div>;
    }

    return renderUsersWithGroups(uniqueUsers, "Total", <UserIcon className="w-4 h-4" />);
  };



  // Render a group pill for the tree
  const renderGroupPill = (groupName: string, type: 'parent' | 'child' | 'current') => {
    let baseClass = "";

    if (type === 'parent') {
      baseClass = "bg-green-100 text-green-800";
    } else if (type === 'child') {
      baseClass = "bg-purple-100 text-purple-800";
    } else {
      baseClass = "bg-blue-100 text-blue-800";
    }

    return (
      <div
        key={groupName}
        className={`${baseClass} px-2 py-0.5 rounded text-xs inline-block h-5 leading-4 cursor-pointer hover:shadow-sm transition-shadow`}
        onClick={() => type !== 'current' ? handleGroupClick(groupName) : undefined}
      >
        <Users className="h-3 w-3 inline-block mr-1" />
        {groupName}
        {type === 'current' && (
          <span className="ml-1 text-xs bg-blue-200 text-blue-700 px-1 py-0.5 rounded">
            Current
          </span>
        )}
      </div>
    );
  };

  // Render connecting lines between groups
  const renderConnectingLine = (fromLevel: number, toLevel: number, isVertical: boolean = false) => {
    if (isVertical) {
      return (
        <div className="flex justify-center">
          <div className="w-0.5 h-4 bg-gray-300"></div>
        </div>
      );
    } else {
      return (
        <div className="flex items-center">
          <div className="h-0.5 w-8 bg-gray-300"></div>
        </div>
      );
    }
  };

  const renderGroupTreeTab = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading group tree...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    if (!treeData) {
      return (
        <div className="flex flex-col items-center justify-center py-8 text-gray-500">
          <div className="text-sm mb-2">Building group tree...</div>
          <div className="text-xs">If this persists, try refreshing the page or re-running your search.</div>
        </div>
      );
    }

    const { parentChain, children, currentGroup } = treeData;

    // Show a message if there are no relationships
    if (parentChain.length === 0 && children.length === 0) {
      return (
        <div className="space-y-4">
          {/* Tree Header */}
          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
            <GitBranch className="w-4 h-4" />
            <span className="font-medium">Group Hierarchy Tree</span>
            <span className="text-xs text-gray-500">
              (Click on groups to navigate)
            </span>
          </div>

          {/* No relationships message */}
          <div className="flex flex-col items-center justify-center py-8 text-gray-500">
            <div className="text-sm mb-2">No parent or child groups found</div>
            <div className="text-xs text-center">
              This group "{currentGroup}" has no hierarchical relationships with other groups.
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Tree Header */}
        <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
          <GitBranch className="w-4 h-4" />
          <span className="font-medium">Group Hierarchy Tree</span>
          <span className="text-xs text-gray-500">
            (Click on groups to navigate)
          </span>
        </div>

        {/* Horizontal Tree Structure */}
        <div className="overflow-x-auto">
          <div className="flex items-center justify-center min-w-max p-4">

            {/* Parent chain - shows the path from root to current group */}
            {parentChain.map((parent, index) => (
              <React.Fragment key={parent.name}>
                <div className="mx-2">
                  {renderGroupPill(parent.name, 'parent')}
                </div>
                {/* Connecting line after each parent */}
                <div className="flex items-center">
                  <div className="h-0.5 w-8 bg-gray-300"></div>
                </div>
              </React.Fragment>
            ))}

            {/* Center - Current group */}
            <div className="mx-2">
              {renderGroupPill(currentGroup, 'current')}
            </div>

            {/* Connecting line to children */}
            {children.length > 0 && (
              <div className="flex items-center">
                <div className="h-0.5 w-8 bg-gray-300"></div>
              </div>
            )}

            {/* Right side - Child groups */}
            {children.length > 0 && (
              <div className="flex flex-col items-start space-y-1 ml-2">
                {children.map(child =>
                  renderGroupPill(child.name, 'child')
                )}
              </div>
            )}
          </div>
        </div>

        {/* Legend */}
        <div className="bg-gray-50 p-3 rounded text-xs text-gray-600">
          <div className="font-medium mb-3 text-center">Group Hierarchy Tree</div>

          {/* Color meanings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
            <div className="flex flex-col items-center gap-1">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-green-100 rounded"></div>
                <span className="font-medium">Parent Groups</span>
              </div>
              <span className="text-center text-gray-500">Groups that contain this group as a member</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-blue-100 rounded"></div>
                <span className="font-medium">Current Group</span>
              </div>
              <span className="text-center text-gray-500">The group you are currently viewing</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-purple-100 rounded"></div>
                <span className="font-medium">Child Groups</span>
              </div>
              <span className="text-center text-gray-500">Groups that are members of this group</span>
            </div>
          </div>

          {/* How to read the tree */}
          <div className="border-t pt-2 text-center text-gray-500">
            <div className="mb-1">
              <span className="font-medium">How to read:</span> Parent groups are connected in a chain showing the membership path
            </div>
            <div className="text-xs">
              <span className="font-medium">Example:</span> Grandparent → Parent → Current Group → Child Groups
            </div>
            <div className="mt-1">
              <span className="font-medium">Tip:</span> Click any group to navigate to it
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="mt-4 border-t pt-4">
      <div className="mb-4">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'direct'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('direct')}
          >
            Direct Members
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'resolved'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('resolved')}
          >
            All Users
          </button>

          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'tree'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('tree')}
          >
            Group Tree
          </button>

          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'usage'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('usage')}
          >
            <Search className="h-4 w-4 inline-block mr-1" />
            Usage
          </button>
        </div>
      </div>

      <div className="min-h-[200px]">
        {activeTab === 'direct' && renderDirectMembersTab()}
        {activeTab === 'resolved' && renderResolvedMembers()}
        {activeTab === 'tree' && renderGroupTreeTab()}
        {activeTab === 'usage' && <GroupUsageTab group={group} repoId={repoId} />}
      </div>
    </div>
  );
};

export default GroupMembershipInfo;
