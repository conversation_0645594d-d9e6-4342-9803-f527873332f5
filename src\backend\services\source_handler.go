package services

import (
	"context"
	"time"

	"adgitops-ui/src/backend/models"
)

// SourceHandler defines the interface for different source type handlers
type SourceHandler interface {
	// Initialize the handler with source configuration
	Initialize(source models.UsageSource) error

	// Scan for group usage in this source
	ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error)

	// Get source type that this handler supports
	GetSourceType() models.SourceType

	// Validate source configuration
	ValidateConfig(source models.UsageSource) error

	// Test connection to the source
	TestConnection(ctx context.Context) error

	// Get source status (available, error, etc.)
	GetStatus(ctx context.Context) (models.SourceStatus, error)

	// Cleanup resources
	Cleanup() error
}

// SourceHandlerRegistry manages source handlers
type SourceHandlerRegistry struct {
	handlers map[models.SourceType]SourceHandlerFactory
}

// SourceHandlerFactory creates a new source handler
type SourceHandlerFactory func() SourceHandler

// NewSourceHandlerRegistry creates a new source handler registry
func NewSourceHandlerRegistry() *SourceHandlerRegistry {
	registry := &SourceHandlerRegistry{
		handlers: make(map[models.SourceType]SourceHandlerFactory),
	}

	// Register built-in handlers
	registry.RegisterHandler(models.SourceTypeGit, func() SourceHandler {
		return NewGitSourceHandler()
	})

	registry.RegisterHandler(models.SourceTypeAPI, func() SourceHandler {
		return NewApiSourceHandler()
	})

	registry.RegisterHandler(models.SourceTypeFile, func() SourceHandler {
		return NewFileSourceHandler()
	})

	return registry
}

// RegisterHandler registers a source handler factory
func (r *SourceHandlerRegistry) RegisterHandler(sourceType models.SourceType, factory SourceHandlerFactory) {
	r.handlers[sourceType] = factory
}

// GetHandler gets a source handler for a source type
func (r *SourceHandlerRegistry) GetHandler(sourceType models.SourceType) (SourceHandler, error) {
	factory, exists := r.handlers[sourceType]
	if !exists {
		return nil, models.NewValidationError("sourceType", "unsupported source type: "+string(sourceType))
	}

	return factory(), nil
}

// GetSupportedTypes gets all supported source types
func (r *SourceHandlerRegistry) GetSupportedTypes() []models.SourceType {
	types := make([]models.SourceType, 0, len(r.handlers))
	for sourceType := range r.handlers {
		types = append(types, sourceType)
	}
	return types
}

// BaseSourceHandler provides common functionality for source handlers
type BaseSourceHandler struct {
	source     models.UsageSource
	lastStatus models.SourceStatus
}

// Initialize initializes the base handler
func (b *BaseSourceHandler) Initialize(source models.UsageSource) error {
	b.source = source
	b.lastStatus = models.SourceStatus{
		Available:   false,
		LastChecked: time.Now(),
		Error:       "",
		Metadata:    make(map[string]interface{}),
	}
	return nil
}

// ValidateConfig validates the source configuration using the model's validation
func (b *BaseSourceHandler) ValidateConfig(source models.UsageSource) error {
	return source.Validate()
}

// GetStatus returns the last known status
func (b *BaseSourceHandler) GetStatus(ctx context.Context) (models.SourceStatus, error) {
	return b.lastStatus, nil
}

// updateStatus updates the internal status
func (b *BaseSourceHandler) updateStatus(available bool, err error) {
	b.lastStatus.Available = available
	b.lastStatus.LastChecked = time.Now()
	if err != nil {
		b.lastStatus.Error = err.Error()
	} else {
		b.lastStatus.Error = ""
	}
}

// Cleanup provides default cleanup (no-op)
func (b *BaseSourceHandler) Cleanup() error {
	return nil
}

// Helper functions for pattern matching

// matchesPatterns checks if a file path matches include/exclude patterns
func matchesPatterns(filePath string, includePatterns, excludePatterns []string) bool {
	// If no include patterns, include all files
	included := len(includePatterns) == 0

	// Check include patterns
	for _, pattern := range includePatterns {
		if matchesPattern(filePath, pattern) {
			included = true
			break
		}
	}

	if !included {
		return false
	}

	// Check exclude patterns
	for _, pattern := range excludePatterns {
		if matchesPattern(filePath, pattern) {
			return false
		}
	}

	return true
}

// matchesPattern checks if a file path matches a pattern (simple wildcard matching)
func matchesPattern(filePath, pattern string) bool {
	// Simple pattern matching - can be enhanced with proper glob matching
	if pattern == "*" {
		return true
	}

	// Check file extension patterns like "*.yaml"
	if len(pattern) > 2 && pattern[0] == '*' && pattern[1] == '.' {
		ext := pattern[1:]
		return len(filePath) >= len(ext) && filePath[len(filePath)-len(ext):] == ext
	}

	// Check prefix patterns like "config/*"
	if len(pattern) > 2 && pattern[len(pattern)-2:] == "/*" {
		prefix := pattern[:len(pattern)-2]
		return len(filePath) >= len(prefix) && filePath[:len(prefix)] == prefix
	}

	// Check patterns like "config.*" (prefix with wildcard extension)
	if len(pattern) > 2 && pattern[len(pattern)-2:] == ".*" {
		prefix := pattern[:len(pattern)-2]
		return len(filePath) >= len(prefix) && filePath[:len(prefix)] == prefix
	}

	// Exact match
	return filePath == pattern
}

// findGroupUsageInContent searches for group usage in text content
func findGroupUsageInContent(content, groupName, filePath string) []models.UsageResult {
	var results []models.UsageResult
	lines := splitLines(content)

	for lineNum, line := range lines {
		if containsGroupReference(line, groupName) {
			result := models.UsageResult{
				FilePath:   filePath,
				LineNumber: lineNum + 1,                             // 1-based line numbers
				Context:    getContextAroundLine(lines, lineNum, 2), // 2 lines of context
				MatchType:  "exact",                                 // Can be enhanced to detect partial matches
				DetectedAt: time.Now(),
			}
			results = append(results, result)
		}
	}

	return results
}

// containsGroupReference checks if a line contains a reference to the group
func containsGroupReference(line, groupName string) bool {
	// Simple string matching - can be enhanced with regex patterns
	// Look for common patterns like:
	// - "groupName"
	// - 'groupName'
	// - groupName (without quotes)
	// - groups: [groupName]
	// - member: groupName

	// Check for quoted references
	if containsQuotedString(line, groupName) {
		return true
	}

	// Check for unquoted references (word boundaries)
	return containsWord(line, groupName)
}

// containsQuotedString checks if line contains the string in quotes
func containsQuotedString(line, str string) bool {
	quotedStr1 := `"` + str + `"`
	quotedStr2 := `'` + str + `'`
	return contains(line, quotedStr1) || contains(line, quotedStr2)
}

// containsWord checks if line contains the word with word boundaries
func containsWord(line, word string) bool {
	// Simple word boundary check - can be enhanced with regex
	return contains(line, word)
}

// contains is a case-insensitive string contains check
func contains(s, substr string) bool {
	// Simple case-sensitive check for now
	// Can be enhanced to be case-insensitive if needed
	return len(s) >= len(substr) && findSubstring(s, substr) >= 0
}

// findSubstring finds the index of substr in s
func findSubstring(s, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(substr) > len(s) {
		return -1
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// splitLines splits content into lines
func splitLines(content string) []string {
	if content == "" {
		return []string{}
	}

	var lines []string
	var currentLine string

	for _, char := range content {
		if char == '\n' {
			lines = append(lines, currentLine)
			currentLine = ""
		} else if char != '\r' { // Skip carriage returns
			currentLine += string(char)
		}
	}

	// Always add the last line (even if empty for trailing newline)
	lines = append(lines, currentLine)

	return lines
}

// getContextAroundLine gets context lines around a specific line
func getContextAroundLine(lines []string, lineIndex, contextSize int) string {
	start := lineIndex - contextSize
	if start < 0 {
		start = 0
	}

	end := lineIndex + contextSize + 1
	if end > len(lines) {
		end = len(lines)
	}

	var context string
	for i := start; i < end; i++ {
		if i > start {
			context += "\n"
		}
		context += lines[i]
	}

	return context
}
