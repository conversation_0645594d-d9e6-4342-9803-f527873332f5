import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { EnhancedProgress, CompactProgress } from '@/components/ui/enhanced-progress';
import {
  Wifi,
  WifiOff,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Activity,
  Clock,
  Pause,
  Play,
  X,
  Eye,
  EyeOff
} from 'lucide-react';
import { Progress, ChunkProcessingStatus } from '@/types/scanLogs';

export type TaskStatus = 'idle' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected';

export interface UnifiedTaskData {
  id: string;
  name: string;
  type: string;
  status: TaskStatus;
  startTime?: string;
  endTime?: string;
  progress?: Progress;
  currentChunk?: ChunkProcessingStatus;
  error?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface UnifiedActiveTaskProgressProps {
  task: UnifiedTaskData;
  connectionStatus?: ConnectionStatus;
  connectionError?: string;
  compact?: boolean;
  showDetails?: boolean;
  showControls?: boolean;
  realTimeEnabled?: boolean;
  onPause?: (taskId: string) => void;
  onResume?: (taskId: string) => void;
  onCancel?: (taskId: string) => void;
  onRefresh?: (taskId: string) => void;
  onToggleRealTime?: (enabled: boolean) => void;
  className?: string;
}

export function UnifiedActiveTaskProgress({
  task,
  connectionStatus = 'disconnected',
  connectionError,
  compact = false,
  showDetails = true,
  showControls = true,
  realTimeEnabled = false,
  onPause,
  onResume,
  onCancel,
  onRefresh,
  onToggleRealTime,
  className = ""
}: UnifiedActiveTaskProgressProps) {

  // Status badge component
  const StatusBadge = () => {
    const getStatusConfig = (status: TaskStatus) => {
      switch (status) {
        case 'running':
          return {
            icon: <Loader2 className="h-3 w-3 animate-spin" />,
            className: 'bg-blue-100 text-blue-800 border-blue-200',
            text: 'Running'
          };
        case 'paused':
          return {
            icon: <Pause className="h-3 w-3" />,
            className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
            text: 'Paused'
          };
        case 'completed':
          return {
            icon: <CheckCircle className="h-3 w-3" />,
            className: 'bg-green-100 text-green-800 border-green-200',
            text: 'Completed'
          };
        case 'failed':
          return {
            icon: <XCircle className="h-3 w-3" />,
            className: 'bg-red-100 text-red-800 border-red-200',
            text: 'Failed'
          };
        case 'cancelled':
          return {
            icon: <X className="h-3 w-3" />,
            className: 'bg-gray-100 text-gray-800 border-gray-200',
            text: 'Cancelled'
          };
        default:
          return {
            icon: <Activity className="h-3 w-3" />,
            className: 'bg-gray-100 text-gray-800 border-gray-200',
            text: 'Idle'
          };
      }
    };

    const config = getStatusConfig(task.status);
    return (
      <Badge variant="outline" className={`gap-1 ${config.className}`}>
        {config.icon}
        {config.text}
      </Badge>
    );
  };

  // Connection status indicator
  const ConnectionStatusIndicator = () => (
    <div className="flex items-center gap-2">
      {connectionStatus === 'connecting' ? (
        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      ) : connectionStatus === 'connected' ? (
        <Wifi className="h-4 w-4 text-green-500" />
      ) : (
        <WifiOff className="h-4 w-4 text-red-500" />
      )}

      <span className="text-sm text-muted-foreground">
        {connectionStatus === 'connecting' ? 'Connecting...' :
         connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
      </span>

      {connectionStatus === 'disconnected' && onRefresh && (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onRefresh(task.id)}
          className="h-6 px-2"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  );

  // Task controls
  const TaskControls = () => (
    <div className="flex items-center gap-2">
      {onToggleRealTime && (
        <Button
          variant={realTimeEnabled ? "default" : "outline"}
          size="sm"
          onClick={() => onToggleRealTime(!realTimeEnabled)}
          className="gap-1"
        >
          {realTimeEnabled ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
          {realTimeEnabled ? 'Live' : 'Static'}
        </Button>
      )}

      {onPause && task.status === 'running' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPause(task.id)}
          title="Pause Task"
        >
          <Pause className="h-4 w-4" />
        </Button>
      )}

      {onResume && task.status === 'paused' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onResume(task.id)}
          title="Resume Task"
        >
          <Play className="h-4 w-4" />
        </Button>
      )}

      {onCancel && (task.status === 'running' || task.status === 'paused') && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onCancel(task.id)}
          title="Cancel Task"
        >
          <X className="h-4 w-4" />
        </Button>
      )}

      {onRefresh && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRefresh(task.id)}
          title="Refresh Status"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      )}
    </div>
  );

  // Format time duration
  const formatDuration = (startTime?: string, endTime?: string): string => {
    if (!startTime) return '';

    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = end.getTime() - start.getTime();

    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Compact version
  if (compact) {
    return (
      <Card className={`w-full ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <span>{task.name}</span>
              <Badge variant="outline" className="text-xs">
                {task.type}
              </Badge>
            </CardTitle>
            <div className="flex items-center gap-2">
              <StatusBadge />
              {realTimeEnabled && <ConnectionStatusIndicator />}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {task.progress ? (
            <CompactProgress progress={task.progress} showCurrentFile={true} />
          ) : (
            <div className="text-center text-muted-foreground py-4">
              {task.status === 'idle' ? 'Waiting to start...' : 'Loading progress...'}
            </div>
          )}

          {task.description && (
            <p className="text-xs text-muted-foreground mt-2 truncate" title={task.description}>
              {task.description}
            </p>
          )}

          {task.startTime && (
            <div className="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>Running for {formatDuration(task.startTime)}</span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Full version
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header Card */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-3">
              <span>{task.name}</span>
              <Badge variant="outline">{task.type}</Badge>
            </CardTitle>
            <div className="flex items-center gap-3">
              <StatusBadge />
              {realTimeEnabled && <ConnectionStatusIndicator />}
              {showControls && <TaskControls />}
            </div>
          </div>

          {task.description && (
            <div className="text-sm text-muted-foreground">
              {task.description}
            </div>
          )}

          {task.startTime && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Started {formatDuration(task.startTime)} ago</span>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Error Alert */}
      {(connectionError || task.error) && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {task.error || connectionError}
          </AlertDescription>
        </Alert>
      )}

      {/* Progress Display */}
      {task.progress ? (
        <EnhancedProgress
          progress={task.progress}
          currentChunk={task.currentChunk}
          title={`${task.name} Progress`}
          showDetails={showDetails}
        />
      ) : (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              {task.status === 'idle' ? (
                <div className="space-y-2">
                  <Activity className="h-8 w-8 mx-auto text-gray-400" />
                  <p>Waiting for task to start...</p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Loader2 className="h-8 w-8 mx-auto animate-spin text-blue-500" />
                  <p>Initializing task progress...</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
