package services

import (
	"context"
	"testing"
	"time"
)

func TestTaskCancellationManager_RegisterTask(t *testing.T) {
	manager := NewTaskCancellationManager()

	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Test successful registration
	err := manager.RegisterTask("test-task-1", "usage_scan", "Test Task", "test-group", "test-repo", cancel)
	if err != nil {
		t.<PERSON>("Expected no error, got %v", err)
	}

	// Test duplicate registration
	err = manager.RegisterTask("test-task-1", "usage_scan", "Test Task", "test-group", "test-repo", cancel)
	if err == nil {
		t.Error("Expected error for duplicate task registration")
	}

	// Test empty task ID
	err = manager.RegisterTask("", "usage_scan", "Test Task", "test-group", "test-repo", cancel)
	if err == nil {
		t.Error("Expected error for empty task ID")
	}

	// Test nil cancel function
	err = manager.RegisterTask("test-task-2", "usage_scan", "Test Task", "test-group", "test-repo", nil)
	if err == nil {
		t.Error("Expected error for nil cancel function")
	}
}

func TestTaskCancellationManager_CancelTask(t *testing.T) {
	manager := NewTaskCancellationManager()

	ctx, cancel := context.WithCancel(context.Background())

	// Register a task
	taskID := "test-task-1"
	err := manager.RegisterTask(taskID, "usage_scan", "Test Task", "test-group", "test-repo", cancel)
	if err != nil {
		t.Fatalf("Failed to register task: %v", err)
	}

	// Test successful cancellation
	err = manager.CancelTask(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Verify context was cancelled
	select {
	case <-ctx.Done():
		// Context was cancelled as expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Context was not cancelled")
	}

	// Test cancelling already cancelled task
	err = manager.CancelTask(taskID)
	if err == nil {
		t.Error("Expected error for already cancelled task")
	}

	// Test cancelling non-existent task
	err = manager.CancelTask("non-existent-task")
	if err == nil {
		t.Error("Expected error for non-existent task")
	}

	// Test empty task ID
	err = manager.CancelTask("")
	if err == nil {
		t.Error("Expected error for empty task ID")
	}
}

func TestTaskCancellationManager_GetTaskInfo(t *testing.T) {
	manager := NewTaskCancellationManager()

	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Register a task
	taskID := "test-task-1"
	taskType := "usage_scan"
	taskName := "Test Task"
	groupName := "test-group"
	repoID := "test-repo"

	err := manager.RegisterTask(taskID, taskType, taskName, groupName, repoID, cancel)
	if err != nil {
		t.Fatalf("Failed to register task: %v", err)
	}

	// Test getting task info
	taskInfo, err := manager.GetTaskInfo(taskID)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if taskInfo.ID != taskID {
		t.Errorf("Expected task ID %s, got %s", taskID, taskInfo.ID)
	}
	if taskInfo.Type != taskType {
		t.Errorf("Expected task type %s, got %s", taskType, taskInfo.Type)
	}
	if taskInfo.Name != taskName {
		t.Errorf("Expected task name %s, got %s", taskName, taskInfo.Name)
	}
	if taskInfo.GroupName != groupName {
		t.Errorf("Expected group name %s, got %s", groupName, taskInfo.GroupName)
	}
	if taskInfo.RepoID != repoID {
		t.Errorf("Expected repo ID %s, got %s", repoID, taskInfo.RepoID)
	}
	if taskInfo.Status != "running" {
		t.Errorf("Expected status 'running', got %s", taskInfo.Status)
	}
	if taskInfo.CancelFunc != nil {
		t.Error("Cancel function should not be exposed in task info")
	}

	// Test getting non-existent task
	_, err = manager.GetTaskInfo("non-existent-task")
	if err == nil {
		t.Error("Expected error for non-existent task")
	}
}

func TestTaskCancellationManager_CleanupTask(t *testing.T) {
	manager := NewTaskCancellationManager()

	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Register a task
	taskID := "test-task-1"
	err := manager.RegisterTask(taskID, "usage_scan", "Test Task", "test-group", "test-repo", cancel)
	if err != nil {
		t.Fatalf("Failed to register task: %v", err)
	}

	// Verify task exists
	if !manager.IsTaskActive(taskID) {
		t.Error("Task should be active")
	}

	// Cleanup task
	manager.CleanupTask(taskID)

	// Verify task is cleaned up
	if manager.IsTaskActive(taskID) {
		t.Error("Task should not be active after cleanup")
	}

	// Test cleanup of non-existent task (should not panic)
	manager.CleanupTask("non-existent-task")

	// Test cleanup with empty task ID (should not panic)
	manager.CleanupTask("")
}

func TestTaskCancellationManager_GetActiveTasks(t *testing.T) {
	manager := NewTaskCancellationManager()

	// Initially no active tasks
	tasks := manager.GetActiveTasks()
	if len(tasks) != 0 {
		t.Errorf("Expected 0 active tasks, got %d", len(tasks))
	}

	// Register multiple tasks
	_, cancel1 := context.WithCancel(context.Background())
	_, cancel2 := context.WithCancel(context.Background())
	defer cancel1()
	defer cancel2()

	err := manager.RegisterTask("task-1", "usage_scan", "Task 1", "group-1", "repo-1", cancel1)
	if err != nil {
		t.Fatalf("Failed to register task 1: %v", err)
	}

	err = manager.RegisterTask("task-2", "usage_scan", "Task 2", "group-2", "repo-2", cancel2)
	if err != nil {
		t.Fatalf("Failed to register task 2: %v", err)
	}

	// Check active tasks
	tasks = manager.GetActiveTasks()
	if len(tasks) != 2 {
		t.Errorf("Expected 2 active tasks, got %d", len(tasks))
	}

	// Cancel one task
	err = manager.CancelTask("task-1")
	if err != nil {
		t.Errorf("Failed to cancel task: %v", err)
	}

	// Should still have 2 tasks (cancelled task is still tracked until cleanup)
	tasks = manager.GetActiveTasks()
	if len(tasks) != 2 {
		t.Errorf("Expected 2 active tasks, got %d", len(tasks))
	}

	// Cleanup cancelled task
	manager.CleanupTask("task-1")

	// Should now have 1 task
	tasks = manager.GetActiveTasks()
	if len(tasks) != 1 {
		t.Errorf("Expected 1 active task, got %d", len(tasks))
	}
}

func TestTaskCancellationManager_GetActiveTaskCount(t *testing.T) {
	manager := NewTaskCancellationManager()

	// Initially no active tasks
	count := manager.GetActiveTaskCount()
	if count != 0 {
		t.Errorf("Expected 0 active tasks, got %d", count)
	}

	// Register a task
	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	err := manager.RegisterTask("test-task", "usage_scan", "Test Task", "test-group", "test-repo", cancel)
	if err != nil {
		t.Fatalf("Failed to register task: %v", err)
	}

	// Should have 1 active task
	count = manager.GetActiveTaskCount()
	if count != 1 {
		t.Errorf("Expected 1 active task, got %d", count)
	}

	// Cleanup task
	manager.CleanupTask("test-task")

	// Should have 0 active tasks
	count = manager.GetActiveTaskCount()
	if count != 0 {
		t.Errorf("Expected 0 active tasks, got %d", count)
	}
}
