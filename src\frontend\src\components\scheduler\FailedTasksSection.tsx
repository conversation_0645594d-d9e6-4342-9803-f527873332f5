import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  XCircle,
  RotateCcw,
  AlertTriangle,
  Clock,
  Database,
  User,
  ChevronDown,
  ChevronRight,
  Trash2
} from 'lucide-react';

import type { FailedTask } from '@/types/scheduler';
import {
  TaskTypeColors,
  formatRelativeTime,
  getTaskTypeDisplayName
} from '@/types/scheduler';

interface FailedTasksSectionProps {
  tasks: FailedTask[];
  loading?: boolean;
  onRefresh?: () => void;
  onRetryTask?: (taskId: string) => void;
  onDeleteTask?: (taskId: string) => void;
}

const FailedTasksSection: React.FC<FailedTasksSectionProps> = ({
  tasks,
  loading = false,
  onRefresh,
  onRetryTask,
  onDeleteTask
}) => {
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [retryingTasks, setRetryingTasks] = useState<Set<string>>(new Set());

  const toggleExpanded = (taskId: string) => {
    const newExpanded = new Set(expandedTasks);
    if (newExpanded.has(taskId)) {
      newExpanded.delete(taskId);
    } else {
      newExpanded.add(taskId);
    }
    setExpandedTasks(newExpanded);
  };

  const handleRetry = async (taskId: string) => {
    if (!onRetryTask) return;

    setRetryingTasks(prev => new Set(prev).add(taskId));
    try {
      await onRetryTask(taskId);
    } finally {
      setRetryingTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Failed Tasks</CardTitle>
          <CardDescription>
            Tasks that have failed with error details and retry options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <RotateCcw className="h-8 w-8 animate-spin text-red-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-red-600" />
              <span>Failed Tasks</span>
              <Badge variant="destructive">{tasks?.length || 0} failed</Badge>
            </CardTitle>
            <CardDescription>
              Tasks that have failed with error details and retry options
            </CardDescription>
          </div>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RotateCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {(tasks?.length || 0) === 0 ? (
          <div className="text-center py-12">
            <XCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Failed Tasks</h3>
            <p className="text-gray-500">
              Great! All tasks are running successfully. Failed tasks will appear here if any issues occur.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {(tasks || []).map((task) => {
              const isExpanded = expandedTasks.has(task.id);
              const isRetrying = retryingTasks.has(task.id);

              return (
                <div
                  key={task.id}
                  className="border border-red-200 rounded-lg p-4 bg-red-50 hover:bg-red-100 transition-colors"
                >
                  {/* Task Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Badge className={`${TaskTypeColors[task.type]} border-0`}>
                        {getTaskTypeDisplayName(task.type)}
                      </Badge>
                      <Badge variant="destructive">
                        <XCircle className="h-3 w-3 mr-1" />
                        Failed
                      </Badge>
                      <h3 className="font-medium">{task.name}</h3>
                      {task.retryCount > 0 && (
                        <Badge variant="outline" className="text-orange-600 border-orange-300">
                          Retry #{task.retryCount}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-red-600">
                        Failed {formatRelativeTime(task.failedAt)}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(task.id)}
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Error Message */}
                  <Alert className="mb-3 border-red-300 bg-red-100">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      <strong>Error:</strong> {task.errorMessage}
                    </AlertDescription>
                  </Alert>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 mb-3">
                    {task.canRetry && onRetryTask && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRetry(task.id)}
                        disabled={isRetrying}
                        className="border-green-300 text-green-700 hover:bg-green-50"
                      >
                        <RotateCcw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
                        {isRetrying ? 'Retrying...' : 'Retry Task'}
                      </Button>
                    )}

                    {onDeleteTask && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDeleteTask(task.id)}
                        className="border-red-300 text-red-700 hover:bg-red-100"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    )}
                  </div>

                  {/* Expanded Details */}
                  {isExpanded && (
                    <div className="border-t border-red-200 pt-3 space-y-3">
                      {/* Task Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Database className="h-4 w-4 text-gray-600" />
                          <div>
                            <span className="font-medium text-gray-700">Repository:</span>
                            <div className="text-gray-600">{task.repository || 'N/A'}</div>
                          </div>
                        </div>

                        {task.groupName && (
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-gray-600" />
                            <div>
                              <span className="font-medium text-gray-700">Group:</span>
                              <div className="text-gray-600">{task.groupName}</div>
                            </div>
                          </div>
                        )}

                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-600" />
                          <div>
                            <span className="font-medium text-gray-700">Failed At:</span>
                            <div className="text-gray-600">{new Date(task.failedAt).toLocaleString()}</div>
                          </div>
                        </div>

                        {task.lastAttempt && (
                          <div className="flex items-center space-x-2">
                            <RotateCcw className="h-4 w-4 text-gray-600" />
                            <div>
                              <span className="font-medium text-gray-700">Last Attempt:</span>
                              <div className="text-gray-600">{new Date(task.lastAttempt).toLocaleString()}</div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Retry Information */}
                      <div className="bg-white rounded p-3 border border-red-200">
                        <h4 className="font-medium text-gray-800 mb-2">Retry Information</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Retry Count:</span>
                            <span className="ml-2 text-gray-600">{task.retryCount}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Can Retry:</span>
                            <span className={`ml-2 ${task.canRetry ? 'text-green-600' : 'text-red-600'}`}>
                              {task.canRetry ? 'Yes' : 'No'}
                            </span>
                          </div>
                        </div>
                        {!task.canRetry && (
                          <p className="text-xs text-red-600 mt-2">
                            This task cannot be retried automatically. Manual intervention may be required.
                          </p>
                        )}
                      </div>

                      {/* Metadata */}
                      {task.metadata && Object.keys(task.metadata).length > 0 && (
                        <div className="bg-white rounded p-3 border border-red-200">
                          <h4 className="font-medium text-gray-800 mb-2">Task Metadata</h4>
                          <div className="space-y-1">
                            {Object.entries(task.metadata).map(([key, value]) => (
                              <div key={key} className="text-xs text-gray-600">
                                <span className="font-medium">{key}:</span> {JSON.stringify(value)}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FailedTasksSection;
