package services

import (
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// ScanLogger handles logging for usage scan operations
type ScanLogger struct {
	logs                map[string][]models.ScanLogEntry  // scanID -> logs
	summaries           map[string]*models.ScanLogSummary // scanID -> summary
	mutex               sync.RWMutex
	maxLogs             int                  // Maximum number of log entries to keep per scan
	maxSummaries        int                  // Maximum number of scan summaries to keep
	progressBroadcaster *ProgressBroadcaster // For real-time progress updates
	autoScanService     *AutoScanService     // For updating scheduled job status
}

// NewScanLogger creates a new scan logger instance
func NewScanLogger() *ScanLogger {
	return &ScanLogger{
		logs:         make(map[string][]models.ScanLogEntry),
		summaries:    make(map[string]*models.ScanLogSummary),
		maxLogs:      1000, // Keep up to 1000 log entries per scan
		maxSummaries: 100,  // Keep up to 100 scan summaries
	}
}

// SetProgressBroadcaster sets the progress broadcaster for real-time updates
func (sl *ScanLogger) SetProgressBroadcaster(broadcaster *ProgressBroadcaster) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()
	sl.progressBroadcaster = broadcaster
}

// SetAutoScanService sets the auto-scan service for job status updates
func (sl *ScanLogger) SetAutoScanService(autoScanService *AutoScanService) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()
	sl.autoScanService = autoScanService
}

// StartScan initializes logging for a new scan operation
func (sl *ScanLogger) StartScan(scanID, repoID, groupName string, totalSources int) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := &models.ScanLogSummary{
		ScanID:         scanID,
		RepoID:         repoID,
		GroupName:      groupName,
		Status:         models.ScanStatusRunning,
		StartTime:      time.Now(),
		TotalSources:   totalSources,
		SourcesScanned: 0,
		ResultsFound:   0,
		ErrorCount:     0,
		WarningCount:   0,
		LastActivity:   time.Now(),
		SourceStatuses: make([]models.SourceScanStatus, 0),

		// Initialize enhanced progress tracking
		TotalFiles:        0, // Will be updated when file count is known
		ProcessedFiles:    0,
		ChunkedProcessing: false, // Will be set if chunked processing is enabled
		ChunkSize:         0,
		CurrentChunk:      0,
		TotalChunks:       0,
	}

	sl.summaries[scanID] = summary
	sl.logs[scanID] = make([]models.ScanLogEntry, 0)

	// Initialize progress broadcasting if available
	if sl.progressBroadcaster != nil {
		// Start progress broadcasting with default chunked config (disabled by default)
		chunkedConfig := &models.ChunkedProcessingConfig{
			Enabled:                false,
			ChunkSize:              100,
			MaxConcurrentChunks:    1,
			ProgressUpdateInterval: 10,
		}
		sl.progressBroadcaster.StartScan(scanID, repoID, groupName, 0, chunkedConfig)
	}

	// Log scan start
	sl.logEntry(scanID, models.ScanLogEntry{
		ID:        uuid.New().String(),
		ScanID:    scanID,
		RepoID:    repoID,
		GroupName: groupName,
		Timestamp: time.Now(),
		Level:     models.LogLevelInfo,
		Message:   fmt.Sprintf("Starting usage scan for group '%s' in repository '%s'", groupName, repoID),
		Step:      "initialization",
		Details:   fmt.Sprintf("Total sources to scan: %d", totalSources),
	})

	// Clean up old entries if needed
	sl.cleanupOldEntries()
}

// LogStep logs a step in the scan process (milestone steps only)
func (sl *ScanLogger) LogStep(scanID, step, message string, progress *models.Progress) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := sl.summaries[scanID]
	if summary == nil {
		return
	}

	// Check if this is a file progress step that should be broadcast even if not a milestone
	isFileProgress := step == "file_scan_progress" || step == "chunked_scan_progress"

	// Only log milestone steps to reduce log noise, but always broadcast file progress
	if !sl.isMilestoneStep(step, progress) && !isFileProgress {
		// Still update summary progress even if we don't log the step
		if progress != nil {
			summary.CompletedSteps = progress.Current
			summary.TotalSteps = progress.Total
			summary.LastActivity = time.Now()
		}
		return
	}

	entry := models.ScanLogEntry{
		ID:        uuid.New().String(),
		ScanID:    scanID,
		RepoID:    summary.RepoID,
		GroupName: summary.GroupName,
		Timestamp: time.Now(),
		Level:     models.LogLevelInfo,
		Message:   message,
		Step:      step,
		Progress:  progress,
	}

	sl.logEntry(scanID, entry)
	summary.LastActivity = time.Now()

	if progress != nil {
		summary.CompletedSteps = progress.Current
		summary.TotalSteps = progress.Total

		// Update summary with enhanced progress information
		if summary.CurrentProgress == nil {
			summary.CurrentProgress = progress
		} else {
			// Copy progress values to current progress
			summary.CurrentProgress.Current = progress.Current
			summary.CurrentProgress.Total = progress.Total
			summary.CurrentProgress.Percentage = progress.Percentage
			summary.CurrentProgress.Description = progress.Description

			// Copy enhanced progress fields if present
			if progress.CurrentFile != "" {
				summary.CurrentProgress.CurrentFile = progress.CurrentFile
			}
			if progress.CurrentChunk != 0 {
				summary.CurrentProgress.CurrentChunk = progress.CurrentChunk
			}
			if progress.TotalChunks != 0 {
				summary.CurrentProgress.TotalChunks = progress.TotalChunks
			}
			if progress.ChunkSize != 0 {
				summary.CurrentProgress.ChunkSize = progress.ChunkSize
			}
			if progress.FilesPerSecond != 0 {
				summary.CurrentProgress.FilesPerSecond = progress.FilesPerSecond
			}
			if progress.EstimatedTimeRemaining != nil {
				summary.CurrentProgress.EstimatedTimeRemaining = progress.EstimatedTimeRemaining
			}
			if progress.StartTime != nil {
				summary.CurrentProgress.StartTime = progress.StartTime
			}
			if progress.LastUpdateTime != nil {
				summary.CurrentProgress.LastUpdateTime = progress.LastUpdateTime
			}
		}

		// Update file processing counts if available
		if progress.Current > 0 && step == "file_scan_progress" {
			summary.ProcessedFiles = progress.Current
			if summary.TotalFiles == 0 {
				summary.TotalFiles = progress.Total
			}
		}
	}

	// Broadcast progress update via WebSocket if available
	if sl.progressBroadcaster != nil && progress != nil {
		// For file progress steps, send general progress update (not individual file updates)
		if step == "file_scan_progress" || step == "chunked_scan_progress" {
			update := models.RealTimeProgressUpdate{
				ScanID:    scanID,
				RepoID:    summary.RepoID,
				GroupName: summary.GroupName,
				Timestamp: time.Now(),
				EventType: "file_progress",
				Message:   message,
				Progress:  progress,
			}

			// Send to progress broadcaster's update channel for broadcasting
			select {
			case sl.progressBroadcaster.updateChannel <- update:
				// Successfully sent update
			default:
				// Channel is full, skip this update to avoid blocking
				log.Printf("Warning: Progress broadcaster channel is full, skipping file progress update for scan %s", scanID)
			}
		} else {
			// For other milestone steps, broadcast general progress updates
			eventType := "progress"
			switch step {
			case "init":
				eventType = "scan_init"
			case "initialization":
				eventType = "scan_start"
			case "completion":
				eventType = "scan_complete"
			case "source_scan":
				eventType = "progress"
			case "source_complete":
				eventType = "progress"
			case "scan_cancelled":
				eventType = "scan_error"
			case "scan_failed":
				eventType = "scan_error"
			}

			// Create and broadcast real-time progress update
			update := models.RealTimeProgressUpdate{
				ScanID:    scanID,
				RepoID:    summary.RepoID,
				GroupName: summary.GroupName,
				Timestamp: time.Now(),
				EventType: eventType,
				Message:   message,
				Progress:  progress,
			}

			// Send to progress broadcaster's update channel for broadcasting
			select {
			case sl.progressBroadcaster.updateChannel <- update:
				// Successfully sent update
			default:
				// Channel is full, skip this update to avoid blocking
				log.Printf("Warning: Progress broadcaster channel is full, skipping update for scan %s", scanID)
			}
		}
	}
}

// LogSourceStart logs the start of scanning a specific source
func (sl *ScanLogger) LogSourceStart(scanID, sourceID, sourceName, sourceType string) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := sl.summaries[scanID]
	if summary == nil {
		return
	}

	now := time.Now()
	sourceStatus := models.SourceScanStatus{
		SourceID:   sourceID,
		SourceName: sourceName,
		SourceType: sourceType,
		Status:     models.ScanStatusRunning,
		StartTime:  &now,
	}

	// Add or update source status
	found := false
	for i, status := range summary.SourceStatuses {
		if status.SourceID == sourceID {
			summary.SourceStatuses[i] = sourceStatus
			found = true
			break
		}
	}
	if !found {
		summary.SourceStatuses = append(summary.SourceStatuses, sourceStatus)
	}

	entry := models.ScanLogEntry{
		ID:         uuid.New().String(),
		ScanID:     scanID,
		RepoID:     summary.RepoID,
		GroupName:  summary.GroupName,
		Timestamp:  now,
		Level:      models.LogLevelInfo,
		Message:    fmt.Sprintf("Starting scan of source '%s' (%s)", sourceName, sourceType),
		Step:       "source_scan",
		SourceID:   sourceID,
		SourceName: sourceName,
	}

	sl.logEntry(scanID, entry)
	summary.LastActivity = now
}

// LogSourceComplete logs the completion of scanning a specific source
func (sl *ScanLogger) LogSourceComplete(scanID, sourceID string, resultCount int, duration time.Duration) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := sl.summaries[scanID]
	if summary == nil {
		return
	}

	now := time.Now()
	durationMs := duration.Milliseconds()

	// Update source status
	for i, status := range summary.SourceStatuses {
		if status.SourceID == sourceID {
			summary.SourceStatuses[i].Status = models.ScanStatusCompleted
			summary.SourceStatuses[i].EndTime = &now
			summary.SourceStatuses[i].Duration = &durationMs
			summary.SourceStatuses[i].ResultCount = resultCount
			break
		}
	}

	summary.SourcesScanned++
	summary.ResultsFound += resultCount
	summary.LastActivity = now

	var sourceName string
	for _, status := range summary.SourceStatuses {
		if status.SourceID == sourceID {
			sourceName = status.SourceName
			break
		}
	}

	entry := models.ScanLogEntry{
		ID:        uuid.New().String(),
		ScanID:    scanID,
		RepoID:    summary.RepoID,
		GroupName: summary.GroupName,
		Timestamp: now,
		Level:     models.LogLevelInfo,
		Message:   fmt.Sprintf("Completed scan of source '%s' - found %d results", sourceName, resultCount),
		Step:      "source_complete",
		SourceID:  sourceID,
		Duration:  &durationMs,
		Metadata:  map[string]interface{}{"resultCount": resultCount},
	}

	sl.logEntry(scanID, entry)
}

// LogError logs an error during the scan process
func (sl *ScanLogger) LogError(scanID, step, message, errorDetails string, sourceID ...string) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := sl.summaries[scanID]
	if summary == nil {
		return
	}

	var srcID string
	if len(sourceID) > 0 {
		srcID = sourceID[0]
		// Update source status if error is source-specific
		for i, status := range summary.SourceStatuses {
			if status.SourceID == srcID {
				summary.SourceStatuses[i].ErrorCount++
				summary.SourceStatuses[i].LastError = errorDetails
				break
			}
		}
	}

	entry := models.ScanLogEntry{
		ID:        uuid.New().String(),
		ScanID:    scanID,
		RepoID:    summary.RepoID,
		GroupName: summary.GroupName,
		Timestamp: time.Now(),
		Level:     models.LogLevelError,
		Message:   message,
		Step:      step,
		Error:     errorDetails,
		SourceID:  srcID,
	}

	sl.logEntry(scanID, entry)
	summary.ErrorCount++
	summary.LastActivity = time.Now()
}

// LogWarning logs a warning during the scan process
func (sl *ScanLogger) LogWarning(scanID, step, message, details string) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := sl.summaries[scanID]
	if summary == nil {
		return
	}

	entry := models.ScanLogEntry{
		ID:        uuid.New().String(),
		ScanID:    scanID,
		RepoID:    summary.RepoID,
		GroupName: summary.GroupName,
		Timestamp: time.Now(),
		Level:     models.LogLevelWarning,
		Message:   message,
		Step:      step,
		Details:   details,
	}

	sl.logEntry(scanID, entry)
	summary.WarningCount++
	summary.LastActivity = time.Now()

	// Report to progress broadcaster as non-fatal error if it's a connection-related warning
	if sl.progressBroadcaster != nil && (strings.Contains(strings.ToLower(message), "connection") ||
		strings.Contains(strings.ToLower(message), "auth") ||
		strings.Contains(strings.ToLower(details), "connect") ||
		strings.Contains(strings.ToLower(details), "timeout")) {
		sl.progressBroadcaster.ReportScanError(scanID, fmt.Sprintf("%s: %s", message, details), true)
	}
}

// CompleteScan marks a scan as completed and calculates final metrics
func (sl *ScanLogger) CompleteScan(scanID string, status models.ScanStatus) {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	summary := sl.summaries[scanID]
	if summary == nil {
		return
	}

	now := time.Now()
	summary.Status = status
	summary.EndTime = &now
	summary.LastActivity = now

	duration := now.Sub(summary.StartTime)
	durationMs := duration.Milliseconds()
	summary.Duration = &durationMs

	// Calculate performance metrics
	if summary.ResultsFound > 0 && durationMs > 0 {
		summary.Performance = &models.PerformanceMetrics{
			TotalProcessingTime: durationMs,
			ItemsPerSecond:      float64(summary.ResultsFound) / duration.Seconds(),
		}
	}

	var statusText string
	switch status {
	case models.ScanStatusCompleted:
		statusText = "completed successfully"
	case models.ScanStatusFailed:
		statusText = "failed"
	case models.ScanStatusCancelled:
		statusText = "was cancelled"
	default:
		statusText = "finished"
	}

	entry := models.ScanLogEntry{
		ID:        uuid.New().String(),
		ScanID:    scanID,
		RepoID:    summary.RepoID,
		GroupName: summary.GroupName,
		Timestamp: now,
		Level:     models.LogLevelInfo,
		Message:   fmt.Sprintf("Scan %s - found %d results in %v", statusText, summary.ResultsFound, duration.Round(time.Millisecond)),
		Step:      "completion",
		Duration:  &durationMs,
		Metadata: map[string]interface{}{
			"totalResults": summary.ResultsFound,
			"duration":     durationMs,
			"status":       status,
		},
	}

	sl.logEntry(scanID, entry)

	// Notify progress broadcaster about scan completion/failure
	if sl.progressBroadcaster != nil {
		switch status {
		case models.ScanStatusCompleted:
			sl.progressBroadcaster.CompleteScan(scanID, summary.ResultsFound)
		case models.ScanStatusFailed:
			errorMsg := fmt.Sprintf("Scan failed after processing %d files", summary.ProcessedFiles)
			sl.progressBroadcaster.FailScan(scanID, errorMsg)
		case models.ScanStatusCancelled:
			// For cancelled scans, we don't need to call FailScan as they weren't actual failures
			// The progress broadcaster will handle cleanup when the scan tracker is removed
		}
	}
}

// logEntry adds a log entry to the internal storage
func (sl *ScanLogger) logEntry(scanID string, entry models.ScanLogEntry) {
	logs := sl.logs[scanID]
	logs = append(logs, entry)

	// Keep only the most recent entries
	if len(logs) > sl.maxLogs {
		logs = logs[len(logs)-sl.maxLogs:]
	}

	sl.logs[scanID] = logs

	// Create a more descriptive log prefix using group name and repo ID
	// Extract group name and repo ID from scanID (format: GroupName:RepoID:timestamp:active)
	parts := strings.Split(scanID, ":")
	var logPrefix string
	if len(parts) >= 2 {
		groupName := parts[0]
		repoID := parts[1]
		// Include timestamp if available for better uniqueness in logs
		if len(parts) >= 3 {
			timestamp := parts[2]
			logPrefix = fmt.Sprintf("SCAN %s@%s[%s]", groupName, repoID, timestamp)
		} else {
			logPrefix = fmt.Sprintf("SCAN %s@%s", groupName, repoID)
		}
	} else {
		// Fallback to using the full scanID if format is unexpected
		logPrefix = fmt.Sprintf("SCAN %s", scanID)
	}

	log.Printf("[%s] %s: %s", logPrefix, strings.ToUpper(string(entry.Level)), entry.Message)
}

// cleanupOldEntries removes old scan data to prevent memory leaks
func (sl *ScanLogger) cleanupOldEntries() {
	if len(sl.summaries) <= sl.maxSummaries {
		return
	}

	// Sort summaries by start time and keep only the most recent ones
	type summaryWithTime struct {
		scanID    string
		startTime time.Time
	}

	var summariesList []summaryWithTime
	for scanID, summary := range sl.summaries {
		summariesList = append(summariesList, summaryWithTime{
			scanID:    scanID,
			startTime: summary.StartTime,
		})
	}

	sort.Slice(summariesList, func(i, j int) bool {
		return summariesList[i].startTime.After(summariesList[j].startTime)
	})

	// Remove oldest entries
	for i := sl.maxSummaries; i < len(summariesList); i++ {
		scanID := summariesList[i].scanID
		delete(sl.summaries, scanID)
		delete(sl.logs, scanID)
	}
}

// GetScanLogs retrieves scan logs with filtering and pagination
func (sl *ScanLogger) GetScanLogs(filter models.ScanLogFilter) models.ScanLogResponse {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	var allLogs []models.ScanLogEntry
	var summary *models.ScanLogSummary

	// If specific scan ID is requested, get logs for that scan
	if filter.ScanID != "" {
		if logs, exists := sl.logs[filter.ScanID]; exists {
			allLogs = append(allLogs, logs...)
		}
		if sum, exists := sl.summaries[filter.ScanID]; exists {
			summary = sum
		}
	} else {
		// Collect logs from all scans
		for scanID, logs := range sl.logs {
			// Apply filters
			if filter.RepoID != "" && sl.summaries[scanID] != nil && sl.summaries[scanID].RepoID != filter.RepoID {
				continue
			}
			if filter.GroupName != "" && sl.summaries[scanID] != nil && sl.summaries[scanID].GroupName != filter.GroupName {
				continue
			}
			allLogs = append(allLogs, logs...)
		}
	}

	// Apply additional filters
	filteredLogs := sl.applyFilters(allLogs, filter)

	// Sort by timestamp (newest first)
	sort.Slice(filteredLogs, func(i, j int) bool {
		return filteredLogs[i].Timestamp.After(filteredLogs[j].Timestamp)
	})

	// Apply pagination
	page := filter.Page
	if page < 1 {
		page = 1
	}
	pageSize := filter.PageSize
	if pageSize < 1 {
		pageSize = 50
	}

	total := len(filteredLogs)
	totalPages := (total + pageSize - 1) / pageSize

	start := (page - 1) * pageSize
	end := start + pageSize
	if end > total {
		end = total
	}

	var paginatedLogs []models.ScanLogEntry
	if start < total {
		paginatedLogs = filteredLogs[start:end]
	} else {
		paginatedLogs = make([]models.ScanLogEntry, 0)
	}

	return models.ScanLogResponse{
		Logs:       paginatedLogs,
		Summary:    summary,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
}

// GetScanSummary retrieves a single scan summary by scanID
func (sl *ScanLogger) GetScanSummary(scanID string) *models.ScanLogSummary {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	return sl.summaries[scanID]
}

// GetScanSummaries retrieves scan summaries with filtering
func (sl *ScanLogger) GetScanSummaries(filter models.ScanLogFilter) []models.ScanLogSummary {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	var summaries []models.ScanLogSummary

	for _, summary := range sl.summaries {
		// Apply filters
		if filter.RepoID != "" && summary.RepoID != filter.RepoID {
			continue
		}
		if filter.GroupName != "" && summary.GroupName != filter.GroupName {
			continue
		}
		if filter.Status != "" && string(summary.Status) != filter.Status {
			continue
		}
		if filter.StartTime != nil && summary.StartTime.Before(*filter.StartTime) {
			continue
		}
		if filter.EndTime != nil && summary.EndTime != nil && summary.EndTime.After(*filter.EndTime) {
			continue
		}

		summaries = append(summaries, *summary)
	}

	// Sort by start time (newest first)
	sort.Slice(summaries, func(i, j int) bool {
		return summaries[i].StartTime.After(summaries[j].StartTime)
	})

	return summaries
}

// GetScanOverview provides an overview of all scan activities
func (sl *ScanLogger) GetScanOverview() models.ScanLogsOverview {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	overview := models.ScanLogsOverview{
		RecentActivity: make([]models.ScanLogSummary, 0),
		TopErrors:      make([]models.ErrorSummary, 0),
	}

	var allSummaries []models.ScanLogSummary
	errorCounts := make(map[string]*models.ErrorSummary)

	for _, summary := range sl.summaries {
		allSummaries = append(allSummaries, *summary)

		overview.TotalScans++
		switch summary.Status {
		case models.ScanStatusRunning, models.ScanStatusQueued:
			overview.ActiveScans++
		case models.ScanStatusCompleted:
			overview.CompletedScans++
		case models.ScanStatusFailed:
			overview.FailedScans++
		}

		// Collect error information
		if logs, exists := sl.logs[summary.ScanID]; exists {
			for _, logEntry := range logs {
				if logEntry.Level == models.LogLevelError && logEntry.Error != "" {
					if errorSummary, exists := errorCounts[logEntry.Error]; exists {
						errorSummary.Count++
						if logEntry.Timestamp.After(errorSummary.LastOccured) {
							errorSummary.LastOccured = logEntry.Timestamp
						}
						// Add group if not already present
						found := false
						for _, group := range errorSummary.AffectedGroups {
							if group == logEntry.GroupName {
								found = true
								break
							}
						}
						if !found {
							errorSummary.AffectedGroups = append(errorSummary.AffectedGroups, logEntry.GroupName)
						}
					} else {
						errorCounts[logEntry.Error] = &models.ErrorSummary{
							Error:          logEntry.Error,
							Count:          1,
							LastOccured:    logEntry.Timestamp,
							AffectedGroups: []string{logEntry.GroupName},
						}
					}
				}
			}
		}
	}

	// Sort summaries by start time and take recent ones
	sort.Slice(allSummaries, func(i, j int) bool {
		return allSummaries[i].StartTime.After(allSummaries[j].StartTime)
	})

	// Take up to 10 most recent activities
	recentCount := 10
	if len(allSummaries) < recentCount {
		recentCount = len(allSummaries)
	}
	overview.RecentActivity = allSummaries[:recentCount]

	// Convert error map to slice and sort by count
	for _, errorSummary := range errorCounts {
		overview.TopErrors = append(overview.TopErrors, *errorSummary)
	}
	sort.Slice(overview.TopErrors, func(i, j int) bool {
		return overview.TopErrors[i].Count > overview.TopErrors[j].Count
	})

	// Take top 5 errors
	if len(overview.TopErrors) > 5 {
		overview.TopErrors = overview.TopErrors[:5]
	}

	// Calculate performance stats
	if overview.TotalScans > 0 {
		var totalDuration int64
		var totalResults int
		completedCount := 0

		for _, summary := range allSummaries {
			if summary.Duration != nil {
				totalDuration += *summary.Duration
				completedCount++
			}
			totalResults += summary.ResultsFound
		}

		if completedCount > 0 {
			overview.PerformanceStats = &models.OverallPerformance{
				AverageScanDuration:   totalDuration / int64(completedCount),
				SuccessRate:           float64(overview.CompletedScans) / float64(overview.TotalScans) * 100,
				TotalResultsFound:     totalResults,
				AverageResultsPerScan: totalResults / overview.TotalScans,
			}
		}
	}

	return overview
}

// applyFilters applies additional filters to log entries
func (sl *ScanLogger) applyFilters(logs []models.ScanLogEntry, filter models.ScanLogFilter) []models.ScanLogEntry {
	var filtered []models.ScanLogEntry

	for _, logEntry := range logs {
		// Apply level filter
		if filter.Level != "" && logEntry.Level != filter.Level {
			continue
		}

		// Apply source filter
		if filter.SourceID != "" && logEntry.SourceID != filter.SourceID {
			continue
		}

		// Apply time range filters
		if filter.StartTime != nil && logEntry.Timestamp.Before(*filter.StartTime) {
			continue
		}
		if filter.EndTime != nil && logEntry.Timestamp.After(*filter.EndTime) {
			continue
		}

		// Apply search query filter
		if filter.SearchQuery != "" {
			query := strings.ToLower(filter.SearchQuery)
			if !strings.Contains(strings.ToLower(logEntry.Message), query) &&
				!strings.Contains(strings.ToLower(logEntry.Details), query) &&
				!strings.Contains(strings.ToLower(logEntry.Error), query) {
				continue
			}
		}

		filtered = append(filtered, logEntry)
	}

	return filtered
}

// isMilestoneStep determines if a step should be logged based on milestone criteria
func (sl *ScanLogger) isMilestoneStep(step string, progress *models.Progress) bool {
	// Always log these milestone steps
	milestoneSteps := map[string]bool{
		"init":            true, // Job initialization and authentication preparation
		"initialization":  true, // Scan start
		"completion":      true, // Scan completion
		"source_scan":     true, // Source scan start
		"source_complete": true, // Source scan completion
		"git_clone":       true, // Git operations
		"git_pull":        true,
		"git_fallback":    true,
		"scan_cancelled":  true, // Cancellation events
		"scan_failed":     true, // Failure events
		"git_connection":  true, // Git connection tests
		"sync_start":      true, // Repository sync start
		"sync_complete":   true, // Repository sync completion
	}

	if milestoneSteps[step] {
		return true
	}

	// Log progress checkpoints at 25%, 50%, 75%, and 100%
	if progress != nil && progress.Total > 0 {
		percentage := int((float64(progress.Current) / float64(progress.Total)) * 100)
		if percentage == 25 || percentage == 50 || percentage == 75 || percentage == 100 {
			return true
		}
	}

	return false
}
