import { useEffect, useState } from "react"
import { useSearchParams, useNavigate } from "react-router-dom"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Database, AlertTriangle } from "lucide-react"
import RepositorySettings from "@/components/settings/RepositorySettings"
import SchedulerDashboard from "@/pages/Settings/SchedulerDashboard"
import UsageSourcesSettings from "@/components/settings/UsageSourcesSettings"

const Settings = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState("general")

  // Get tab from URL parameter
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab && ["general", "repository", "scheduler", "usage-sources", "storage"].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setSearchParams({ tab: value })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="repository">Repository Integration</TabsTrigger>
          <TabsTrigger value="scheduler">Scheduler Dashboard</TabsTrigger>
          <TabsTrigger value="usage-sources">Usage Sources</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure general application settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="appName">Application Name</Label>
                  <Input id="appName" defaultValue="ADGitOps UI" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dataDirectory">Data Directory</Label>
                  <Input id="dataDirectory" defaultValue="./data" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="repository">
          <RepositorySettings />
        </TabsContent>

        <TabsContent value="scheduler">
          <SchedulerDashboard />
        </TabsContent>

        <TabsContent value="usage-sources">
          <UsageSourcesSettings />
        </TabsContent>

        {/* Export settings tab removed as it's now covered by the Report feature */}
      </Tabs>
    </div>
  )
}

export default Settings
