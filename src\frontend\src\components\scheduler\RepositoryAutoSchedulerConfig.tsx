import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Clock, Settings, Target, AlertTriangle, CheckCircle, Plus, Trash2 } from 'lucide-react';
import { apiClient as api } from '@/api/client';
import type { PriorityRule, PriorityRuleOperator } from '@/types/autoScan';

interface RepositoryAutoSchedulerConfig {
  id?: string;
  repoId: string;
  repositoryName?: string;
  enabled: boolean;
  timeWindow: {
    startHour: number;
    endHour: number;
    timezone: string;
  };
  minimumIntervalMinutes: number; // Repository-specific minimum interval override
  priorityConfig: {
    enabled: boolean;
    defaultWeight: number;
    rules: PriorityRule[];
    scheduleByWeight: boolean;
  };
  createdAt?: string;
  updatedAt?: string;
}

interface RepositoryAutoSchedulerConfigProps {
  isOpen: boolean;
  onClose: () => void;
  repoId?: string;
  repositoryName?: string;
  onConfigSaved: () => void;
}

const DEFAULT_CONFIG: Partial<RepositoryAutoSchedulerConfig> = {
  enabled: true,
  timeWindow: {
    startHour: 2,
    endHour: 6,
    timezone: 'UTC',
  },
  minimumIntervalMinutes: 60, // Default 1 hour minimum interval
  priorityConfig: {
    enabled: false,
    defaultWeight: 50,
    scheduleByWeight: true,
    rules: [],
  },
};

export function RepositoryAutoSchedulerConfig({
  isOpen,
  onClose,
  repoId,
  repositoryName,
  onConfigSaved
}: RepositoryAutoSchedulerConfigProps) {
  const [config, setConfig] = useState<RepositoryAutoSchedulerConfig>({
    repoId: repoId || '',
    repositoryName: repositoryName || '',
    ...DEFAULT_CONFIG
  } as RepositoryAutoSchedulerConfig);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Load existing configuration
  useEffect(() => {
    if (isOpen && repoId) {
      loadConfig();
    }
  }, [isOpen, repoId]);

  const loadConfig = async () => {
    if (!repoId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.scheduler.getAutoScanConfig(repoId);
      if (response.config) {
        // Parse the minimum interval from the loadBalancing.minIntervalBetween field
        let minimumIntervalMinutes = 60; // Default
        if (response.config.loadBalancing?.minIntervalBetween) {
          const intervalStr = response.config.loadBalancing.minIntervalBetween;
          // Parse duration strings like "30m", "1h", "2h30m"
          const match = intervalStr.match(/^(\d+)([mh])$/);
          if (match) {
            const value = parseInt(match[1]);
            const unit = match[2];
            minimumIntervalMinutes = unit === 'h' ? value * 60 : value;
          }
        }

        // Convert existing config to new format
        const loadedConfig: RepositoryAutoSchedulerConfig = {
          id: response.config.id,
          repoId: response.config.repoId,
          repositoryName: response.config.repositoryName,
          enabled: response.config.enabled,
          timeWindow: response.config.timeWindow,
          minimumIntervalMinutes: minimumIntervalMinutes,
          priorityConfig: response.config.priorityConfig || DEFAULT_CONFIG.priorityConfig!,
          createdAt: response.config.createdAt,
          updatedAt: response.config.updatedAt,
        };
        setConfig(loadedConfig);
        setIsEditing(true);
      } else {
        // No existing config, use defaults
        setConfig({
          repoId: repoId,
          repositoryName: repositoryName || '',
          ...DEFAULT_CONFIG
        } as RepositoryAutoSchedulerConfig);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Failed to load config:', error);
      setError('Failed to load repository configuration');
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Convert back to the expected API format
      const apiConfig = {
        ...config,
        // Add any required fields that the API expects
        frequency: 'daily' as const,
        maxConcurrentScans: 3,
        scanAllGroups: true,
        targetGroups: [],
        loadBalancing: {
          spreadAcrossDay: false,
          minIntervalBetween: `${config.minimumIntervalMinutes}m`,
          maxScansPerHour: 10,
        },
        notificationConfig: {
          onCompletion: false,
          onFailure: true,
          onSummary: false,
        },
      };

      if (isEditing) {
        await api.scheduler.updateAutoScanConfig(config.repoId, apiConfig);
        setSuccess('Repository configuration updated successfully');
      } else {
        await api.scheduler.createAutoScanConfig(apiConfig);
        setSuccess('Repository configuration created successfully');
        setIsEditing(true);
      }

      onConfigSaved();
    } catch (error) {
      console.error('Failed to save config:', error);
      setError('Failed to save repository configuration');
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current: any = newConfig;

      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold">
                {isEditing ? 'Edit' : 'Create'} Auto-Scheduler Configuration
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Repository: <span className="font-medium">{repositoryName}</span>
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Configure repository-specific settings for the continuous auto-scheduler
              </p>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading configuration...</p>
              </div>
            </div>
          ) : (
            <>
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Basic Settings</TabsTrigger>
                  <TabsTrigger value="schedule">Schedule Settings</TabsTrigger>
                  <TabsTrigger value="priority">Priority Rules</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Repository Configuration</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={config.enabled}
                          onCheckedChange={(checked) => updateConfig('enabled', checked)}
                        />
                        <Label>Enable Auto-Scheduler for this Repository</Label>
                      </div>

                      <div className="space-y-2">
                        <Label>Minimum Interval Between Scans (minutes)</Label>
                        <Input
                          type="number"
                          min="30"
                          max="1440"
                          value={config.minimumIntervalMinutes}
                          onChange={(e) => updateConfig('minimumIntervalMinutes', parseInt(e.target.value) || 60)}
                        />
                        <p className="text-xs text-gray-500">
                          Repository-specific override for minimum time between scans (30 minutes to 24 hours)
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="schedule" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>Time Window</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <Label>Start Hour (24h format)</Label>
                          <Input
                            type="number"
                            min="0"
                            max="23"
                            value={config.timeWindow.startHour}
                            onChange={(e) => updateConfig('timeWindow.startHour', parseInt(e.target.value) || 0)}
                          />
                        </div>
                        <div>
                          <Label>End Hour (24h format)</Label>
                          <Input
                            type="number"
                            min="0"
                            max="23"
                            value={config.timeWindow.endHour}
                            onChange={(e) => updateConfig('timeWindow.endHour', parseInt(e.target.value) || 6)}
                          />
                        </div>
                        <div>
                          <Label>Timezone</Label>
                          <Select
                            value={config.timeWindow.timezone}
                            onValueChange={(value) => updateConfig('timeWindow.timezone', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="UTC">UTC</SelectItem>
                              <SelectItem value="America/New_York">Eastern Time</SelectItem>
                              <SelectItem value="America/Chicago">Central Time</SelectItem>
                              <SelectItem value="America/Denver">Mountain Time</SelectItem>
                              <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500">
                        Scans will only be scheduled within this time window
                      </p>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="priority" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center space-x-2">
                        <Target className="h-4 w-4" />
                        <span>Priority-Based Scheduling</span>
                      </CardTitle>
                      <p className="text-xs text-muted-foreground">
                        Configure priority rules to control the order in which groups are scanned
                      </p>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={config.priorityConfig?.enabled || false}
                          onCheckedChange={(checked) =>
                            setConfig(prev => ({
                              ...prev,
                              priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), enabled: checked }
                            }))
                          }
                        />
                        <Label className="text-sm">Enable priority-based scheduling</Label>
                      </div>

                      {config.priorityConfig?.enabled && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-xs">Default Weight</Label>
                              <Input
                                type="number"
                                min="1"
                                max="100"
                                value={config.priorityConfig?.defaultWeight || 50}
                                onChange={(e) =>
                                  setConfig(prev => ({
                                    ...prev,
                                    priorityConfig: {
                                      ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!),
                                      defaultWeight: parseInt(e.target.value) || 50
                                    }
                                  }))
                                }
                                className="text-xs"
                              />
                              <p className="text-xs text-muted-foreground">
                                Weight for groups that don't match any rule
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={config.priorityConfig?.scheduleByWeight || true}
                                onCheckedChange={(checked) =>
                                  setConfig(prev => ({
                                    ...prev,
                                    priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), scheduleByWeight: checked }
                                  }))
                                }
                              />
                              <Label className="text-xs">Higher weight = higher priority</Label>
                            </div>
                          </div>

                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <Label className="text-sm font-medium">Priority Rules</Label>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const newRule: PriorityRule = {
                                    id: `rule-${Date.now()}`,
                                    name: 'New Rule',
                                    pattern: '*',
                                    weight: 50,
                                    description: '',
                                    enabled: true,
                                    operator: 'include' as PriorityRuleOperator,
                                  };
                                  setConfig(prev => ({
                                    ...prev,
                                    priorityConfig: {
                                      ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!),
                                      rules: [...(prev.priorityConfig?.rules || []), newRule]
                                    }
                                  }));
                                }}
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                Add Rule
                              </Button>
                            </div>

                            <div className="space-y-2 max-h-64 overflow-y-auto">
                              {(config.priorityConfig?.rules || []).map((rule, index) => (
                                <Card key={rule.id} className="p-3">
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Switch
                                          checked={rule.enabled}
                                          onCheckedChange={(checked) => {
                                            const newRules = [...(config.priorityConfig?.rules || [])];
                                            newRules[index] = { ...rule, enabled: checked };
                                            setConfig(prev => ({
                                              ...prev,
                                              priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                            }));
                                          }}
                                        />
                                        <Input
                                          value={rule.name}
                                          onChange={(e) => {
                                            const newRules = [...(config.priorityConfig?.rules || [])];
                                            newRules[index] = { ...rule, name: e.target.value };
                                            setConfig(prev => ({
                                              ...prev,
                                              priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                            }));
                                          }}
                                          className="text-xs font-medium"
                                          placeholder="Rule name"
                                        />
                                      </div>
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          const newRules = (config.priorityConfig?.rules || []).filter((_, i) => i !== index);
                                          setConfig(prev => ({
                                            ...prev,
                                            priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                          }));
                                        }}
                                      >
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                    </div>

                                    <div className="grid grid-cols-3 gap-2">
                                      <div>
                                        <Label className="text-xs">Pattern</Label>
                                        <Input
                                          value={rule.pattern}
                                          onChange={(e) => {
                                            const newRules = [...(config.priorityConfig?.rules || [])];
                                            newRules[index] = { ...rule, pattern: e.target.value };
                                            setConfig(prev => ({
                                              ...prev,
                                              priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                            }));
                                          }}
                                          className="text-xs"
                                          placeholder="e.g., *Admin*"
                                        />
                                      </div>
                                      <div>
                                        <Label className="text-xs">Weight</Label>
                                        <Input
                                          type="number"
                                          min="1"
                                          max="100"
                                          value={rule.weight}
                                          onChange={(e) => {
                                            const newRules = [...(config.priorityConfig?.rules || [])];
                                            newRules[index] = { ...rule, weight: parseInt(e.target.value) || 50 };
                                            setConfig(prev => ({
                                              ...prev,
                                              priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                            }));
                                          }}
                                          className="text-xs"
                                        />
                                      </div>
                                      <div>
                                        <Label className="text-xs">Operator</Label>
                                        <Select
                                          value={rule.operator || 'include'}
                                          onValueChange={(value: PriorityRuleOperator) => {
                                            const newRules = [...(config.priorityConfig?.rules || [])];
                                            newRules[index] = { ...rule, operator: value };
                                            setConfig(prev => ({
                                              ...prev,
                                              priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                            }));
                                          }}
                                        >
                                          <SelectTrigger className="text-xs">
                                            <SelectValue />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="include">Include</SelectItem>
                                            <SelectItem value="exclude">Exclude</SelectItem>
                                            <SelectItem value="exclusive">Exclusive</SelectItem>
                                            <SelectItem value="required">Required</SelectItem>
                                          </SelectContent>
                                        </Select>
                                      </div>
                                    </div>

                                    <Input
                                      value={rule.description}
                                      onChange={(e) => {
                                        const newRules = [...(config.priorityConfig?.rules || [])];
                                        newRules[index] = { ...rule, description: e.target.value };
                                        setConfig(prev => ({
                                          ...prev,
                                          priorityConfig: { ...(prev.priorityConfig || DEFAULT_CONFIG.priorityConfig!), rules: newRules }
                                        }));
                                      }}
                                      className="text-xs"
                                      placeholder="Optional description"
                                    />
                                  </div>
                                </Card>
                              ))}
                            </div>

                            {(config.priorityConfig?.rules || []).length === 0 && (
                              <div className="text-center py-4 text-muted-foreground">
                                <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <p className="text-sm">No priority rules defined</p>
                                <p className="text-xs">Add rules to control scan scheduling order</p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              {/* Error/Success Messages */}
              {error && (
                <Alert className="border-red-200 bg-red-50 mt-4">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50 mt-4">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">{success}</AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 mt-6 pt-4 border-t">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={saveConfig} disabled={saving}>
                  {saving ? 'Saving...' : (isEditing ? 'Update Configuration' : 'Create Configuration')}
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
