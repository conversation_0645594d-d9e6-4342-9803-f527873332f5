package controllers

import (
	"fmt"
	"log"
	"net/http"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/repository"
	"adgitops-ui/src/backend/services"

	"github.com/gin-gonic/gin"
)

// RepositoryController handles Git repository-related API endpoints
type RepositoryController struct {
	repoManager *repository.RepositoryManager
	syncLogger  *services.SyncLogger
}

// NewRepositoryController creates a new repository controller
func NewRepositoryController(repoManager *repository.RepositoryManager, syncLogger *services.SyncLogger) *RepositoryController {
	return &RepositoryController{
		repoManager: repoManager,
		syncLogger:  syncLogger,
	}
}

// RegisterRoutes registers the controller's routes
func (c *RepositoryController) RegisterRoutes(router *gin.RouterGroup) {
	// New type-agnostic routes under /repo
	repoRoutes := router.Group("/repo")
	{
		// Repository configuration management
		repoRoutes.GET("/configurations", c.getConfigurations)
		repoRoutes.GET("/configurations/:id", c.getConfiguration)
		repoRoutes.POST("/configurations", c.addConfiguration)
		repoRoutes.PUT("/configurations/:id", c.updateConfiguration)
		repoRoutes.DELETE("/configurations/:id", c.deleteConfiguration)

		// Repository status and operations
		repoRoutes.GET("/status", c.getAllStatuses)
		repoRoutes.GET("/status/:id", c.getRepositoryStatus)
		repoRoutes.POST("/sync/:id", c.syncRepository)
		repoRoutes.POST("/polling/start/:id", c.startPolling)
		repoRoutes.POST("/polling/stop/:id", c.stopPolling)

		// Sync logs
		repoRoutes.GET("/sync-logs/:id", c.getSyncLogs)

		// Repository-specific routes with ID parameter
		// These will be registered in main.go to forward to the data controller
	}
}

// getConfigurations returns all repository configurations
func (c *RepositoryController) getConfigurations(ctx *gin.Context) {
	configs, err := c.repoManager.GetConfigurations()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get configurations: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, configs)
}

// getConfiguration returns a specific repository configuration
func (c *RepositoryController) getConfiguration(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	config, err := c.repoManager.GetConfiguration(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"config": config,
	})
}

// addConfiguration adds a new repository configuration
func (c *RepositoryController) addConfiguration(ctx *gin.Context) {
	var config models.RepositoryConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid configuration format: " + err.Error(),
		})
		return
	}

	// Validate configuration
	if config.URL == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "URL is required",
		})
		return
	}

	// Set default repository type if not specified
	if config.Type == "" {
		config.Type = models.GitLab // Default to GitLab for backward compatibility
	}

	// Validate repository-specific fields
	if config.Type == models.Bitbucket {
		if config.BitbucketWorkspace == "" || config.BitbucketRepoSlug == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Bitbucket workspace and repository slug are required",
			})
			return
		}

		// For Bitbucket, either token or username/password is required
		if config.Token == "" && (config.BitbucketUsername == "" || config.BitbucketPassword == "") {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Either token or username/password is required for Bitbucket",
			})
			return
		}
	} else if config.Type == models.GitLab {
		if config.Token == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Token is required for GitLab",
			})
			return
		}

		if config.ProjectID == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Project ID is required for GitLab",
			})
			return
		}
	}

	// Set default values if not provided
	if config.PollFrequency <= 0 {
		config.PollFrequency = 60 // Default to 60 seconds
	} else if config.PollFrequency < 30 {
		config.PollFrequency = 30 // Minimum 30 seconds to prevent overloading
	}

	if config.Name == "" {
		// Use project ID or repo slug as default name
		if config.Type == models.GitLab {
			config.Name = "GitLab: " + config.ProjectID
		} else {
			config.Name = "Bitbucket: " + config.BitbucketRepoSlug
		}
	}

	if err := c.repoManager.AddConfiguration(config); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to add configuration: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"message": "Configuration added successfully",
		"config":  config,
	})
}

// updateConfiguration updates an existing repository configuration
func (c *RepositoryController) updateConfiguration(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	var config models.RepositoryConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid configuration format: " + err.Error(),
		})
		return
	}

	// Ensure ID in the path matches the ID in the config
	config.ID = id

	// Validate configuration
	if config.URL == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "URL is required",
		})
		return
	}

	// Validate repository-specific fields
	if config.Type == models.Bitbucket {
		if config.BitbucketWorkspace == "" || config.BitbucketRepoSlug == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Bitbucket workspace and repository slug are required",
			})
			return
		}

		// For Bitbucket, either token or username/password is required
		if config.Token == "" && (config.BitbucketUsername == "" || config.BitbucketPassword == "") {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Either token or username/password is required for Bitbucket",
			})
			return
		}
	} else if config.Type == models.GitLab {
		if config.Token == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Token is required for GitLab",
			})
			return
		}

		if config.ProjectID == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Project ID is required for GitLab",
			})
			return
		}
	}

	// Set default values if not provided
	if config.PollFrequency <= 0 {
		config.PollFrequency = 60 // Default to 60 seconds
	} else if config.PollFrequency < 30 {
		config.PollFrequency = 30 // Minimum 30 seconds to prevent overloading
	}

	if config.Name == "" {
		// Use project ID or repo slug as default name
		if config.Type == models.GitLab {
			config.Name = "GitLab: " + config.ProjectID
		} else {
			config.Name = "Bitbucket: " + config.BitbucketRepoSlug
		}
	}

	if err := c.repoManager.UpdateConfiguration(config); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update configuration: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Configuration updated successfully",
		"config":  config,
	})
}

// deleteConfiguration deletes a repository configuration
func (c *RepositoryController) deleteConfiguration(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	if err := c.repoManager.DeleteConfiguration(id); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete configuration: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Configuration deleted successfully",
	})
}

// getAllStatuses returns the status of all repositories
func (c *RepositoryController) getAllStatuses(ctx *gin.Context) {
	statuses := c.repoManager.GetAllRepositoryStatuses()
	ctx.JSON(http.StatusOK, gin.H{
		"statuses": statuses,
	})
}

// getRepositoryStatus returns the status of a specific repository
func (c *RepositoryController) getRepositoryStatus(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	status, err := c.repoManager.GetRepositoryStatus(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, status)
}

// syncRepository triggers a manual sync of a specific repository
func (c *RepositoryController) syncRepository(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Trigger sync in a separate goroutine to avoid blocking the HTTP response
	go func() {
		if err := repo.SyncRepository(); err != nil {
			// Log error but don't affect HTTP response
			// since we're in a separate goroutine
			log.Printf("Error syncing repository %s: %v", id, err)
		} else {
			log.Printf("Successfully synced repository %s", id)

			// Manually trigger reindexing after successful sync
			log.Printf("Manually triggering reindexing for repository %s", id)

			// Make a direct API call to reindex
			reindexURL := fmt.Sprintf("http://localhost:8080/api/%s/search/reindex", id)
			resp, err := http.Post(reindexURL, "application/json", nil)
			if err != nil {
				log.Printf("Error triggering reindex for repository %s: %v", id, err)
			} else {
				defer resp.Body.Close()
				log.Printf("Reindex triggered for repository %s with status: %d", id, resp.StatusCode)
			}
		}
	}()

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Repository synchronization has been initiated",
	})
}

// startPolling starts the periodic polling for a specific repository
func (c *RepositoryController) startPolling(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Start polling
	if err := repo.StartPolling(); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to start polling: " + err.Error(),
		})
		return
	}

	// Update the configuration to mark as active
	configs, err := c.repoManager.GetConfigurations()
	if err == nil {
		for i, config := range configs.Configs {
			if config.ID == id {
				configs.Configs[i].IsActive = true
				c.repoManager.SaveConfigurations(configs)
				break
			}
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Polling started successfully",
	})
}

// stopPolling stops the periodic polling for a specific repository
func (c *RepositoryController) stopPolling(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// Get the repository
	repo, err := c.repoManager.GetRepository(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Stop polling
	repo.StopPolling()

	// Update the configuration to mark as inactive
	configs, err := c.repoManager.GetConfigurations()
	if err == nil {
		for i, config := range configs.Configs {
			if config.ID == id {
				configs.Configs[i].IsActive = false
				c.repoManager.SaveConfigurations(configs)
				break
			}
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Polling stopped successfully",
	})
}

// getSyncLogs returns the sync logs for a specific repository
func (c *RepositoryController) getSyncLogs(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Repository ID is required",
		})
		return
	}

	// Check if repository exists
	_, err := c.repoManager.GetRepository(id)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"error": "Repository not found: " + err.Error(),
		})
		return
	}

	// Get logs for the repository
	logs := c.syncLogger.GetLogs(id)

	ctx.JSON(http.StatusOK, models.SyncLogResponse{
		Logs: logs,
	})
}
